// Initialize variables
let currentCustomerId = null;
let currentPage = 1;
let addCustomerModal = null;

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Event listeners for filters
    document.getElementById('searchTermInput')?.addEventListener('input', debounceSearch);
    document.getElementById('filterStatus')?.addEventListener('change', () => fetchCustomers(1));
    document.getElementById('filterSpending')?.addEventListener('change', () => fetchCustomers(1));
    document.getElementById('filterSort')?.addEventListener('change', () => fetchCustomers(1));

    // Initial fetch
    fetchCustomers(1);
});

// Search debouncing
let searchTimeout = null;
function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => fetchCustomers(1), 300);
}

// Main data fetching function
function fetchCustomers(page = 1) {
    currentPage = page;
    const searchTerm = document.getElementById('searchTermInput')?.value || '';
    const status = document.getElementById('filterStatus')?.value || 'all';
    const spending = document.getElementById('filterSpending')?.value || 'all';
    const sort = document.getElementById('filterSort')?.value || 'recent';

    fetch(`/shopify/api/list?page=${page}&search=${encodeURIComponent(searchTerm)}&status=${status}&spending=${spending}&sort=${sort}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error fetching customers: ' + data.error);
                return;
            }

            // Update stats
            document.getElementById('statTotalCustomers').textContent = data.total_customers || 0;
            document.getElementById('statStoreCredit').textContent = `$${(data.total_store_credit || 0).toFixed(2)}`;
            document.getElementById('statAverageOrder').textContent = `$${(data.average_order_value || 0).toFixed(2)}`;
            document.getElementById('statRetention').textContent = `${((data.retention_rate || 0) * 100).toFixed(1)}%`;

            // Update customer table
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = '';

            data.customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.id}</td>
                    <td>${customer.first_name} ${customer.last_name}</td>
                    <td>${customer.orders_count || 0}</td>
                    <td>$${(customer.total_spent || 0).toFixed(2)}</td>
                    <td>${customer.last_order_date ? new Date(customer.last_order_date).toLocaleDateString() : 'Never'}</td>
                    <td>${customer.store_credit_balance ? '$' + parseFloat(customer.store_credit_balance).toFixed(2) : '$0.00'}</td>
                    <td><span class="badge bg-${getStatusBadgeColor(customer.status)}">${customer.status || 'Active'}</span></td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-info btn-sm" onclick="showCustomerDetails('${customer.id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="showEditCustomer('${customer.id}')">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteCustomer('${customer.id}')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update pagination
            updatePagination(data.total_pages, page);
        })
        .catch(error => {
            console.error('Error fetching customers:', error);
            alert('An error occurred while fetching customers');
        });
}

function updatePagination(totalPages, currentPage) {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="return fetchCustomers(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </a>
    `;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || // First page
            i === totalPages || // Last page
            (i >= currentPage - 2 && i <= currentPage + 2) // Pages around current
        ) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `
                <a class="page-link" href="#" onclick="return fetchCustomers(${i})">${i}</a>
            `;
            pagination.appendChild(li);
        } else if (
            i === currentPage - 3 ||
            i === currentPage + 3
        ) {
            // Add ellipsis
            const li = document.createElement('li');
            li.className = 'page-item disabled';
            li.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(li);
        }
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="return fetchCustomers(${currentPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </a>
    `;
    pagination.appendChild(nextLi);
}

function getStatusBadgeColor(status) {
    const colors = {
        'active': 'success',
        'dormant': 'warning',
        'new': 'info',
        'blocked': 'danger'
    };
    return colors[status?.toLowerCase()] || 'secondary';
}

// Add Customer Functions
function saveNewCustomer() {
    const data = {
        first_name: document.getElementById('newFirstName').value,
        last_name: document.getElementById('newLastName').value,
        email: document.getElementById('newEmail').value,
        phone: document.getElementById('newPhone').value,
        customer_group: document.getElementById('newCustomerGroup').value,
        tags: document.getElementById('newTags').value,
        accepts_marketing: document.getElementById('newAcceptMarketing').checked
    };

    fetch('/shopify/api/customers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error creating customer: ' + data.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();
            fetchCustomers(currentPage);
            alert('Customer created successfully');
        }
    })
    .catch(error => {
        console.error('Error creating customer:', error);
        alert('An error occurred while creating the customer');
    });
}

// Customer Management Functions
function showAddCustomerModal() {
    if (!addCustomerModal) {
        addCustomerModal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
    }
    // Reset form
    document.getElementById('newCustomerForm')?.reset();
    addCustomerModal.show();
}

function showCustomerDetails(customerId) {
    fetch(`/shopify/api/customer/${customerId}/details`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            currentCustomerId = customerId;

            // Check if customer data exists before updating
            if (!data.customer) {
                throw new Error('Customer data not found in response');
            }

            updateCustomerDetails(data);
            new bootstrap.Modal(document.getElementById('customerViewModal')).show();
        })
        .catch(error => {
            console.error('Error loading customer details:', error);
            alert(`Error loading customer details: ${error.message}`);
        });
}

function showEditCustomer() {
    fetch(`/shopify/api/customer/${currentCustomerId}/details`)
        .then(response => response.json())
        .then(data => {
            // Populate edit form with customer data
            const customer = data.customer;
            document.getElementById('editFirstName').value = customer.first_name;
            document.getElementById('editLastName').value = customer.last_name;
            document.getElementById('editEmail').value = customer.email;
            document.getElementById('editPhone').value = customer.phone || '';

            // Show edit modal
            new bootstrap.Modal(document.getElementById('editCustomerModal')).show();
        })
        .catch(error => {
            console.error('Error loading customer details for edit:', error);
            alert('Error loading customer details');
        });
}

function saveCustomerChanges() {
    const data = {
        first_name: document.getElementById('editFirstName').value,
        last_name: document.getElementById('editLastName').value,
        email: document.getElementById('editEmail').value,
        phone: document.getElementById('editPhone').value
    };

    fetch(`/shopify/api/customer/${currentCustomerId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error updating customer: ' + data.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('editCustomerModal')).hide();
            refreshCustomerView();
            alert('Customer updated successfully');
        }
    })
    .catch(error => {
        console.error('Error updating customer:', error);
        alert('An error occurred while updating the customer');
    });
}

function deleteCustomer(customerId) {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
        return;
    }

    fetch(`/shopify/api/customer/${customerId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error deleting customer: ' + data.error);
        } else {
            alert('Customer deleted successfully');
            fetchCustomers(currentPage);
        }
    })
    .catch(error => {
        console.error('Error deleting customer:', error);
        alert('An error occurred while deleting the customer');
    });
}

// Credit Management Functions
function showAddCredit() {
    document.getElementById('creditActionType').value = 'add';
    document.getElementById('creditModalTitle').textContent = 'Add Store Credit';
    document.getElementById('creditAmount').value = '';
    document.getElementById('creditReason').value = 'refund';
    document.getElementById('creditNote').value = '';
    document.getElementById('creditNotifyCustomer').checked = true;
    new bootstrap.Modal(document.getElementById('creditModal')).show();
}

function showDeductCredit() {
    document.getElementById('creditActionType').value = 'deduct';
    document.getElementById('creditModalTitle').textContent = 'Deduct Store Credit';
    document.getElementById('creditAmount').value = '';
    document.getElementById('creditReason').value = 'adjustment';
    document.getElementById('creditNote').value = '';
    document.getElementById('creditNotifyCustomer').checked = true;
    new bootstrap.Modal(document.getElementById('creditModal')).show();
}

function showAdjustCredit() {
    document.getElementById('creditActionType').value = 'adjust';
    document.getElementById('creditModalTitle').textContent = 'Adjust Store Credit';
    document.getElementById('creditAmount').value = document.getElementById('currentStoreCredit').textContent.replace('$', '');
    document.getElementById('creditReason').value = 'adjustment';
    document.getElementById('creditNote').value = '';
    document.getElementById('creditNotifyCustomer').checked = true;
    new bootstrap.Modal(document.getElementById('creditModal')).show();
}

function submitCreditChange() {
    const actionType = document.getElementById('creditActionType').value;
    const amount = parseFloat(document.getElementById('creditAmount').value);
    const reason = document.getElementById('creditReason').value;
    const note = document.getElementById('creditNote').value;
    const notify = document.getElementById('creditNotifyCustomer').checked;

    if (!amount || isNaN(amount)) {
        alert('Please enter a valid amount');
        return;
    }

    fetch(`/shopify/api/customer/${currentCustomerId}/credit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: actionType,
            amount: amount,
            reason: reason,
            note: note,
            notify: notify
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error updating credit: ' + data.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('creditModal')).hide();
            refreshCustomerView();
            alert('Store credit updated successfully');
        }
    })
    .catch(error => {
        console.error('Error updating credit:', error);
        alert('An error occurred while updating the store credit');
    });
}

function refreshCustomerView() {
    // Refresh customer details
    fetch(`/shopify/api/customer/${currentCustomerId}/details`)
        .then(response => response.json())
        .then(data => {
            updateCustomerDetails(data);
        })
        .catch(error => {
            console.error('Error refreshing customer details:', error);
        });

    // Refresh credit history
    fetch(`/shopify/api/customer/${currentCustomerId}/credit-history`)
        .then(response => response.json())
        .then(data => {
            updateCreditHistory(data.transactions);
        })
        .catch(error => {
            console.error('Error refreshing credit history:', error);
        });
}

function updateCustomerDetails(data) {
    try {
        const customer = data.customer;

        // Safely get customer properties with fallbacks
        const firstName = customer.first_name || '';
        const lastName = customer.last_name || '';
        const email = customer.email || 'No email';
        const ordersCount = customer.orders_count || 0;
        const totalSpent = parseFloat(customer.total_spent || 0).toFixed(2);

        // Update UI elements
        document.getElementById('viewCustomerName').textContent = `${firstName} ${lastName}`;
        document.getElementById('viewCustomerEmail').textContent = email;
        document.getElementById('viewCustomerOrders').textContent = ordersCount;
        document.getElementById('viewCustomerSpent').textContent = `$${totalSpent}`;

        // Handle store credit balance
        let storeCreditBalance = 'N/A';
        if (customer.store_credit_balance !== undefined && customer.store_credit_balance !== 'N/A') {
            storeCreditBalance = `$${parseFloat(customer.store_credit_balance).toFixed(2)}`;
        }
        document.getElementById('viewCustomerBalance').textContent = storeCreditBalance;

        // Set current store credit for adjustment
        document.getElementById('currentStoreCredit').textContent =
            customer.store_credit_balance !== undefined && customer.store_credit_balance !== 'N/A' ?
            `$${parseFloat(customer.store_credit_balance).toFixed(2)}` : '$0.00';

        // Format last update date
        let lastUpdate = 'Never';
        if (customer.updated_at) {
            try {
                lastUpdate = new Date(customer.updated_at).toLocaleDateString();
            } catch (e) {
                console.warn('Error formatting date:', e);
            }
        }
        document.getElementById('lastCreditUpdate').textContent = lastUpdate;
    } catch (error) {
        console.error('Error updating customer details:', error);
        alert('Error displaying customer information. Please try again.');
    }
}

function updateCreditHistory(transactions) {
    const tbody = document.getElementById('creditHistoryTable');
    tbody.innerHTML = '';

    transactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${new Date(transaction.timestamp).toLocaleDateString()}</td>
            <td>
                <span class="badge bg-${getTransactionColor(transaction.action)}">
                    ${formatTransactionType(transaction.action)}
                </span>
            </td>
            <td>
                <span class="${transaction.action === 'deduct' ? 'text-danger' : 'text-success'}">
                    ${transaction.action === 'deduct' ? '-' : '+'}$${parseFloat(transaction.amount).toFixed(2)}
                </span>
            </td>
            <td>$${parseFloat(transaction.new_balance).toFixed(2)}</td>
            <td>
                <span class="text-muted">${transaction.reason}</span>
                ${transaction.note ? `<br><small>${transaction.note}</small>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function getTransactionColor(action) {
    const colors = {
        'add': 'success',
        'deduct': 'danger',
        'adjust': 'warning'
    };
    return colors[action] || 'secondary';
}

function formatTransactionType(action) {
    const types = {
        'add': 'Added',
        'deduct': 'Deducted',
        'adjust': 'Adjusted'
    };
    return types[action] || action;
}
