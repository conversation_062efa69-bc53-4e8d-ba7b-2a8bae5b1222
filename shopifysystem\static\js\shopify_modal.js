document.addEventListener('DOMContentLoaded', function() {
    // Check if Shopify is connected by looking for the "Connected" text
    const shopifyStatus = document.querySelector('.fa-shopping-cart').nextElementSibling;
    const isConnected = shopifyStatus && shopifyStatus.textContent.includes('Connected');

    // Show Shopify modal when not connected
    if (!isConnected) {
        document.getElementById('shopifyModal').style.display = 'block';
    }

    // Close Shopify modal when clicking the X
    document.querySelector('.close-shopify-modal').onclick = function() {
        document.getElementById('shopifyModal').style.display = 'none';
    };

    // Close Shopify modal when clicking outside
    window.onclick = function(event) {
        if (event.target == document.getElementById('shopifyModal')) {
            document.getElementById('shopifyModal').style.display = 'none';
        }
    };

    // Handle Shopify form submission
    document.getElementById('shopifyForm').onsubmit = function(e) {
        e.preventDefault();
        
        const formData = {
            storeName: document.getElementById('storeName').value,
            collabCode: document.getElementById('collabCode').value
        };

        fetch('/shopify_auth/submit-shopify-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Information submitted successfully!');
                document.getElementById('shopifyModal').style.display = 'none';
                // Reload the page to show updated status
                window.location.reload();
            } else {
                alert(data.message || 'Error submitting information. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error submitting information. Please try again.');
        });
    };
});
