import multiprocessing

# Optimal worker calculation for 16 CPUs
def get_worker_count():
    cores = multiprocessing.cpu_count()
    # More conservative: (cores * 1.5) to prevent overloading
    workers = max(int(cores * 1.5), 8)
    return min(workers, 16)  # Cap at total core count

# Server socket
bind = "0.0.0.0:8000"
workers = get_worker_count()
worker_class = "gevent"  # Efficient for I/O-bound applications
threads = 2  # Light threading with gevent
worker_connections = 400  # Balanced concurrency

# Performance tuning
timeout = 180  # Generous timeout
keepalive = 10
max_requests = 750  # Periodic worker recycling
max_requests_jitter = 50

# Memory management
graceful_timeout = 60
limit_request_fields = 50
limit_request_field_size = 4096

# Logging
accesslog = "/var/log/gunicorn/access.log"
errorlog = "/var/log/gunicorn/error.log"
loglevel = "info"

# Process naming
proc_name = "tcgsync_gunicorn"

# Additional performance optimizations
preload_app = True
capture_output = True