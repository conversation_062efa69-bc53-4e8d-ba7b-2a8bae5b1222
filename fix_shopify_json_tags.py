import json
import re
import os
from datetime import datetime

def clean_tag(tag_str):
    """Clean a single tag according to Shopify requirements"""
    if not tag_str:
        return None
    
    # Convert to string if not already
    tag_str = str(tag_str).strip()
    
    # Skip empty tags after stripping
    if not tag_str:
        return None
    
    # Remove HTML tags first
    tag_str = re.sub(r'<[^>]+>', '', tag_str)
    
    # Remove HTML entities and special characters
    tag_str = tag_str.replace('&nbsp;', ' ')
    tag_str = tag_str.replace('&amp;', '&')
    tag_str = tag_str.replace('&lt;', '<')
    tag_str = tag_str.replace('&gt;', '>')
    tag_str = tag_str.replace('&quot;', '"')
    tag_str = tag_str.replace('&#39;', "'")
    
    # Remove line breaks and carriage returns
    tag_str = tag_str.replace('\r\n', ' ')
    tag_str = tag_str.replace('\r', ' ')
    tag_str = tag_str.replace('\n', ' ')
    
    # Remove other problematic characters
    tag_str = re.sub(r'[^\w\s\-&().,]', '', tag_str)
    
    # Replace multiple spaces with a single space
    tag_str = re.sub(r'\s+', ' ', tag_str).strip()
    
    # Limit tag length to 50 characters to be safe
    if len(tag_str) > 50:
        tag_str = tag_str[:50].strip()
    
    # Skip if tag is now empty after cleaning or too short to be meaningful
    if tag_str and len(tag_str) >= 2:
        return tag_str
    
    return None

def extract_basic_tags_from_product(product_input):
    """Extract only the basic tags we want from a product"""
    # Try to extract basic information from the title and existing data
    title = product_input.get('title', '')
    vendor = product_input.get('vendor', '')
    
    # Look for metafields to get more structured data
    metafields = product_input.get('metafields', [])
    set_name = None
    number = None
    game = vendor  # Default to vendor
    
    for metafield in metafields:
        if metafield.get('key') == 'set':
            set_name = metafield.get('value')
        elif metafield.get('key') == 'number':
            number = metafield.get('value')
        elif metafield.get('key') == 'game':
            game = metafield.get('value')
    
    # Try to extract rarity from existing tags if possible
    existing_tags = product_input.get('tags', '').split(', ')
    rarity = None
    abbreviation = None
    
    # Common rarities to look for
    rarities = ['Common', 'Uncommon', 'Rare', 'Mythic Rare', 'Ultra Rare', 'Secret Rare', 'Radiant Rare']
    for tag in existing_tags:
        clean_tag_str = clean_tag(tag)
        if clean_tag_str in rarities:
            rarity = clean_tag_str
            break
    
    # Try to extract abbreviation (usually short codes like "SWSH11")
    for tag in existing_tags:
        clean_tag_str = clean_tag(tag)
        if clean_tag_str and len(clean_tag_str) <= 10 and re.match(r'^[A-Z0-9]+$', clean_tag_str):
            abbreviation = clean_tag_str
            break
    
    # Build the simplified tag list
    basic_tags = []
    
    if game:
        basic_tags.append(game)
    if set_name:
        basic_tags.append(set_name)
    if abbreviation:
        basic_tags.append(abbreviation)
    if rarity:
        basic_tags.append(rarity)
    if number:
        basic_tags.append(number)
    
    return basic_tags

def fix_json_file(file_path):
    """Fix tags in a JSON file"""
    print(f"Processing file: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print(f"Skipping {file_path} - not a list of products")
            return False
        
        modified = False
        
        for i, product in enumerate(data):
            if 'input' in product and 'tags' in product['input']:
                original_tags = product['input']['tags']
                
                # Extract basic tags from the product
                basic_tags = extract_basic_tags_from_product(product['input'])
                
                # Clean each basic tag
                cleaned_tags = []
                for tag in basic_tags:
                    clean_tag_result = clean_tag(tag)
                    if clean_tag_result:
                        cleaned_tags.append(clean_tag_result)
                
                # Join with comma and space
                new_tags = ', '.join(cleaned_tags) if cleaned_tags else ""
                
                if new_tags != original_tags:
                    print(f"  Product {i+1}:")
                    print(f"    Original tags: {original_tags[:100]}...")
                    print(f"    New tags: {new_tags}")
                    product['input']['tags'] = new_tags
                    modified = True
        
        if modified:
            # Create backup
            backup_path = file_path + '.backup'
            os.rename(file_path, backup_path)
            print(f"  Created backup: {backup_path}")
            
            # Write fixed file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"  Fixed file saved: {file_path}")
            return True
        else:
            print(f"  No changes needed for {file_path}")
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all JSON files"""
    print("=== SHOPIFY JSON TAG FIXER ===")
    print(f"Timestamp: {datetime.now()}")
    
    # Look for JSON files in shopify_json_files directory
    json_dir = "shopify_json_files"
    
    if not os.path.exists(json_dir):
        print(f"Directory {json_dir} not found")
        return
    
    fixed_files = 0
    total_files = 0
    
    # Walk through all subdirectories
    for root, dirs, files in os.walk(json_dir):
        for file in files:
            if file.endswith('.json') and not file.endswith('.backup'):
                file_path = os.path.join(root, file)
                total_files += 1
                
                if fix_json_file(file_path):
                    fixed_files += 1
    
    print(f"\n=== SUMMARY ===")
    print(f"Total JSON files processed: {total_files}")
    print(f"Files modified: {fixed_files}")
    print(f"Files unchanged: {total_files - fixed_files}")

if __name__ == "__main__":
    main()
