from mongoengine import Document, ReferenceField, StringField, IntField, DateTimeField, FloatField, BooleanField
from models.user_model import User

class StagedInventory(Document):
    user = ReferenceField(User, required=True)
    skuId = StringField(required=True)  # Used to match with catalog
    product_id = StringField(required=False)  # Will be populated from catalog match
    quantity = IntField(required=True, default=1)
    condition = StringField(default='Near Mint')
    name = StringField()
    shProduct = StringField()  # Reference to Shopify product
    matched_variant = BooleanField(default=False)
    lowPrice = FloatField()
    foil = StringField(default='Normal')
    created_at = DateTimeField(required=True)
    updated_at = DateTimeField(required=True)

    meta = {
        'collection': 'staged_inventory',
        'indexes': [
            'user',
            'skuId',
            'product_id',
            ('user', 'skuId'),
        ]
    }
