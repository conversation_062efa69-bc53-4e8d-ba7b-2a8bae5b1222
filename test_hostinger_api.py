#!/usr/bin/env python3
"""
Test Hostinger API
This script tests the Hostinger API using the provided API key to retrieve virtual machines.
"""

import requests
import json
import sys

def test_hostinger_api():
    print("\n" + "="*70)
    print("🔍 TESTING HOSTINGER API")
    print("="*70)
    
    # API key from the original task
    api_key = "VZhm5unXOgitVHsLfmk9RomYE5CYu4c5B6CWkVNL148c393d"
    
    # API endpoint
    endpoint = "https://developers.hostinger.com/api/vps/v1/virtual-machines"
    
    # Headers
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("1. 🔄 Sending API request to Hostinger...")
    print(f"Endpoint: {endpoint}")
    print(f"API Key: {api_key[:5]}...{api_key[-5:]}")
    
    try:
        # Make the API request
        response = requests.get(endpoint, headers=headers)
        
        # Check response status code
        print(f"\n2. 📊 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API request successful!")
            
            # Parse JSON response
            data = response.json()
            
            # Pretty print the response
            print("\n3. 📋 API Response Data:")
            print(json.dumps(data, indent=2))
            
            # Count virtual machines
            if isinstance(data, list):
                vm_count = len(data)
                print(f"\n4. 🖥️ Found {vm_count} virtual machines")
                
                # Display details of each VM
                for i, vm in enumerate(data, 1):
                    print(f"\nVirtual Machine #{i}:")
                    if "id" in vm:
                        print(f"  ID: {vm['id']}")
                    if "hostname" in vm:
                        print(f"  Hostname: {vm['hostname']}")
                    if "state" in vm:
                        print(f"  State: {vm['state']}")
                    if "ipv4" in vm and vm["ipv4"]:
                        print(f"  IPv4: {vm['ipv4'][0]['address']}")
                    if "plan" in vm:
                        print(f"  Plan: {vm['plan']}")
            elif "data" in data and isinstance(data["data"], list):
                vm_count = len(data["data"])
                print(f"\n4. 🖥️ Found {vm_count} virtual machines")
                
                # Display details of each VM
                for i, vm in enumerate(data["data"], 1):
                    print(f"\nVirtual Machine #{i}:")
                    if "id" in vm:
                        print(f"  ID: {vm['id']}")
                    if "name" in vm:
                        print(f"  Name: {vm['name']}")
                    if "status" in vm:
                        print(f"  Status: {vm['status']}")
                    if "ip" in vm:
                        print(f"  IP: {vm['ip']}")
            else:
                print("\n⚠️ No virtual machines found in the response")
        elif response.status_code == 401:
            print("❌ Authentication failed. Invalid API key.")
        elif response.status_code == 403:
            print("❌ Access forbidden. Your API key may not have permission to access this resource.")
        elif response.status_code == 404:
            print("❌ Resource not found. The API endpoint may be incorrect.")
        else:
            print(f"❌ API request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Error making API request: {e}")
    except json.JSONDecodeError:
        print("❌ Error parsing JSON response")
        print(f"Response text: {response.text}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    print("\n" + "="*70)
    print("✅ HOSTINGER API TEST COMPLETE")
    print("="*70)

if __name__ == "__main__":
    test_hostinger_api()
