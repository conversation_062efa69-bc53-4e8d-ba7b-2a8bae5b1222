from mongoengine import Document, <PERSON><PERSON><PERSON>, ReferenceField, DateTimeField
from models.user_model import User
from datetime import datetime

class Report(Document):
    user = ReferenceField(User, required=True)
    title = StringField(required=True)
    description = StringField()
    report_type = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': str(self.id),
            'title': self.title,
            'description': self.description,
            'report_type': self.report_type,
            'created_at': self.created_at
        }
