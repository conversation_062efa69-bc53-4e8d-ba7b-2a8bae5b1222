from mongoengine import Document, StringField, FloatField, DateTimeField, ListField, EmbeddedDocumentField, EmbeddedDocument, ReferenceField, DictField, IntField, ObjectIdField, BooleanField, BinaryField
from flask_login import current_user
from datetime import datetime, timedelta

# Payment Method EmbeddedDocument
class PaymentMethod(EmbeddedDocument):
    type = StringField(required=True)  # 'cash', 'card', etc.
    amount = FloatField(required=True)
    reference_id = StringField()  # For card payments, store the transaction reference
    timestamp = DateTimeField(default=datetime.utcnow)

    meta = {'strict': False}

# Transaction EmbeddedDocument
class Transaction(EmbeddedDocument):
    items = ListField(DictField())  # Changed to store item details as dictionaries
    total = FloatField(required=True)
    payment_method = StringField()  # For backward compatibility
    payment_methods = ListField(EmbeddedDocumentField(PaymentMethod))  # For split payments
    is_split_payment = BooleanField(default=False)  # Flag to indicate if this is a split payment
    customer_id = StringField()
    timestamp = DateTimeField(default=datetime.utcnow)
    employee_name = StringField(required=True)

    # Gift card fields
    gift_card_id = StringField()
    gift_card_amount = FloatField(default=0.0)

    # Store credit fields
    store_credit_account_id = StringField()
    store_credit_amount = FloatField(default=0.0)

    meta = {'strict': False}

# PosSetting Document
class PosSetting(Document):
    location = StringField(required=True)
    starting_float = FloatField(required=True)
    running_total = FloatField(required=True)
    tax_rate = FloatField(required=True)
    tax_inclusive = BooleanField(default=False)  # Whether tax is included in product prices
    username = StringField(required=True)
    transactions = ListField(EmbeddedDocumentField(Transaction))

    # Shopify location field
    shopify_location_id = StringField()
    shopify_location_name = StringField()

    # Merchant Match Card Machine fields
    tpn = StringField()  # Terminal Provider Number
    register_id = StringField()
    auth_key = StringField()

    # Staff tracking fields
    current_staff = StringField()  # Username of the staff member currently using this till
    staff_name = StringField()     # Display name of the staff member currently using this till
    last_used = DateTimeField()    # When the till was last used

    meta = {'strict': False}

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = current_user.username
        if self.running_total is None:
            self.running_total = self.starting_float

        # Update the last_used timestamp whenever the till is saved
        self.last_used = datetime.utcnow()

        return super(PosSetting, self).save(*args, **kwargs)

# Layaway Document
class Layaway(Document):
    customer_id = StringField(required=True)
    items = ListField(DictField(), required=True)  # Changed to store item details as dictionaries
    total = FloatField(required=True)
    deposit = FloatField(required=True)
    remaining_balance = FloatField(required=True)
    due_date = DateTimeField(required=True)
    till = ReferenceField(PosSetting, required=True)
    status = StringField(choices=['active', 'completed', 'cancelled'], default='active')

    # Gift card fields
    gift_card_id = StringField()
    gift_card_amount = FloatField(default=0.0)

    # Store credit fields
    store_credit_account_id = StringField()
    store_credit_amount = FloatField(default=0.0)

    meta = {'strict': False}

    def save(self, *args, **kwargs):
        if not self.due_date:
            self.due_date = datetime.utcnow() + timedelta(days=14)
        return super(Layaway, self).save(*args, **kwargs)

# Employee Document
class Employee(Document):
    name = StringField(required=True)
    pin = StringField(required=True)
    username = StringField(required=True)

    meta = {'strict': False}

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = current_user.username
        return super(Employee, self).save(*args, **kwargs)

# Currency Document
class Currency(Document):
    name = StringField(required=True)
    rate = FloatField(required=True)
    username = StringField(required=True)

    meta = {'strict': False}

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = current_user.username
        return super(Currency, self).save(*args, **kwargs)

# HeldSale Document
class HeldSale(Document):
    customer_id = StringField()
    customer_name = StringField()  # Added customer_name to store the name of the customer
    items = ListField(DictField(), required=True)  # Changed to store item details as dictionaries
    till = ReferenceField(PosSetting, required=True)
    timestamp = DateTimeField(default=datetime.utcnow)
    employee_name = StringField(required=True)
    username = StringField(required=True)

    meta = {'collection': 'held_sale', 'strict': False}

# Quicklink Document
class Quicklink(Document):
    till = ReferenceField(PosSetting, required=True)
    quicklink_number = IntField(required=True, min_value=0, max_value=3)
    product_id = ObjectIdField(required=True)
    product_name = StringField(required=True)
    username = StringField(required=True)

    meta = {'collection': 'quicklinks'}

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = current_user.username
        return super(Quicklink, self).save(*args, **kwargs)

# SavedCategorySearch Document
class SavedCategorySearch(Document):
    vendor = StringField(required=True)
    product_type = StringField(required=True)
    display_name = StringField(required=True)
    in_stock_only = BooleanField(default=True)
    username = StringField(required=True)
    timestamp = DateTimeField(default=datetime.utcnow)

    meta = {'collection': 'saved_category_searches'}

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = current_user.username
        return super(SavedCategorySearch, self).save(*args, **kwargs)

# TillClosing Document
class TillClosing(Document):
    # Basic information
    till_id = StringField(required=True)
    location = StringField(required=True)
    username = StringField(required=True)
    employee_name = StringField()

    # Timestamps
    business_date = DateTimeField(required=True)  # The business date this closing represents
    closing_time = DateTimeField(default=datetime.utcnow)

    # Financial data
    starting_float = FloatField(required=True)
    ending_float = FloatField(required=True)  # Actual counted amount
    expected_cash = FloatField(required=True)  # Calculated expected amount
    cash_discrepancy = FloatField(required=True)  # Difference between actual and expected

    # Sales summaries
    total_sales = FloatField(required=True)
    cash_sales = FloatField(required=True)
    card_sales = FloatField(required=True)
    gift_card_sales = FloatField(default=0.0)
    store_credit_sales = FloatField(default=0.0)
    total_payouts = FloatField(default=0.0)

    # Transaction details
    transaction_count = IntField(required=True)
    transactions = ListField(EmbeddedDocumentField(Transaction))

    # Tax information
    total_tax_collected = FloatField(default=0.0)

    # Cash count details
    cash_count = DictField()  # Stores denomination counts: {currency, notes: [{value, count}], coins: [{value, count}]}

    # Notes and approval
    discrepancy_notes = StringField()
    closing_notes = StringField()
    manager_approval = BooleanField(default=False)
    manager_username = StringField()

    # Z-Report number (sequential)
    z_report_number = IntField()

    meta = {'collection': 'till_closings'}

# TillClosingReport Document
class TillClosingReport(Document):
    till_closing = ReferenceField(TillClosing, required=True)
    html_report = StringField()  # HTML formatted report for viewing/printing
    pdf_data = BinaryField()     # Optional PDF version
    generated_at = DateTimeField(default=datetime.utcnow)

    meta = {'collection': 'till_closing_reports'}
