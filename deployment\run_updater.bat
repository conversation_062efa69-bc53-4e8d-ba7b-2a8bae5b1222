@echo off
echo Server Updater Script
echo =====================

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is required but not installed.
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install -r deployment\requirements.txt

REM Run the updater script
echo Running server updater...
python deployment\server_updater.py

echo Done!
pause
