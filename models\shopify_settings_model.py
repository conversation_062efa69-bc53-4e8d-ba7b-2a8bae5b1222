from mongoengine import Document, StringField, DictField, FloatField, ListField, BooleanField, IntField

class ShopifySettings(Document):
    """
    Settings specific to Shopify pricing and auto-repricing functionality.
    Separate from buylist/user settings to avoid conflicts.
    """
    username = String<PERSON>ield(required=True, unique=True)
    
    # Price calculation settings
    minPrice = FloatField(default=0.50)  # Default minimum price
    price_point = StringField(default='Low Price')  # Options: Low Price, Mid Price, Market Price, High Price
    price_rounding_enabled = BooleanField(default=False)  # Enable/disable price rounding
    price_rounding_thresholds = ListField(IntField(), default=[49, 99])  # List of rounding thresholds
    
    # Price comparison settings
    use_highest_price = BooleanField(default=False)  # Use highest price from comparison pairs
    price_comparison_pairs = ListField(ListField(StringField()), default=[])  # List of price type pairs to compare
    price_modifiers = DictField(default={})  # Modifiers for each price type
    price_preference_order = ListField(StringField(), default=['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
    
    # Game-specific settings
    game_minimum_prices = DictField(default={})  # Game-specific minimum prices
    advancedPricingRules = DictField(default={})  # Advanced pricing rules by vendor/product/expansion
    customStepping = DictField(default={
        'nm': 100,
        'lp': 80,
        'mp': 70,
        'hp': 65,
        'dm': 50
    })
    
    # TCG trend settings
    tcg_trend_increasing = FloatField(default=0.0)
    tcg_trend_decreasing = FloatField(default=0.0)

    meta = {
        'collection': 'shopify_settings',
        'indexes': [
            {'fields': ['username'], 'unique': True}
        ]
    }
