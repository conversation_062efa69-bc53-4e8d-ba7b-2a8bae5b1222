#!/usr/bin/env python3
"""
Clean and Rebuild Gunicorn
This script completely removes all existing gunicorn configurations and services,
then creates a fresh, clean setup with proper performance optimizations.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def clean_and_rebuild_gunicorn():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to clean and rebuild gunicorn...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🧹 COMPLETELY REMOVING ALL EXISTING GUNICORN CONFIGURATIONS")
        print("="*70)
        
        # 1. Stop and disable any existing gunicorn services
        print("1. 🛑 Stopping and disabling all gunicorn services...")
        execute_command(ssh_client, "systemctl stop gunicorn")
        execute_command(ssh_client, "systemctl disable gunicorn")
        execute_command(ssh_client, "systemctl daemon-reload")
        
        # Kill all gunicorn processes
        execute_command(ssh_client, "pkill -9 -f gunicorn")
        time.sleep(2)
        
        # Check if any gunicorn processes are still running
        output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep")
        if output:
            print("⚠️ Some gunicorn processes are still running, killing them forcefully...")
            execute_command(ssh_client, "pkill -9 -f gunicorn")
            time.sleep(1)
        
        print("✅ All gunicorn processes stopped")
        
        # 2. Remove all existing gunicorn configuration files
        print("\n2. 🗑️ Removing all existing gunicorn configuration files...")
        execute_command(ssh_client, "rm -f /var/www/html/gunicorn*.conf.py")
        execute_command(ssh_client, "rm -f /var/www/html/emergency.conf.py")
        execute_command(ssh_client, "rm -f /etc/systemd/system/gunicorn.service")
        
        # Check if files were removed
        output, error, code = execute_command(ssh_client, "find /var/www/html -name 'gunicorn*.conf.py'")
        if output:
            print(f"⚠️ Some configuration files still exist: {output}")
            execute_command(ssh_client, "rm -f " + " ".join(output.split('\n')))
        
        print("✅ All gunicorn configuration files removed")
        
        # 3. Clean up log files
        print("\n3. 🧹 Cleaning up log files...")
        execute_command(ssh_client, "mkdir -p /var/www/html/logs/app")
        execute_command(ssh_client, "rm -f /var/www/html/logs/app/gunicorn-*.log")
        execute_command(ssh_client, "touch /var/www/html/logs/app/gunicorn-error.log")
        execute_command(ssh_client, "touch /var/www/html/logs/app/gunicorn-access.log")
        execute_command(ssh_client, "chown -R www-data:www-data /var/www/html/logs")
        execute_command(ssh_client, "chmod -R 755 /var/www/html/logs")
        
        print("✅ Log files cleaned up")
        
        # 4. Create fresh gunicorn configuration
        print("\n" + "="*70)
        print("🔨 CREATING FRESH GUNICORN CONFIGURATION")
        print("="*70)
        
        # Get system resources
        output, error, code = execute_command(ssh_client, "nproc")
        cpu_count = int(output.strip()) if output.strip().isdigit() else 4
        
        output, error, code = execute_command(ssh_client, "free -m | grep Mem")
        mem_parts = output.split()
        total_mem = int(mem_parts[1]) if len(mem_parts) > 1 else 8000
        
        # Calculate optimal workers (conservative)
        optimal_workers = min(cpu_count * 2, int((total_mem * 0.7) / 100))
        optimal_workers = max(optimal_workers, 4)  # At least 4 workers
        
        print(f"Setting worker count to {optimal_workers} based on {cpu_count} CPU cores and {total_mem}MB memory")
        
        # Create a simple, clean configuration
        clean_config = f'''
# Clean Gunicorn Configuration
workers = {optimal_workers}
worker_class = "sync"  # Start with sync for stability
timeout = 120
keepalive = 5
max_requests = 1000
max_requests_jitter = 200
bind = "127.0.0.1:8000"
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"
'''
        
        config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{clean_config}\nEOF'
        output, error, code = execute_command(ssh_client, config_command)
        
        if code == 0:
            print("✅ Created clean gunicorn configuration")
            
            # Set permissions
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
            execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
        else:
            print(f"❌ Failed to create configuration: {error}")
            return False
        
        # 5. Create clean systemd service file
        print("\n5. 📄 Creating clean systemd service file...")
        
        service_content = '''[Unit]
Description=Gunicorn daemon for TCG Sync Application
After=network.target
Wants=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
ExecStart=/var/www/html/venv/bin/gunicorn --config gunicorn.conf.py wsgi:app
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
'''
        
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        output, error, code = execute_command(ssh_client, service_command)
        
        if code == 0:
            print("✅ Created clean systemd service file")
        else:
            print(f"❌ Failed to create service file: {error}")
            return False
        
        # 6. Reload systemd and enable service
        print("\n6. 🔄 Reloading systemd and enabling service...")
        execute_command(ssh_client, "systemctl daemon-reload")
        output, error, code = execute_command(ssh_client, "systemctl enable gunicorn")
        
        if "Created symlink" in output or code == 0:
            print("✅ Service enabled for auto-start on boot")
        else:
            print(f"⚠️ Warning when enabling service: {error}")
        
        # 7. Start service and verify
        print("\n7. 🚀 Starting service...")
        execute_command(ssh_client, "systemctl start gunicorn")
        time.sleep(5)
        
        # Check if service is running
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running!")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Verify it's using our config
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | head -1")
            if "gunicorn.conf.py" in output:
                print("✅ Using our clean configuration file")
            else:
                print("⚠️ May not be using our configuration file")
                print(f"Process: {output[:100]}...")
        else:
            print("❌ Service failed to start")
            print("Status output:")
            print(output[:300])
            
            # Check logs
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -10")
            print("Recent logs:")
            print(output)
            return False
        
        # 8. Test response time
        print("\n8. 🧪 Testing response time...")
        output, error, code = execute_command(ssh_client, "curl -s -w 'Response time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
        
        if code == 0:
            print(f"✅ Application response: {output}")
        else:
            print(f"❌ Failed to test response time: {error}")
        
        # 9. Now that we have a stable base, apply optimizations
        print("\n" + "="*70)
        print("⚡ APPLYING PERFORMANCE OPTIMIZATIONS")
        print("="*70)
        
        # Install gevent
        print("1. 📦 Installing gevent...")
        execute_command(ssh_client, "cd /var/www/html && source venv/bin/activate && pip install gevent")
        print("✅ Gevent installed")
        
        # Create optimized configuration
        print("\n2. ⚡ Creating optimized configuration...")
        
        optimized_config = f'''
# Optimized Gunicorn Configuration
workers = {optimal_workers}
worker_class = "gevent"
worker_connections = 1000
timeout = 120
keepalive = 5
max_requests = 1000
max_requests_jitter = 200
bind = "127.0.0.1:8000"
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"
preload_app = True
worker_tmp_dir = "/dev/shm"
'''
        
        config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{optimized_config}\nEOF'
        output, error, code = execute_command(ssh_client, config_command)
        
        if code == 0:
            print("✅ Created optimized configuration")
            
            # Set permissions
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
            execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
        else:
            print(f"❌ Failed to create optimized configuration: {error}")
        
        # Restart service with optimized config
        print("\n3. 🔄 Restarting service with optimized configuration...")
        execute_command(ssh_client, "systemctl restart gunicorn")
        time.sleep(5)
        
        # Verify service is running with optimized config
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running with optimized configuration!")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Test response time
            print("\n4. 🧪 Testing optimized response time...")
            total_time = 0
            tests = 3
            
            for i in range(tests):
                output, error, code = execute_command(ssh_client, "curl -s -w '%{time_total}' -o /dev/null http://localhost:8000/")
                if code == 0:
                    try:
                        response_time = float(output.strip())
                        total_time += response_time
                        print(f"Test {i+1}: {response_time:.3f}s")
                    except:
                        print(f"Test {i+1}: Failed to parse time")
                else:
                    print(f"Test {i+1}: Failed")
                
                time.sleep(1)
            
            if total_time > 0:
                avg_time = total_time / tests
                print(f"\nAverage response time: {avg_time:.3f}s")
        else:
            print("❌ Service failed to start with optimized configuration")
            print("Status output:")
            print(output[:300])
            
            # Revert to basic configuration
            print("\n⚠️ Reverting to basic configuration...")
            execute_command(ssh_client, f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{clean_config}\nEOF')
            execute_command(ssh_client, "systemctl restart gunicorn")
        
        # 10. Final verification
        print("\n" + "="*70)
        print("✅ GUNICORN CLEAN REBUILD COMPLETE!")
        print("="*70)
        print("The following has been done:")
        print("1. ✅ Completely removed all existing gunicorn configurations")
        print("2. ✅ Created a fresh, clean gunicorn configuration")
        print("3. ✅ Set up a proper systemd service")
        print("4. ✅ Verified the service is running correctly")
        print("5. ✅ Applied performance optimizations")
        print("6. ✅ Enabled auto-start on server reboot")
        
        print("\nTo manage the service, use these commands:")
        print("- Check status: systemctl status gunicorn")
        print("- Restart service: systemctl restart gunicorn")
        print("- View logs: journalctl -u gunicorn -f")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    clean_and_rebuild_gunicorn()
