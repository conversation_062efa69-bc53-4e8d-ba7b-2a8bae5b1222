from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
import pymongo
from bson import ObjectId
import json
from datetime import datetime
import os
import sys
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import the standardizer class
from utils.shopify_standardizer import ShopifyStandardizer

standardizer_bp = Blueprint('standardizer', __name__, url_prefix='/shopify/standardizer')

# MongoDB setup
from mongo_connection_manager import get_mongo_client
db = get_mongo_client().test
discrepancy_collection = db.productDiscrepancies
shopify_collection = db.shProducts

@standardizer_bp.route('/')
@login_required
def dashboard():
    """Main dashboard view"""
    # Initialize standardizer with current user's username and db connection
    standardizer = ShopifyStandardizer(db_connection=db, username=current_user.username)
    
    # Get stats for the current user only
    stats = standardizer.get_discrepancy_stats()
    
    # Get checked and remaining product counts
    checked_count = shopify_collection.count_documents({
        "username": current_user.username,
        "checked": True,
        "productId": {"$exists": True, "$ne": None, "$ne": ""},
        "productId": {"$not": {"$type": "string"}}
    })
    
    # Count all products with valid productId (not None, not empty string, not a string)
    total_valid_products = shopify_collection.count_documents({
        "username": current_user.username,
        "productId": {"$exists": True, "$ne": None, "$ne": ""},
        "productId": {"$not": {"$type": "string"}}
    })
    
    # Calculate remaining products (total valid products minus checked products)
    remaining_count = total_valid_products - checked_count
    
    return render_template(
        'shopify_standardizer_dashboard.html',
        total=stats.get('total', 0),
        pending=stats.get('pending', 0),
        approved=stats.get('approved', 0),
        rejected=stats.get('rejected', 0),
        repaired=stats.get('repaired', 0),
        issue_breakdown=stats.get('issue_breakdown', {}),
        users=stats.get('users', []),
        checked_count=checked_count,
        remaining_count=remaining_count
    )

@standardizer_bp.route('/run_analysis', methods=['POST'])
@login_required
def run_analysis():
    """Run analysis on products"""
    # Always use the current user's username
    username = current_user.username
    
    # Get game and expansion filters
    game = request.form.get('game', '')
    expansion = request.form.get('expansion', '')
    
    # Check if "analyze all" is checked
    analyze_all = request.form.get('analyze_all') == 'on'
    
    # Get limit (if analyze_all is checked, use None for no limit)
    if analyze_all:
        limit = None
        logger.info("Analyze all products selected - no limit will be applied")
    else:
        limit = request.form.get('limit', 1000)
        try:
            limit = int(limit)
        except ValueError:
            limit = 1000
    
    # Get product type filter
    product_type = request.form.get('product_type', 'all')
    if product_type not in ['all', 'singles', 'sealed']:
        product_type = 'all'
    
    # Build additional filters based on game and expansion
    additional_filters = {}
    if game:
        additional_filters['gameName'] = game
        logger.info(f"Filtering by game: {game}")
    if expansion:
        additional_filters['expansionName'] = expansion
        logger.info(f"Filtering by expansion: {expansion}")
    
    # Initialize standardizer with filters and db connection
    standardizer = ShopifyStandardizer(
        db_connection=db, 
        username=username, 
        limit=limit, 
        product_type=product_type,
        additional_filters=additional_filters
    )
    
    # Run analysis in a background task (for a real implementation)
    # For now, run synchronously for demonstration
    results = standardizer.analyze_products()
    
    product_type_msg = f" ({product_type})" if product_type != 'all' else ""
    limit_msg = " with no limit" if analyze_all else f" with limit {limit}"
    flash(f"Analysis complete: Found {results['total_discrepancies']} products{product_type_msg}{limit_msg} with discrepancies", "success")
    return redirect(url_for('standardizer.dashboard'))

@standardizer_bp.route('/discrepancies')
@login_required
def list_discrepancies():
    """List all discrepancies"""
    # Get filter parameters
    status = request.args.get('status', 'pending')
    issue_type = request.args.get('issue_type', None)
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 50))
    
    # Build query - always filter by current user
    query = {
        'status': status,
        'username': current_user.username
    }
    if issue_type:
        query[f'discrepancies.{issue_type}'] = {'$exists': True}
    
    # Get paginated results
    total = discrepancy_collection.count_documents(query)
    discrepancies = list(discrepancy_collection.find(query)
                         .sort('timestamp', pymongo.DESCENDING)
                         .skip((page - 1) * limit)
                         .limit(limit))
    
    return render_template(
        'shopify_standardizer_list.html',
        discrepancies=discrepancies,
        total=total,
        page=page,
        limit=limit,
        pages=(total // limit) + (1 if total % limit > 0 else 0),
        status=status,
        username=current_user.username,
        issue_type=issue_type
    )

@standardizer_bp.route('/discrepancy/<discrepancy_id>')
@login_required
def view_discrepancy(discrepancy_id):
    """View a single discrepancy in detail"""
    # Only allow viewing discrepancies that belong to the current user
    discrepancy = discrepancy_collection.find_one({
        '_id': ObjectId(discrepancy_id),
        'username': current_user.username
    })
    
    if not discrepancy:
        flash("Discrepancy not found or you don't have permission to view it", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    return render_template(
        'shopify_standardizer_detail.html',
        discrepancy=discrepancy
    )

@standardizer_bp.route('/approve', methods=['POST'])
@login_required
def approve_discrepancy():
    """Approve changes for a discrepancy and immediately update Shopify"""
    discrepancy_id = request.form.get('discrepancy_id')
    
    if not discrepancy_id:
        flash("No discrepancy ID provided", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    # Get the discrepancy - only if it belongs to the current user
    discrepancy = discrepancy_collection.find_one({
        '_id': ObjectId(discrepancy_id),
        'username': current_user.username
    })
    
    if not discrepancy:
        flash("Discrepancy not found or you don't have permission to approve it", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    try:
        # Import the update_shopify_product function directly
        from shopify_utils import update_shopify_product
        
        # Get the product ID from the discrepancy record
        product_id_int = discrepancy.get('product_id_int')
        if not product_id_int:
            flash("Cannot approve discrepancy: no product ID found", "error")
            return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))
        
        # Look up the product in the shProducts collection to get the Shopify ID
        # Include the username to ensure we're getting the correct product for the current user
        logger.info(f"Looking up product with productId: {product_id_int} and username: {current_user.username}")
        product = shopify_collection.find_one({
            "productId": product_id_int,
            "username": current_user.username
        })
        
        if not product:
            flash(f"Cannot approve discrepancy: product with ID {product_id_int} not found", "error")
            return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))
        
        # Get the Shopify ID from the id field
        shopify_id = str(product.get('id'))
        if not shopify_id:
            flash(f"Cannot approve discrepancy: product with ID {product_id_int} has no Shopify ID", "error")
            return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))
        
        logger.info(f"Found Shopify ID: {shopify_id}")
        
        # Prepare the update data
        update_data = {}
        
        # Add title if it has a discrepancy
        if 'title' in discrepancy.get('discrepancies', {}):
            update_data['title'] = discrepancy['discrepancies']['title']['simulated']
            logger.info(f"Adding title to update data: {update_data['title']}")
        
        # Add tags if they have a discrepancy
        if 'tags' in discrepancy.get('discrepancies', {}):
            update_data['tags'] = discrepancy['discrepancies']['tags']['simulated']
            logger.info(f"Adding tags to update data: {update_data['tags']}")
        
        # Only update if there's data to update
        if update_data:
            # Get the user's Shopify credentials from the database
            user_collection = db['user']
            user = user_collection.find_one({"username": current_user.username})
            
            if not user or not user.get('shopifyAccessToken') or not user.get('shopifyStoreName'):
                logger.error(f"No user with username {current_user.username} and Shopify credentials found")
                flash(f"Cannot approve discrepancy: no Shopify credentials found for user {current_user.username}", "error")
                return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))
            
            # Update the product in Shopify
            logger.info(f"Updating product {shopify_id} in Shopify with data: {update_data}")
            result = update_shopify_product(shopify_id, update_data, username=current_user.username)
            
            if result.get('success'):
                flash("Discrepancy approved and changes pushed to Shopify", "success")
            else:
                flash(f"Discrepancy approved but failed to update Shopify: {result.get('error')}", "warning")
        else:
            # If there's no data to update, just mark as approved
            discrepancy_collection.update_one(
                {'_id': ObjectId(discrepancy_id)},
                {'$set': {
                    'status': 'approved',
                    'reviewed_by': current_user.username,
                    'review_timestamp': datetime.now()
                }}
            )
            flash("Discrepancy approved", "success")
    except Exception as e:
        logger.error(f"Error approving discrepancy: {str(e)}")
        logger.exception("Full exception details:")
        flash(f"Error approving discrepancy: {str(e)}", "error")
    
    return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))

@standardizer_bp.route('/reject', methods=['POST'])
@login_required
def reject_discrepancy():
    """Reject changes for a discrepancy"""
    discrepancy_id = request.form.get('discrepancy_id')
    
    if not discrepancy_id:
        flash("No discrepancy ID provided", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    # Update discrepancy status - only if it belongs to the current user
    result = discrepancy_collection.update_one(
        {
            '_id': ObjectId(discrepancy_id),
            'username': current_user.username
        },
        {'$set': {
            'status': 'rejected',
            'reviewed_by': current_user.username,
            'review_timestamp': datetime.now()
        }}
    )
    
    if result.modified_count > 0:
        flash("Discrepancy rejected", "success")
    else:
        flash("Failed to reject discrepancy", "error")
    
    return redirect(url_for('standardizer.view_discrepancy', discrepancy_id=discrepancy_id))

@standardizer_bp.route('/repair', methods=['GET', 'POST'])
@login_required
def repair_discrepancies():
    """Apply approved repairs to Shopify products"""
    # Only perform the repair on POST requests with confirmation
    if request.method == 'POST' and request.form.get('confirm') == 'true':
        try:
            logger.info("=== REPAIR DISCREPANCIES ROUTE CALLED WITH CONFIRMATION ===")
            
            # Initialize the standardizer with the current user's username and db connection
            standardizer = ShopifyStandardizer(db_connection=db, username=current_user.username)
            
            # Use the standardizer's repair_approved_discrepancies method
            # This method already processes discrepancies in chunks
            repaired_count = standardizer.repair_approved_discrepancies()
            
            if repaired_count > 0:
                flash(f"Applied repairs to {repaired_count} products and pushed changes to Shopify", "success")
            else:
                flash("No products were repaired", "warning")
                
        except Exception as e:
            logger.error(f"Error in repair_discrepancies route: {str(e)}")
            logger.exception("Full exception details:")
            flash(f"Error repairing products: {str(e)}", "error")
    
    return redirect(url_for('standardizer.dashboard'))

@standardizer_bp.route('/batch-approve', methods=['POST'])
@login_required
def batch_approve():
    """Approve multiple discrepancies at once and update Shopify"""
    discrepancy_ids = request.form.getlist('discrepancy_ids')
    
    if not discrepancy_ids:
        flash("No discrepancies selected", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    # Convert string IDs to ObjectIds
    object_ids = [ObjectId(id) for id in discrepancy_ids]
    
    # First, mark all selected discrepancies as approved
    result = discrepancy_collection.update_many(
        {
            '_id': {'$in': object_ids},
            'username': current_user.username,
            'status': 'pending'
        },
        {'$set': {
            'status': 'approved',
            'reviewed_by': current_user.username,
            'review_timestamp': datetime.now()
        }}
    )
    
    approved_count = result.modified_count
    
    if approved_count == 0:
        flash("No discrepancies were approved. They may have already been processed.", "warning")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    # Now use the standardizer to push changes to Shopify
    try:
        # Initialize the standardizer with the current user's username and db connection
        standardizer = ShopifyStandardizer(db_connection=db, username=current_user.username)
        
        # Use the standardizer's repair_approved_discrepancies method
        # This method already processes discrepancies in chunks
        repaired_count = standardizer.repair_approved_discrepancies()
        
        if repaired_count > 0:
            flash(f"Approved and updated {repaired_count} products in Shopify", "success")
        else:
            flash(f"Approved {approved_count} discrepancies but failed to update any products in Shopify", "warning")
    except Exception as e:
        logger.error(f"Error updating products in Shopify: {str(e)}")
        logger.exception("Full exception details:")
        flash(f"Approved {approved_count} discrepancies but encountered an error updating Shopify: {str(e)}", "warning")
    
    return redirect(url_for('standardizer.list_discrepancies'))

@standardizer_bp.route('/approve-all', methods=['POST'])
@login_required
def approve_all_discrepancies():
    """Approve all pending discrepancies at once (without updating Shopify)"""
    try:
        # Get count of pending discrepancies for the current user
        total_discrepancies = discrepancy_collection.count_documents({
            'status': 'pending',
            'username': current_user.username
        })
        
        if total_discrepancies == 0:
            flash("No pending discrepancies found to approve", "warning")
            return redirect(url_for('standardizer.list_discrepancies'))
        
        logger.info(f"Found {total_discrepancies} pending discrepancies to approve")
        
        # Process discrepancies in chunks to avoid loading everything into memory
        chunk_size = 100  # Process 100 discrepancies at a time
        total_chunks = (total_discrepancies + chunk_size - 1) // chunk_size
        
        approved_count = 0
        error_count = 0
        
        for chunk_index in range(total_chunks):
            # Calculate skip and limit for this chunk
            skip = chunk_index * chunk_size
            limit_for_chunk = min(chunk_size, total_discrepancies - skip)
            
            if limit_for_chunk <= 0:
                break
                
            logger.info(f"Processing chunk {chunk_index + 1}/{total_chunks} (discrepancies {skip+1}-{skip+limit_for_chunk})")
            
            # Get discrepancies for this chunk
            discrepancies_chunk = list(discrepancy_collection.find({
                'status': 'pending',
                'username': current_user.username
            }).skip(skip).limit(limit_for_chunk))
            
            # Get IDs for this chunk
            chunk_ids = [d['_id'] for d in discrepancies_chunk]
            
            try:
                # Mark all discrepancies in this chunk as approved in a single operation
                result = discrepancy_collection.update_many(
                    {
                        '_id': {'$in': chunk_ids},
                        'username': current_user.username,
                        'status': 'pending'
                    },
                    {'$set': {
                        'status': 'approved',
                        'reviewed_by': current_user.username,
                        'review_timestamp': datetime.now()
                    }}
                )
                
                chunk_approved = result.modified_count
                approved_count += chunk_approved
                
                if chunk_approved < len(discrepancies_chunk):
                    error_count += (len(discrepancies_chunk) - chunk_approved)
                    logger.warning(f"Failed to mark {len(discrepancies_chunk) - chunk_approved} discrepancies as approved in chunk {chunk_index + 1}")
                
                logger.info(f"Approved {chunk_approved} discrepancies in chunk {chunk_index + 1}")
                
            except Exception as e:
                error_count += len(discrepancies_chunk)
                logger.error(f"Error processing chunk {chunk_index + 1}: {str(e)}")
                logger.exception("Full exception details:")
        
        if approved_count > 0:
            if error_count > 0:
                flash(f"Approved {approved_count} discrepancies with {error_count} errors. Use 'Push Changes to Shopify' to update Shopify.", "warning")
            else:
                flash(f"Approved all {approved_count} discrepancies. Use 'Push Changes to Shopify' to update Shopify.", "success")
        else:
            flash("Failed to approve any discrepancies. There may be connection issues with the database.", "error")
    
    except pymongo.errors.ServerSelectionTimeoutError as e:
        logger.error(f"MongoDB connection timeout: {str(e)}")
        flash("Database connection timeout. Please try again later.", "error")
    except Exception as e:
        logger.error(f"Error in approve_all_discrepancies: {str(e)}")
        logger.exception("Full exception details:")
        flash(f"An error occurred: {str(e)}", "error")
    
    return redirect(url_for('standardizer.list_discrepancies'))

@standardizer_bp.route('/batch-reject', methods=['POST'])
@login_required
def batch_reject():
    """Reject multiple discrepancies at once"""
    discrepancy_ids = request.form.getlist('discrepancy_ids')
    
    if not discrepancy_ids:
        flash("No discrepancies selected", "error")
        return redirect(url_for('standardizer.list_discrepancies'))
    
    # Convert string IDs to ObjectIds
    object_ids = [ObjectId(id) for id in discrepancy_ids]
    
    # Process in chunks if there are many IDs
    chunk_size = 100
    total_rejected = 0
    
    # Split object_ids into chunks
    for i in range(0, len(object_ids), chunk_size):
        chunk = object_ids[i:i + chunk_size]
        
        # Update all selected discrepancies in this chunk that belong to the current user
        result = discrepancy_collection.update_many(
            {
                '_id': {'$in': chunk},
                'username': current_user.username
            },
            {'$set': {
                'status': 'rejected',
                'reviewed_by': current_user.username,
                'review_timestamp': datetime.now()
            }}
        )
        
        total_rejected += result.modified_count
        logger.info(f"Rejected {result.modified_count} discrepancies in chunk {i//chunk_size + 1}")
    
    if total_rejected > 0:
        flash(f"Rejected {total_rejected} discrepancies", "success")
    else:
        flash("Failed to reject discrepancies", "error")
    
    return redirect(url_for('standardizer.list_discrepancies'))

@standardizer_bp.route('/api/games')
@login_required
def api_games():
    """API endpoint to get games from the user's shProducts collection"""
    try:
        logger.info(f"Fetching games for user: {current_user.username}")
        
        # Check if the user has any products
        user_products_count = shopify_collection.count_documents({"username": current_user.username})
        logger.info(f"User has {user_products_count} products in the collection")
        
        # Get distinct gameNames from the user's products
        games = shopify_collection.distinct("gameName", {"username": current_user.username})
        logger.info(f"Raw games from DB: {games}")
        
        # Filter out None or empty values and sort alphabetically
        games = sorted([game for game in games if game and isinstance(game, str)])
        logger.info(f"Filtered and sorted games: {games}")
        
        # Check if gameName field exists in any documents
        sample_with_gamename = shopify_collection.find_one(
            {"username": current_user.username, "gameName": {"$exists": True}}
        )
        if sample_with_gamename:
            logger.info(f"Found document with gameName: {sample_with_gamename.get('gameName', 'None')}")
        else:
            logger.warning("No documents found with gameName field")
            
            # Check if there's a different field that might contain game names
            sample_doc = shopify_collection.find_one({"username": current_user.username})
            if sample_doc:
                logger.info(f"Sample document fields: {list(sample_doc.keys())}")
                
                # Check if 'game' field exists instead of 'gameName'
                if 'game' in sample_doc:
                    logger.info("Found 'game' field instead of 'gameName'")
                    games = shopify_collection.distinct("game", {"username": current_user.username})
                    games = sorted([game for game in games if game and isinstance(game, str)])
                    logger.info(f"Games from 'game' field: {games}")
        
        return jsonify({'games': games})
    except Exception as e:
        logger.error(f"Error fetching games: {str(e)}")
        logger.exception("Full exception details:")
        return jsonify({'error': str(e), 'games': []}), 500

@standardizer_bp.route('/api/expansions')
@login_required
def api_expansions():
    """API endpoint to get expansions for a selected game"""
    try:
        # Get the game parameter
        game = request.args.get('game')
        logger.info(f"Fetching expansions for game: '{game}' and user: {current_user.username}")
        
        if not game:
            logger.warning("No game parameter provided")
            return jsonify({'error': 'Game parameter is required', 'expansions': []}), 400
        
        # Get distinct expansions for the selected game from the user's products
        query = {
            "username": current_user.username,
            "gameName": game
        }
        logger.info(f"Query for expansions: {query}")
        
        # Check if any products match the query
        matching_products_count = shopify_collection.count_documents(query)
        logger.info(f"Found {matching_products_count} products matching the query")
        
        # Get distinct expansions
        expansions = shopify_collection.distinct("expansionName", query)
        logger.info(f"Raw expansions from DB: {expansions}")
        
        # Check if expansionName field exists in any documents
        sample_with_expansionname = shopify_collection.find_one(
            {"username": current_user.username, "gameName": game, "expansionName": {"$exists": True}}
        )
        
        if not sample_with_expansionname:
            logger.warning(f"No documents found with expansionName field for game: {game}")
            
            # Check if there's a different field that might contain expansion names
            sample_doc = shopify_collection.find_one({"username": current_user.username, "gameName": game})
            if sample_doc:
                logger.info(f"Sample document fields: {list(sample_doc.keys())}")
                
                # Check if 'expansion' field exists instead of 'expansionName'
                if 'expansion' in sample_doc:
                    logger.info("Found 'expansion' field instead of 'expansionName'")
                    query = {
                        "username": current_user.username,
                        "gameName": game
                    }
                    expansions = shopify_collection.distinct("expansion", query)
                    logger.info(f"Expansions from 'expansion' field: {expansions}")
                    
                    # If still no results and we found 'game' field earlier, try that combination
                    if not expansions:
                        logger.info("Trying with 'game' field instead of 'gameName'")
                        query = {
                            "username": current_user.username,
                            "game": game
                        }
                        expansions = shopify_collection.distinct("expansion", query)
                        logger.info(f"Expansions from 'game'+'expansion' fields: {expansions}")
        
        # Filter out None or empty values and sort alphabetically
        expansions = sorted([exp for exp in expansions if exp and isinstance(exp, str)])
        logger.info(f"Filtered and sorted expansions: {expansions}")
        
        return jsonify({'expansions': expansions})
    except Exception as e:
        logger.error(f"Error fetching expansions: {str(e)}")
        logger.exception("Full exception details:")
        return jsonify({'error': str(e), 'expansions': []}), 500

@standardizer_bp.route('/api/analyze_product', methods=['POST'])
@login_required
def api_analyze_product():
    """API endpoint to analyze a single product and return results as JSON"""
    # Get product ID from request
    product_id = request.json.get('product_id')
    
    if not product_id:
        return jsonify({'success': False, 'error': 'No product ID provided'}), 400
    
    try:
        # Find the product in the database
        product = shopify_collection.find_one({'_id': ObjectId(product_id)})
        
        if not product:
            return jsonify({'success': False, 'error': 'Product not found'}), 404
        
        # Initialize standardizer with current user's username and db connection
        standardizer = ShopifyStandardizer(
            db_connection=db, 
            username=current_user.username
        )
        
        # Analyze the product
        discrepancies = standardizer.analyze_single_product(product)
        
        # Prepare response
        if discrepancies:
            # Format discrepancies for JSON response
            formatted_discrepancies = {}
            for field, values in discrepancies.items():
                formatted_discrepancies[field] = {
                    'existing': values.get('existing'),
                    'simulated': values.get('simulated')
                }
            
            return jsonify({
                'success': True,
                'has_discrepancies': True,
                'product_title': product.get('title'),
                'discrepancies': formatted_discrepancies
            })
        else:
            return jsonify({
                'success': True,
                'has_discrepancies': False,
                'product_title': product.get('title')
            })
    
    except Exception as e:
        logger.error(f"Error analyzing product: {str(e)}")
        logger.exception("Full exception details:")
        return jsonify({'success': False, 'error': str(e)}), 500

@standardizer_bp.route('/api/approve', methods=['POST'])
@login_required
def api_approve_discrepancy():
    """API endpoint to approve and apply standardization changes to a product"""
    # Get product ID and update data from request
    product_id = request.json.get('product_id')
    update_data = request.json.get('update_data', {})
    
    if not product_id:
        return jsonify({'success': False, 'error': 'No product ID provided'}), 400
    
    try:
        # Find the product in the database
        product = shopify_collection.find_one({'_id': ObjectId(product_id)})
        
        if not product:
            return jsonify({'success': False, 'error': 'Product not found'}), 404
        
        # Get the Shopify ID from the id field
        shopify_id = str(product.get('id'))
        if not shopify_id:
            return jsonify({'success': False, 'error': 'Product has no Shopify ID'}), 400
        
        logger.info(f"Found Shopify ID: {shopify_id}")
        
        # Only update if there's data to update
        if update_data:
            # Import the update_shopify_product function directly
            from shopify_utils import update_shopify_product
            
            # Get the user's Shopify credentials from the database
            user_collection = db['user']
            user = user_collection.find_one({"username": current_user.username})
            
            if not user or not user.get('shopifyAccessToken') or not user.get('shopifyStoreName'):
                logger.error(f"No user with username {current_user.username} and Shopify credentials found")
                return jsonify({
                    'success': False,
                    'error': f"No Shopify credentials found for user {current_user.username}"
                }), 400
            
            # Update the product in Shopify
            logger.info(f"Updating product {shopify_id} in Shopify with data: {update_data}")
            result = update_shopify_product(shopify_id, update_data, username=current_user.username)
            
            if result.get('success'):
                # Find any discrepancy records for this product and mark them as repaired
                discrepancy_collection.update_many(
                    {'product_id': ObjectId(product_id), 'username': current_user.username},
                    {'$set': {
                        'status': 'repaired',
                        'repair_timestamp': datetime.now(),
                        'repaired_by': current_user.username
                    }}
                )
                
                return jsonify({
                    'success': True,
                    'message': 'Changes applied and pushed to Shopify successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f"Failed to update Shopify: {result.get('error')}"
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'No update data provided'
            }), 400
    
    except Exception as e:
        logger.error(f"Error approving discrepancy: {str(e)}")
        logger.exception("Full exception details:")
        return jsonify({'success': False, 'error': str(e)}), 500

@standardizer_bp.route('/api/discrepancies')
@login_required
def api_discrepancies():
    """API endpoint to get discrepancies for AJAX requests"""
    # Get filter parameters
    status = request.args.get('status', 'pending')
    issue_type = request.args.get('issue_type', None)
    limit = int(request.args.get('limit', 10))
    
    # Build query - always filter by current user
    query = {
        'status': status,
        'username': current_user.username
    }
    if issue_type:
        query[f'discrepancies.{issue_type}'] = {'$exists': True}
    
    # Get results
    discrepancies = list(discrepancy_collection.find(query)
                         .sort('timestamp', pymongo.DESCENDING)
                         .limit(limit))
    
    # Convert ObjectId to string for JSON serialization
    for disc in discrepancies:
        disc['_id'] = str(disc['_id'])
        if 'product_id' in disc and isinstance(disc['product_id'], ObjectId):
            disc['product_id'] = str(disc['product_id'])
        if 'timestamp' in disc and isinstance(disc['timestamp'], datetime):
            disc['timestamp'] = disc['timestamp'].isoformat()
        if 'review_timestamp' in disc and isinstance(disc['review_timestamp'], datetime):
            disc['review_timestamp'] = disc['review_timestamp'].isoformat()
    
    return jsonify({'discrepancies': discrepancies})
