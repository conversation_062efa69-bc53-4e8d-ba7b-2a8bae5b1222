#!/usr/bin/env python3
"""
Fix Website After Reboot
This script diagnoses and fixes issues with the website not loading after a server reboot.
It checks logs, service status, and attempts to fix any identified problems.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def fix_after_reboot():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to diagnose website issues...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🔍 DIAGNOSING WEBSITE ISSUES AFTER REBOOT")
        print("="*70)
        
        # 1. Check if gunicorn service is running
        print("1. 🔍 Checking if gunicorn service is running...")
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running")
        else:
            print("❌ Gunicorn service is NOT running")
            print("Status output:")
            print(output[:500])
            
            # Try to start the service
            print("\n🔄 Attempting to start gunicorn service...")
            execute_command(ssh_client, "systemctl start gunicorn")
            time.sleep(5)
            
            # Check again
            output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
            if "active (running)" in output:
                print("✅ Successfully started gunicorn service")
            else:
                print("❌ Failed to start gunicorn service")
                print("Status output after attempt:")
                print(output[:500])
        
        # 2. Check gunicorn logs for errors
        print("\n2. 🔍 Checking gunicorn logs for errors...")
        output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --since 'today' | tail -50")
        
        if output:
            print("Recent gunicorn logs from journalctl:")
            print(output)
            
            # Look for common errors
            if "Permission denied" in output:
                print("⚠️ Permission issues detected in logs")
            if "No such file or directory" in output:
                print("⚠️ Missing file or directory detected in logs")
            if "ImportError" in output or "ModuleNotFoundError" in output:
                print("⚠️ Python import errors detected in logs")
            if "BindError" in output:
                print("⚠️ Port binding issues detected in logs")
        else:
            print("No recent gunicorn logs found in journalctl")
        
        # Check gunicorn error log file
        print("\nChecking gunicorn error log file...")
        output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log | tail -50")
        
        if output:
            print("Recent gunicorn error logs:")
            print(output)
        else:
            print("No recent gunicorn error logs found")
        
        # 3. Check if socket file exists
        print("\n3. 🔍 Checking if Unix socket file exists...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/run/gunicorn.sock")
        
        if "No such file" not in error:
            print("✅ Unix socket file exists")
        else:
            print("❌ Unix socket file does not exist")
            
            # Check socket directory
            output, error, code = execute_command(ssh_client, "ls -la /var/www/html/run")
            
            if "No such file" not in error:
                print("Socket directory exists but socket file is missing")
            else:
                print("Socket directory does not exist, creating it...")
                execute_command(ssh_client, "mkdir -p /var/www/html/run")
                execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
                execute_command(ssh_client, "chmod 755 /var/www/html/run")
        
        # 4. Check nginx status
        print("\n4. 🔍 Checking nginx status...")
        output, error, code = execute_command(ssh_client, "systemctl status nginx")
        
        if "active (running)" in output:
            print("✅ Nginx service is running")
        else:
            print("❌ Nginx service is NOT running")
            print("Status output:")
            print(output[:500])
            
            # Try to start nginx
            print("\n🔄 Attempting to start nginx service...")
            execute_command(ssh_client, "systemctl start nginx")
            time.sleep(2)
            
            # Check again
            output, error, code = execute_command(ssh_client, "systemctl status nginx")
            if "active (running)" in output:
                print("✅ Successfully started nginx service")
            else:
                print("❌ Failed to start nginx service")
        
        # 5. Check nginx logs for errors
        print("\n5. 🔍 Checking nginx logs for errors...")
        output, error, code = execute_command(ssh_client, "cat /var/log/nginx/error.log | tail -50")
        
        if output:
            print("Recent nginx error logs:")
            print(output)
            
            # Look for common errors
            if "connect() failed" in output:
                print("⚠️ Nginx cannot connect to gunicorn backend")
            if "Permission denied" in output:
                print("⚠️ Permission issues detected in nginx logs")
            if "No such file or directory" in output:
                print("⚠️ Missing file or directory detected in nginx logs")
        else:
            print("No recent nginx error logs found")
        
        # 6. Check nginx configuration
        print("\n6. 🔍 Checking nginx configuration...")
        output, error, code = execute_command(ssh_client, "nginx -t")
        
        if "successful" in output or code == 0:
            print("✅ Nginx configuration test passed")
        else:
            print("❌ Nginx configuration test failed")
            print(output)
            print(error)
        
        # 7. Check nginx site configuration
        print("\n7. 🔍 Checking nginx site configuration...")
        output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -type f | xargs grep -l 'trollaus.tcgsync.com' 2>/dev/null")
        
        if output:
            print(f"Found site configuration for trollaus.tcgsync.com: {output}")
            
            # Check the configuration
            config_file = output.split('\n')[0]
            output, error, code = execute_command(ssh_client, f"cat {config_file}")
            
            print(f"Site configuration content:")
            print(output)
            
            # Check if it's using the correct socket or port
            if "unix:/var/www/html/run/gunicorn.sock" in output:
                print("✅ Site is configured to use Unix socket")
            elif "127.0.0.1:8000" in output:
                print("⚠️ Site is configured to use TCP port 8000 instead of Unix socket")
            else:
                print("⚠️ Could not determine backend configuration")
        else:
            print("❌ No site configuration found for trollaus.tcgsync.com")
        
        # 8. Check what gunicorn is actually binding to
        print("\n8. 🔍 Checking what gunicorn is binding to...")
        output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep")
        
        if output:
            print("Gunicorn processes:")
            print(output)
            
            # Check if any process is using the Unix socket
            if "unix:" in output:
                print("✅ Gunicorn is using a Unix socket")
            else:
                print("⚠️ Gunicorn may not be using a Unix socket")
                
                # Check if it's binding to a TCP port
                if "127.0.0.1" in output:
                    print("⚠️ Gunicorn is binding to a TCP port")
        else:
            print("❌ No gunicorn processes found running")
        
        # 9. Check gunicorn configuration
        print("\n9. 🔍 Checking gunicorn configuration...")
        output, error, code = execute_command(ssh_client, "cat /var/www/html/gunicorn.conf.py")
        
        if output:
            print("Gunicorn configuration:")
            print(output)
            
            # Check if it's configured to use a Unix socket
            if "unix:" in output:
                print("✅ Gunicorn is configured to use a Unix socket")
                
                # Extract the socket path from configuration
                socket_path = None
                for line in output.split('\n'):
                    if "bind" in line and "unix:" in line:
                        parts = line.split("unix:")
                        if len(parts) > 1:
                            socket_path = parts[1].strip('"\' ')
                            break
                
                if socket_path:
                    print(f"Configured socket path: unix:{socket_path}")
            else:
                print("⚠️ Gunicorn is not configured to use a Unix socket")
        
        # 10. Test connectivity
        print("\n10. 🔍 Testing connectivity...")
        output, error, code = execute_command(ssh_client, "curl -s -I http://localhost")
        
        if "200 OK" in output or "302 Found" in output:
            print("✅ Website is accessible locally")
        else:
            print("❌ Website is NOT accessible locally")
            print("Response:")
            print(output)
        
        # 11. Fix issues based on diagnosis
        print("\n" + "="*70)
        print("🔧 FIXING IDENTIFIED ISSUES")
        print("="*70)
        
        # Fix 1: Ensure socket directory exists with correct permissions
        print("1. 🔧 Ensuring socket directory exists with correct permissions...")
        execute_command(ssh_client, "mkdir -p /var/www/html/run")
        execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
        execute_command(ssh_client, "chmod 755 /var/www/html/run")
        print("✅ Socket directory permissions fixed")
        
        # Fix 2: Ensure log directory exists with correct permissions
        print("\n2. 🔧 Ensuring log directory exists with correct permissions...")
        execute_command(ssh_client, "mkdir -p /var/www/html/logs/app")
        execute_command(ssh_client, "chown -R www-data:www-data /var/www/html/logs")
        execute_command(ssh_client, "chmod -R 755 /var/www/html/logs")
        print("✅ Log directory permissions fixed")
        
        # Fix 3: Update nginx configuration to use the correct socket
        print("\n3. 🔧 Updating nginx configuration...")
        output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -type f | xargs grep -l 'trollaus.tcgsync.com' 2>/dev/null")
        
        if output:
            config_file = output.split('\n')[0]
            
            # Backup the file
            execute_command(ssh_client, f"cp {config_file} {config_file}.bak.$(date +%Y%m%d%H%M%S)")
            
            # Check if it's using proxy_pass to TCP
            output, error, code = execute_command(ssh_client, f"grep -l 'proxy_pass.*http://127.0.0.1:' {config_file}")
            
            if output:
                print("Updating nginx configuration to use Unix socket...")
                
                # Create updated nginx configuration
                socket_path = "/var/www/html/run/gunicorn.sock"
                
                nginx_content = f'''
server {{
    listen 80;
    server_name trollaus.tcgsync.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}}

server {{
    listen 443 ssl;
    server_name trollaus.tcgsync.com;

    ssl_certificate /etc/letsencrypt/live/trollaus.tcgsync.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/trollaus.tcgsync.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    client_max_body_size 5G;

    location / {{
        proxy_pass http://unix:{socket_path};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }}
}}
'''
                
                # Write the new configuration
                nginx_command = f'cat > {config_file} << \'EOF\'\n{nginx_content}\nEOF'
                execute_command(ssh_client, nginx_command)
                print(f"✅ Updated {config_file}")
                
                # Test nginx configuration
                output, error, code = execute_command(ssh_client, "nginx -t")
                
                if code == 0:
                    print("✅ Nginx configuration test passed")
                    
                    # Reload nginx
                    execute_command(ssh_client, "systemctl reload nginx")
                    print("✅ Reloaded nginx with new configuration")
                else:
                    print(f"❌ Nginx configuration test failed: {error}")
                    
                    # Restore backup
                    backup_files = execute_command(ssh_client, f"ls -t {config_file}.bak.* | head -1")[0]
                    if backup_files:
                        backup_file = backup_files.strip()
                        execute_command(ssh_client, f"cp {backup_file} {config_file}")
                        print(f"Restored {config_file} from {backup_file}")
        
        # Fix 4: Update gunicorn configuration
        print("\n4. 🔧 Updating gunicorn configuration...")
        
        # Get CPU count
        output, error, code = execute_command(ssh_client, "nproc")
        cpu_count = int(output.strip()) if output.strip().isdigit() else 4
        
        # Calculate optimal workers
        optimal_workers = cpu_count * 2
        optimal_workers = max(optimal_workers, 4)  # At least 4 workers
        
        socket_path = "/var/www/html/run/gunicorn.sock"
        
        gunicorn_config = f'''
# Ultra-Optimized Gunicorn Configuration with Unix Socket
workers = {optimal_workers}
worker_class = "gevent"
worker_connections = 1000
threads = 2
timeout = 120
keepalive = 5

# Unix socket for better performance
bind = "unix:{socket_path}"

# Request settings
max_requests = 1000
max_requests_jitter = 200
graceful_timeout = 30

# Logging
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"

# Performance optimizations
preload_app = True
worker_tmp_dir = "/dev/shm"

# Prevent thundering herd
forwarded_allow_ips = "*"
secure_scheme_headers = {{"X-Forwarded-Proto": "https"}}

def post_fork(server, worker):
    # Optimize worker settings after fork
    import gevent
    from gevent import monkey
    monkey.patch_all()
    
    # Set worker priority
    import os
    os.nice(10)
'''
        
        config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{gunicorn_config}\nEOF'
        output, error, code = execute_command(ssh_client, config_command)
        
        if code == 0:
            print("✅ Updated gunicorn configuration")
            
            # Set permissions
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
            execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
        else:
            print(f"❌ Failed to update gunicorn configuration: {error}")
        
        # Fix 5: Install gevent if not already installed
        print("\n5. 🔧 Installing gevent for async workers...")
        execute_command(ssh_client, "cd /var/www/html && source venv/bin/activate && pip install gevent")
        print("✅ Gevent installed/updated")
        
        # Fix 6: Restart gunicorn service
        print("\n6. 🔧 Restarting gunicorn service...")
        execute_command(ssh_client, "systemctl restart gunicorn")
        time.sleep(5)
        
        # Check if service is running
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is now running!")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Check if socket file exists
            output, error, code = execute_command(ssh_client, f"ls -la {socket_path}")
            
            if "No such file" not in error:
                print(f"✅ Unix socket file exists: {socket_path}")
                
                # Check socket permissions
                if "www-data" in output:
                    print("✅ Socket has correct ownership")
                else:
                    print("⚠️ Socket may have incorrect ownership, fixing...")
                    execute_command(ssh_client, f"chown www-data:www-data {socket_path}")
            else:
                print(f"❌ Unix socket file does not exist: {socket_path}")
                print("Checking gunicorn logs for errors...")
                
                output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log | tail -20")
                print("Gunicorn error log:")
                print(output)
        else:
            print("❌ Service failed to start")
            print("Status output:")
            print(output[:300])
            
            # Check logs
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -20")
            print("Recent logs:")
            print(output)
        
        # Fix 7: Test website through nginx
        print("\n7. 🧪 Testing website through nginx...")
        
        # Test with curl
        output, error, code = execute_command(ssh_client, "curl -s -I http://localhost/")
        
        if "200 OK" in output or "302 Found" in output:
            print("✅ Website is now accessible locally!")
            
            # Run multiple tests to get average response time
            print("\n8. 📊 Running performance tests...")
            total_time = 0
            tests = 3
            
            for i in range(tests):
                output, error, code = execute_command(ssh_client, "curl -s -w '%{time_total}' -o /dev/null http://localhost/")
                if code == 0:
                    try:
                        response_time = float(output.strip())
                        total_time += response_time
                        print(f"Test {i+1}: {response_time:.3f}s")
                    except:
                        print(f"Test {i+1}: Failed to parse time")
                else:
                    print(f"Test {i+1}: Failed")
                
                time.sleep(1)
            
            if total_time > 0:
                avg_time = total_time / tests
                print(f"\nAverage response time: {avg_time:.3f}s")
                
                if avg_time < 1.0:
                    print("✅ EXCELLENT performance!")
                elif avg_time < 3.0:
                    print("✅ GOOD performance")
                else:
                    print("⚠️ Performance still needs improvement")
        else:
            print("❌ Website is still NOT accessible locally")
            print("Response:")
            print(output)
            
            # Check for additional issues
            print("\nChecking for additional issues...")
            
            # Check if port 80 is open
            output, error, code = execute_command(ssh_client, "netstat -tulpn | grep :80")
            if "nginx" in output:
                print("✅ Nginx is listening on port 80")
            else:
                print("❌ Nginx is NOT listening on port 80")
            
            # Check if port 443 is open
            output, error, code = execute_command(ssh_client, "netstat -tulpn | grep :443")
            if "nginx" in output:
                print("✅ Nginx is listening on port 443")
            else:
                print("❌ Nginx is NOT listening on port 443")
        
        # 12. Final verification and summary
        print("\n" + "="*70)
        print("✅ WEBSITE REPAIR COMPLETE!")
        print("="*70)
        
        # Check for any warnings or errors
        if "❌" in locals().get('output', ''):
            print("⚠️ Some issues were detected that may still affect the website")
            print("Please review the output above and fix any remaining issues")
            
            print("\nPossible next steps:")
            print("1. Check firewall settings: 'ufw status' and ensure ports 80/443 are open")
            print("2. Check DNS settings for trollaus.tcgsync.com")
            print("3. Check SSL certificate validity: 'certbot certificates'")
            print("4. Manually restart both nginx and gunicorn: 'systemctl restart nginx gunicorn'")
            print("5. Check application code for errors in wsgi.py or app.py")
        else:
            print("✅ All checks passed! Website should be accessible now")
            print("The following has been fixed:")
            print("1. ✅ Socket directory permissions")
            print("2. ✅ Log directory permissions")
            print("3. ✅ Nginx configuration updated to use Unix socket")
            print("4. ✅ Gunicorn configuration optimized")
            print("5. ✅ Gevent installed for async workers")
            print("6. ✅ Gunicorn service restarted")
            print("7. ✅ Website accessibility verified")
        
        print("\nTo manage the services, use these commands:")
        print("- Check gunicorn status: systemctl status gunicorn")
        print("- Check nginx status: systemctl status nginx")
        print("- Restart both services: systemctl restart nginx gunicorn")
        print("- View gunicorn logs: journalctl -u gunicorn -f")
        print("- View nginx logs: tail -f /var/log/nginx/error.log")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    fix_after_reboot()
