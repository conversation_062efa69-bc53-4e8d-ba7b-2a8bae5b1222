# Script to pull marketplace listings from CardTrader API for matched IDs with valid TCGPlayer IDs
import asyncio
import aiohttp
import time
import random
import motor.motor_asyncio
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import argparse
import json
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# Async MongoDB client
motor_client = motor.motor_asyncio.AsyncIOMotorClient(mongo_uri)
motor_db = motor_client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Enhanced rate limiting with adaptive behavior
class AdaptiveRateLimiter:
    def __init__(self, initial_rate_limit=9, min_rate_limit=5, max_rate_limit=15):
        self.rate_limit = initial_rate_limit  # requests per second
        self.min_rate_limit = min_rate_limit
        self.max_rate_limit = max_rate_limit
        self.tokens = initial_rate_limit
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        self.consecutive_failures = 0
        self.consecutive_successes = 0
        self.backoff_time = 1.0  # Initial backoff time in seconds
        self.max_backoff_time = 10.0  # Maximum backoff time
    
    async def acquire(self):
        async with self.lock:
            now = time.time()
            time_passed = now - self.last_update
            self.tokens = min(self.rate_limit, self.tokens + time_passed * self.rate_limit)
            self.last_update = now
            
            if self.tokens < 1:
                # Not enough tokens, calculate sleep time
                sleep_time = (1 - self.tokens) / self.rate_limit
                await asyncio.sleep(sleep_time)
                self.tokens = 0
                self.last_update = time.time()
            else:
                self.tokens -= 1
    
    def report_success(self):
        """Report a successful API call to adjust rate limits"""
        self.consecutive_failures = 0
        self.consecutive_successes += 1
        self.backoff_time = max(1.0, self.backoff_time * 0.8)  # Reduce backoff time
        
        # Increase rate limit after several consecutive successes
        if self.consecutive_successes >= 10 and self.rate_limit < self.max_rate_limit:
            self.rate_limit = min(self.max_rate_limit, self.rate_limit + 0.5)
            logger.info(f"Increasing rate limit to {self.rate_limit} req/sec after {self.consecutive_successes} consecutive successes")
            self.consecutive_successes = 0
    
    def report_failure(self):
        """Report a failed API call (rate limited) to adjust rate limits"""
        self.consecutive_successes = 0
        self.consecutive_failures += 1
        
        # Implement exponential backoff
        if self.consecutive_failures > 0:
            self.backoff_time = min(self.max_backoff_time, self.backoff_time * 1.5)
            
        # Decrease rate limit after consecutive failures
        if self.consecutive_failures >= 3 and self.rate_limit > self.min_rate_limit:
            self.rate_limit = max(self.min_rate_limit, self.rate_limit - 1.0)
            logger.info(f"Decreasing rate limit to {self.rate_limit} req/sec after {self.consecutive_failures} consecutive failures")
    
    async def backoff(self):
        """Wait according to the current backoff strategy"""
        await asyncio.sleep(self.backoff_time)
        logger.info(f"Backing off for {self.backoff_time:.2f} seconds")

# Global rate limiter
rate_limiter = None  # Will be initialized in main()

def get_matched_blueprints(count=10, random_sample=True, num_threads=4):
    """Get blueprints from the matchedIds collection with valid TCGPlayer IDs"""
    logger.info(f"Fetching matched blueprints with valid TCGPlayer IDs using {num_threads} threads...")
    
    # Check if matchedIds collection exists
    if 'matchedIds' not in cardtrader_db.list_collection_names():
        logger.error("matchedIds collection not found in cardtrader database")
        return []
    
    # Build the query to get only records with valid TCGPlayer IDs
    query = {
        "tcg_player_id": {"$exists": True, "$ne": None, "$ne": "", "$ne": "None"}
    }
    
    # Count total matched blueprints first (faster than getting all documents)
    total_matched = cardtrader_db.matchedIds.count_documents(query)
    logger.info(f"Found {total_matched} matched blueprints with valid TCGPlayer IDs")
    
    if total_matched == 0:
        return []
    
    # If we need all blueprints or more than exist, adjust count
    if count <= 0 or count > total_matched:
        count = total_matched
        logger.info(f"Will process all {count} matched blueprints")
    
    # For random sampling, we'll use aggregation with $sample
    if random_sample and count < total_matched:
        pipeline = [
            {"$match": query},
            {"$sample": {"size": count}}
        ]
        matched_blueprints = list(cardtrader_db.matchedIds.aggregate(pipeline))
        logger.info(f"Selected {len(matched_blueprints)} random matched blueprints")
    else:
        # For sequential processing, use find with limit
        matched_blueprints = list(cardtrader_db.matchedIds.find(query).limit(count))
        logger.info(f"Selected {len(matched_blueprints)} matched blueprints")
    
    # Convert to the format expected by the rest of the code
    blueprints = []
    for match in matched_blueprints:
        blueprint = {
            "id": match.get("blueprint_id"),
            "tcg_player_id": match.get("tcg_player_id"),
            "scryfall_id": match.get("scryfall_id"),
            "card_market_id": match.get("card_market_id")
        }
        blueprints.append(blueprint)
    
    return blueprints

def extract_market_summary(listings):
    """Extract summary market data from listings"""
    if not listings:
        return {
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None
            }
        }
    
    # Calculate total quantity
    total_quantity = sum(listing.get("quantity", 0) for listing in listings)
    
    # Extract prices (in cents)
    prices = []
    for listing in listings:
        price_info = listing.get("price", {})
        if isinstance(price_info, dict) and "cents" in price_info:
            # Convert cents to dollars/euros for easier reading
            price = float(price_info["cents"]) / 100
            prices.append(price)
    
    # Calculate price statistics
    price_data = {
        "min": min(prices) if prices else None,
        "max": max(prices) if prices else None,
        "avg": sum(prices) / len(prices) if prices else None,
        "currency": listings[0].get("price", {}).get("currency", "EUR") if listings else "EUR"
    }
    
    return {
        "total_quantity": total_quantity,
        "price_data": price_data
    }

async def fetch_marketplace_listings(session, blueprint_id, max_retries=3):
    """Fetch marketplace listings for a specific blueprint ID using aiohttp"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"blueprint_id": blueprint_id}
    
    # Acquire token from rate limiter
    await rate_limiter.acquire()
    
    try:
        logger.info(f"Fetching marketplace listings for blueprint ID: {blueprint_id}")
        
        # Make the API request
        async with session.get(url, params=params, headers=HEADERS, timeout=10) as response:
            if response.status == 200:
                data = await response.json()
                # The response is an object with blueprint IDs as keys and arrays of products as values
                listings = data.get(str(blueprint_id), [])
                logger.info(f"Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                rate_limiter.report_success()
                return listings
            elif response.status == 429:
                # Rate limited, wait and retry
                error_text = await response.text()
                logger.warning(f"Rate limited: {error_text}")
                rate_limiter.report_failure()
                await rate_limiter.backoff()
                
                # Retry with exponential backoff
                for retry in range(max_retries):
                    await rate_limiter.acquire()
                    logger.info(f"Retry {retry+1}/{max_retries} for blueprint ID: {blueprint_id}")
                    
                    async with session.get(url, params=params, headers=HEADERS, timeout=10) as retry_response:
                        if retry_response.status == 200:
                            data = await retry_response.json()
                            listings = data.get(str(blueprint_id), [])
                            logger.info(f"Retry successful. Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                            rate_limiter.report_success()
                            return listings
                        elif retry_response.status == 429:
                            error_text = await retry_response.text()
                            logger.warning(f"Still rate limited on retry {retry+1}: {error_text}")
                            rate_limiter.report_failure()
                            # Increase backoff time for next retry
                            await rate_limiter.backoff()
                        else:
                            error_text = await retry_response.text()
                            logger.error(f"Retry {retry+1} failed with status code {retry_response.status}: {error_text}")
                            
                logger.error(f"All retries failed for blueprint ID {blueprint_id}")
                return []
            else:
                error_text = await response.text()
                logger.error(f"API request failed with status code {response.status}: {error_text}")
                return []
    except aiohttp.ClientError as e:
        logger.error(f"Client error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        # Retry once after a short delay
        await asyncio.sleep(1)
        try:
            logger.info(f"Retrying after error for blueprint ID: {blueprint_id}")
            await rate_limiter.acquire()
            async with session.get(url, params=params, headers=HEADERS, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    listings = data.get(str(blueprint_id), [])
                    logger.info(f"Retry successful. Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                    rate_limiter.report_success()
                    return listings
                else:
                    error_text = await response.text()
                    logger.error(f"Retry failed with status code {response.status}: {error_text}")
                    return []
        except Exception as retry_e:
            logger.error(f"Retry also failed for blueprint ID {blueprint_id}: {str(retry_e)}")
            return []
    except Exception as e:
        logger.error(f"Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        return []

async def save_to_marketprices(blueprint, listings):
    """Save the marketplace listings summary to the marketprices collection using motor"""
    if not listings:
        logger.info(f"No listings to save for blueprint ID: {blueprint.get('id')}")
        return
    
    # Extract market summary data
    market_summary = extract_market_summary(listings)
    
    # Create a document to insert
    document = {
        "blueprint_id": blueprint.get("id"),
        "tcg_player_id": blueprint.get("tcg_player_id"),
        "scryfall_id": blueprint.get("scryfall_id"),
        "card_market_id": blueprint.get("card_market_id"),
        "total_quantity": market_summary["total_quantity"],
        "price_data": market_summary["price_data"],
        "listings_count": len(listings),  # Store the count of listings for reference
        "fetched_at": datetime.now()
    }
    
    try:
        # Insert or update the document in the marketprices collection
        result = await motor_db.marketprices.update_one(
            {"blueprint_id": blueprint.get("id")},
            {"$set": document},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Inserted new document for blueprint ID: {blueprint.get('id')}")
        else:
            logger.info(f"Updated existing document for blueprint ID: {blueprint.get('id')}")
    except Exception as e:
        logger.error(f"Error saving to marketprices collection: {str(e)}")

async def process_blueprint(session, blueprint, batch_results, semaphore):
    """Process a single blueprint asynchronously with semaphore for concurrency control"""
    blueprint_id = blueprint.get('id')
    
    async with semaphore:
        try:
            # Fetch marketplace listings
            listings = await fetch_marketplace_listings(session, blueprint_id)
            
            # Add result to batch
            batch_results.append((blueprint, listings))
            
            logger.info(f"Completed: Blueprint ID: {blueprint_id}")
            return True
        except Exception as e:
            logger.error(f"Error processing blueprint {blueprint_id}: {str(e)}")
            return False

async def process_batch(session, blueprints, concurrency, batch_size=100):
    """Process a batch of blueprints and save results"""
    batch_results = []
    tasks = []
    
    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(concurrency)
    
    # Create tasks for all blueprints in the batch
    for blueprint in blueprints:
        task = asyncio.create_task(process_blueprint(session, blueprint, batch_results, semaphore))
        tasks.append(task)
    
    # Wait for all tasks to complete
    await asyncio.gather(*tasks)
    
    # Process and save results
    save_tasks = []
    for blueprint, listings in batch_results:
        save_task = asyncio.create_task(save_to_marketprices(blueprint, listings))
        save_tasks.append(save_task)
    
    # Wait for all save tasks to complete
    await asyncio.gather(*save_tasks)
    
    return len(batch_results)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull marketplace listings from CardTrader API for matched IDs with valid TCGPlayer IDs')
    parser.add_argument('--blueprints', type=int, default=10,
                        help='Number of blueprints to process (default: 10, use 0 for all matching blueprints)')
    parser.add_argument('--concurrency', type=int, default=15,
                        help='Maximum number of concurrent requests (default: 15)')
    parser.add_argument('--batch-size', type=int, default=200,
                        help='Batch size for processing blueprints (default: 200)')
    parser.add_argument('--random', action='store_true', default=True,
                        help='Select random blueprints (default: True)')
    parser.add_argument('--rate-limit', type=float, default=10.0,
                        help='Initial API request rate limit in requests per second (default: 10.0)')
    parser.add_argument('--threads', type=int, default=4,
                        help='Number of threads for database operations (default: 4)')
    parser.add_argument('--batch-delay', type=float, default=1.0,
                        help='Delay between batches in seconds (default: 1.0)')
    return parser.parse_args()

async def main_async():
    # Parse command line arguments
    args = parse_arguments()
    
    # Initialize adaptive rate limiter
    global rate_limiter
    rate_limiter = AdaptiveRateLimiter(initial_rate_limit=args.rate_limit)
    
    start_time = time.time()
    logger.info(f"Starting CardTrader marketplace data collection for matched IDs with valid TCGPlayer IDs")
    logger.info(f"Configuration: {args.blueprints} blueprints, {args.concurrency} concurrency, {args.batch_size} batch size, {args.rate_limit} req/sec, {args.threads} threads")
    
    # Get matched blueprints with valid TCGPlayer IDs
    blueprints = get_matched_blueprints(args.blueprints, args.random, args.threads)
    
    if not blueprints:
        logger.error("No matched blueprints found. Exiting.")
        return
    
    # Create marketprices collection if it doesn't exist
    if 'marketprices' not in cardtrader_db.list_collection_names():
        logger.info("Creating marketprices collection")
        cardtrader_db.create_collection('marketprices')
    
    # Process blueprints in batches
    total_processed = 0
    total_listings = 0
    
    # Create a ClientSession that will be used for all requests
    # Increase the limit and disable SSL verification for better performance
    connector = aiohttp.TCPConnector(limit=args.concurrency * 2, ssl=False)
    timeout = aiohttp.ClientTimeout(total=30, connect=10, sock_connect=10, sock_read=10)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        # Process blueprints in batches to avoid memory issues with large datasets
        for i in range(0, len(blueprints), args.batch_size):
            batch = blueprints[i:i+args.batch_size]
            logger.info(f"Processing batch {i//args.batch_size + 1}/{(len(blueprints) + args.batch_size - 1)//args.batch_size} ({len(batch)} blueprints)")
            
            batch_start_time = time.time()
            processed = await process_batch(session, batch, args.concurrency, args.batch_size)
            batch_end_time = time.time()
            
            total_processed += processed
            
            # Log progress
            logger.info(f"Batch completed in {batch_end_time - batch_start_time:.2f} seconds")
            logger.info(f"Progress: {total_processed}/{len(blueprints)} blueprints processed ({total_processed/len(blueprints)*100:.1f}%)")
            
            # Add a small delay between batches to avoid overwhelming the API
            if i + args.batch_size < len(blueprints):
                logger.info(f"Waiting {args.batch_delay} seconds before next batch...")
                await asyncio.sleep(args.batch_delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Count documents and total listings in marketprices collection
    marketprices_count = cardtrader_db.marketprices.count_documents({})
    
    # Calculate listings by summing listings_count field
    pipeline = [
        {"$group": {"_id": None, "total_listings": {"$sum": "$listings_count"}}}
    ]
    result = list(cardtrader_db.marketprices.aggregate(pipeline))
    total_listings = result[0]["total_listings"] if result else 0
    
    # Count total matched blueprints with valid TCGPlayer IDs
    total_matched = cardtrader_db.matchedIds.count_documents({
        "tcg_player_id": {"$exists": True, "$ne": None, "$ne": "", "$ne": "None"}
    })
    
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {total_processed} blueprints")
    logger.info(f"Found {total_listings} total marketplace listings")
    logger.info(f"Average processing time: {duration/total_processed:.2f} seconds per blueprint")
    logger.info(f"Processing rate: {total_processed/duration:.2f} blueprints per second")
    logger.info(f"Estimated time for all {total_matched} matched blueprints: {total_matched/(total_processed/duration)/3600:.2f} hours")
    logger.info(f"Total documents in marketprices collection: {marketprices_count}")

def main():
    """Entry point for the script"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
