from flask import session, request, redirect, url_for
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def session_recovery_middleware(app):
    """
    Middleware to handle corrupted sessions by clearing them and redirecting to login.
    This prevents 500 errors when session data is corrupted.
    """
    @app.before_request
    def handle_session_errors():
        try:
            # Try to access the session to see if it's valid
            # This will trigger an exception if the session is corrupted
            _ = session.get('test_session_validity')
        except Exception as e:
            # Log the error
            logger.error(f"Session error detected: {str(e)}")
            logger.error(f"Request path: {request.path}")

            # Clear all cookies
            response = redirect('/auth/login')

            # Clear session cookie
            if 'session' in request.cookies:
                response.delete_cookie('session')

            # Clear remember cookie
            if 'remember_token' in request.cookies:
                response.delete_cookie('remember_token')

            # Add a message to inform the user
            from flask import flash
            flash('Your session has been reset due to an error. Please log in again.', 'warning')

            return response

        return None
