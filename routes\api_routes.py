import logging
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from bson import ObjectId
import requests
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

# Create blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

def get_mongo_client():
    """Get MongoDB client from app context"""
    from flask import current_app
    return current_app.mongo_client

def get_db_name():
    """Get database name from app config"""
    from flask import current_app
    return current_app.config.get('MONGO_DBNAME', 'test')

@api_bp.route('/repair-image', methods=['POST'])
@login_required
def repair_image():
    """Repair image for a single product - direct API endpoint"""
    logger.info("Direct API repair-image endpoint called")

    data = request.json
    product_id = data.get('product_id')

    if not product_id:
        logger.error("Product ID is required but was not provided")
        return jsonify({'error': 'Product ID is required'}), 400

    logger.info(f"Processing image repair for product ID: {product_id}")

    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']
    catalog_collection = db['catalog']
    user_collection = db['user']

    # Get product from MongoDB
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        logger.error(f"Product not found for ID: {product_id}")
        return jsonify({'error': 'Product not found'}), 404

    # Check if the product already has an image
    image_url = None

    # Check for image in different formats
    if 'image' in product:
        # Format 1: image is an object with src property
        if isinstance(product['image'], dict) and 'src' in product['image']:
            image_url = product['image']['src']
            logger.info(f"Using existing image URL from product['image']['src']: {image_url}")
        # Format 2: image is a string
        elif isinstance(product['image'], str):
            image_url = product['image']
            logger.info(f"Using existing image URL from product['image'] string: {image_url}")

    # If no image found in product, try to get it from catalog
    if not image_url:
        # Get TCGPlayer ID
        tcg_product_id = product.get('productId')
        if not tcg_product_id:
            logger.error(f"Product does not have a TCGPlayer ID: {product_id}")
            return jsonify({'error': 'Product does not have a TCGPlayer ID'}), 400

        # Look up catalog item
        catalog_item = catalog_collection.find_one({'productId': int(tcg_product_id)})
        if not catalog_item:
            logger.error(f"Catalog item not found for TCGPlayer ID: {tcg_product_id}")
            return jsonify({'error': 'Catalog item not found'}), 404

        # Get image URL from catalog
        image_url = catalog_item.get('image')
        if not image_url:
            # Try alternative fields
            image_url = catalog_item.get('imageUrl')
            if not image_url:
                logger.error(f"No image URL found in catalog for TCGPlayer ID: {tcg_product_id}")
                return jsonify({'error': 'No image URL found in catalog'}), 404

    logger.info(f"Found image URL: {image_url}")

    # Update product in MongoDB
    result = shopify_collection.update_one(
        {'_id': ObjectId(product_id)},
        {'$set': {
            'image': {'src': image_url},
            'needsPushing': True,
            'last_updated': datetime.now(timezone.utc)
        }}
    )

    if result.modified_count == 0:
        logger.error(f"Failed to update product in MongoDB: {product_id}")
        return jsonify({'error': 'Failed to update product'}), 500

    # Get user profile for Shopify credentials
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        logger.error(f"User profile not found for username: {current_user.username}")
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile.get('shopifyStoreName')
    shopify_access_token = user_profile.get('shopifyAccessToken')

    if not shopify_store_name or not shopify_access_token:
        logger.error(f"Shopify credentials not found for user: {current_user.username}")
        return jsonify({'error': 'Shopify credentials not found'}), 400

    # Get Shopify product ID
    shopify_id = product.get('id')
    if not shopify_id:
        logger.error(f"Shopify ID not found for product: {product_id}")
        return jsonify({'error': 'Shopify ID not found'}), 400

    # Prepare product data for Shopify
    product_data = {
        'product': {
            'id': shopify_id,
            'images': [{'src': image_url}]
        }
    }

    # Push to Shopify
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopify_access_token
    }

    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_id}.json"
    logger.info(f"Pushing to Shopify URL: {url}")

    try:
        response = requests.put(url, headers=headers, json=product_data)
        logger.info(f"Shopify API response status: {response.status_code}")

        if response.status_code == 200:
            # Verify the image was actually added to the product
            shopify_response = response.json()

            # Even if Shopify doesn't return images in the response, consider it a success
            # as long as the API call was successful (status code 200)
            # This is because some Shopify stores might have different response formats

            # Update the local database regardless of Shopify response content
            shopify_collection.update_one(
                {'_id': ObjectId(product_id)},
                {'$set': {
                    'needsPushing': False,
                    'shopify_image_verified': True,
                    'last_updated': datetime.now(timezone.utc)
                }}
            )

            # Log the response for debugging
            logger.info(f"Shopify API response for product {product_id}: {shopify_response}")

            # Check if we can extract an image URL from the response
            shopify_image_url = ""
            if 'product' in shopify_response and 'images' in shopify_response['product'] and len(shopify_response['product']['images']) > 0:
                shopify_image_url = shopify_response['product']['images'][0].get('src', '')
                logger.info(f"Found image URL in Shopify response: {shopify_image_url}")
            else:
                # Even if no image is found in the response, we'll use the one we sent
                logger.warning(f"No images found in Shopify response for product {product_id}, using original image URL")
                shopify_image_url = image_url

            logger.info(f"Image repair successful for product: {product_id}")
            # Return success
            return jsonify({
                'success': True,
                'message': 'Image repair request sent to Shopify successfully',
                'image_url': image_url,
                'shopify_image_url': shopify_image_url or image_url
            })
        else:
            logger.error(f"Failed to push to Shopify: {response.text}")
            return jsonify({
                'success': False,
                'message': f'Failed to push to Shopify: {response.text}',
                'image_url': image_url,
                'status_code': response.status_code
            }), 500
    except Exception as e:
        logger.error(f"Error pushing to Shopify: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error pushing to Shopify: {str(e)}',
            'image_url': image_url
        }), 500
