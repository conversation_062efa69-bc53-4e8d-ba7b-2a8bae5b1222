from flask import request, redirect, render_template, url_for
from flask_login import current_user
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def enterprise_subscription_required(f):
    """
    Middleware to check if a user has an Enterprise subscription when accessing enterprise.tcgsync.com
    or when testing with enterprise in the URL path.
    If not, it shows a message with options to upgrade or redirect to the standard system.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Add detailed logging for debugging
        logger.info(f"Enterprise middleware check - Path: {request.path}, Host: {request.host}, User: {current_user.is_authenticated and current_user.username or 'Not authenticated'}")

        # Check if we're on enterprise domain or if 'enterprise' is in the path
        # Make sure to check for exact enterprise path match to avoid false positives
        is_enterprise_context = request.host.startswith('enterprise.tcgsync.com') or request.path.startswith('/enterprise')
        logger.info(f"Is enterprise context: {is_enterprise_context}")

        # Skip check if not in enterprise context
        if not is_enterprise_context:
            logger.info("Not in enterprise context, skipping check")
            return f(*args, **kwargs)

        # Skip check if user is not authenticated yet (let them log in first)
        if not current_user.is_authenticated:
            logger.info("User not authenticated, skipping check")
            return f(*args, **kwargs)

        # Get subscription name
        subscription_name = current_user.get_subscription_name()
        logger.info(f"User subscription: {subscription_name}")

        # Allow access for Enterprise subscription
        if subscription_name == 'Enterprise':
            logger.info(f"User {current_user.username} has Enterprise subscription, allowing access")
            return f(*args, **kwargs)

        # For all subscription types other than Enterprise, show the subscription warning page
        logger.warning(f"Non-Enterprise user {current_user.username} with {subscription_name} subscription attempted to access enterprise context")

        # Always use the standard login URL for consistency
        standard_login_url = "https://login.tcgsync.com"

        logger.info(f"Showing enterprise subscription required page, redirect URL: {standard_login_url}")
        return render_template(
            'enterprise_subscription_required.html',
            username=current_user.username,
            subscription=subscription_name,
            standard_login_url=standard_login_url
        )

    return decorated_function
