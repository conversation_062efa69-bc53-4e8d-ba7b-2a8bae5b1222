# Script to pull marketplace listings from CardTrader API for random blueprints using threading
import requests
import time
import random
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import threading
import queue
import argparse
from concurrent.futures import ThreadPoolExecutor
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Create a session with retry capabilities
def create_session():
    """Create a requests session with retry capabilities"""
    session = requests.Session()
    retry_strategy = Retry(
        total=3,  # Maximum number of retries
        backoff_factor=1,  # Time factor between retries
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry on
        allowed_methods=["GET"]  # Only retry on GET requests
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session

def get_blueprints_by_game(game_id=None, count=10, random=True):
    """Get blueprints from the database, optionally filtered by game_id"""
    logger.info(f"Fetching blueprints from database...")
    
    # Check if blueprints collection exists
    if 'blueprints' not in cardtrader_db.list_collection_names():
        logger.error("blueprints collection not found in cardtrader database")
        return []
    
    # Build the query
    query = {"id": {"$exists": True}}  # Ensure id field exists
    if game_id is not None:
        query["game_id"] = game_id
        logger.info(f"Filtering blueprints by game_id: {game_id}")
    
    # Count matching blueprints
    total_blueprints = cardtrader_db.blueprints.count_documents(query)
    logger.info(f"Found {total_blueprints} matching blueprints in database")
    
    if total_blueprints == 0:
        return []
    
    # Limit the number of blueprints to process if specified
    if count > 0 and count < total_blueprints:
        if random:
            # Get random sample
            pipeline = [
                {"$match": query},
                {"$sample": {"size": count}}
            ]
            blueprints = list(cardtrader_db.blueprints.aggregate(pipeline))
            logger.info(f"Selected {len(blueprints)} random blueprints")
        else:
            # Get first N blueprints
            blueprints = list(cardtrader_db.blueprints.find(query).limit(count))
            logger.info(f"Selected first {len(blueprints)} blueprints")
    else:
        # Get all matching blueprints
        blueprints = list(cardtrader_db.blueprints.find(query))
        logger.info(f"Selected all {len(blueprints)} matching blueprints")
    
    return blueprints

def fetch_marketplace_listings(blueprint_id, session=None):
    """Fetch marketplace listings for a specific blueprint ID"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"blueprint_id": blueprint_id}
    
    # Use the provided session or create a new one
    if session is None:
        session = create_session()
    
    try:
        logger.info(f"Fetching marketplace listings for blueprint ID: {blueprint_id}")
        response = session.get(url, headers=HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            # The response is an object with blueprint IDs as keys and arrays of products as values
            listings = data.get(str(blueprint_id), [])
            logger.info(f"Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
            return listings
        else:
            logger.error(f"API request failed with status code {response.status_code}: {response.text}")
            return []
    except requests.exceptions.SSLError as e:
        logger.error(f"SSL Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        # Sleep and retry once more
        time.sleep(2)
        try:
            logger.info(f"Retrying after SSL error for blueprint ID: {blueprint_id}")
            response = session.get(url, headers=HEADERS, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                listings = data.get(str(blueprint_id), [])
                logger.info(f"Retry successful. Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                return listings
            else:
                logger.error(f"Retry failed with status code {response.status_code}")
                return []
        except Exception as retry_e:
            logger.error(f"Retry also failed for blueprint ID {blueprint_id}: {str(retry_e)}")
            return []
    except Exception as e:
        logger.error(f"Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        return []

def extract_market_summary(listings):
    """Extract summary market data from listings"""
    if not listings:
        return {
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None
            }
        }
    
    # Calculate total quantity
    total_quantity = sum(listing.get("quantity", 0) for listing in listings)
    
    # Extract prices (in cents)
    prices = []
    for listing in listings:
        price_info = listing.get("price", {})
        if isinstance(price_info, dict) and "cents" in price_info:
            # Convert cents to dollars/euros for easier reading
            price = float(price_info["cents"]) / 100
            prices.append(price)
    
    # Calculate price statistics
    price_data = {
        "min": min(prices) if prices else None,
        "max": max(prices) if prices else None,
        "avg": sum(prices) / len(prices) if prices else None,
        "currency": listings[0].get("price", {}).get("currency", "EUR") if listings else "EUR"
    }
    
    return {
        "total_quantity": total_quantity,
        "price_data": price_data
    }

def save_to_marketprices(blueprint, listings):
    """Save the marketplace listings summary to the marketprices collection"""
    if not listings:
        logger.info(f"No listings to save for blueprint ID: {blueprint.get('id')}")
        return
    
    # Extract market summary data
    market_summary = extract_market_summary(listings)
    
    # Create a document to insert
    document = {
        "blueprint_id": blueprint.get("id"),
        "blueprint_name": blueprint.get("name"),
        "expansion_id": blueprint.get("expansion_id"),
        "game_id": blueprint.get("game_id"),
        "total_quantity": market_summary["total_quantity"],
        "price_data": market_summary["price_data"],
        "listings_count": len(listings),  # Store the count of listings for reference
        "fetched_at": datetime.now()
    }
    
    try:
        # Insert or update the document in the marketprices collection
        result = cardtrader_db.marketprices.update_one(
            {"blueprint_id": blueprint.get("id")},
            {"$set": document},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Inserted new document for blueprint ID: {blueprint.get('id')}")
        else:
            logger.info(f"Updated existing document for blueprint ID: {blueprint.get('id')}")
    except Exception as e:
        logger.error(f"Error saving to marketprices collection: {str(e)}")

# Thread-safe rate limiter
class RateLimiter:
    def __init__(self, rate=1):
        """Initialize rate limiter with rate in requests per second"""
        self.rate = rate  # requests per second
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait(self):
        """Wait if necessary to maintain the rate limit"""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            time_to_wait = (1.0 / self.rate) - time_since_last
            
            if time_to_wait > 0:
                time.sleep(time_to_wait)
            
            self.last_request_time = time.time()

# Global rate limiter - CardTrader API is limited to 1 call per second
# We'll use a conservative rate to avoid SSL errors
rate_limiter = RateLimiter(rate=1)  # 1 request per second

def process_blueprint(blueprint, result_queue, session):
    """Process a single blueprint in a thread"""
    blueprint_id = blueprint.get('id')
    blueprint_name = blueprint.get('name', 'Unknown')
    
    logger.info(f"Processing: {blueprint_name} (ID: {blueprint_id})")
    
    # Wait for rate limiter before making API request
    rate_limiter.wait()
    
    # Fetch marketplace listings
    listings = fetch_marketplace_listings(blueprint_id, session)
    
    # Add result to queue
    result_queue.put((blueprint, listings))
    
    logger.info(f"Completed: {blueprint_name} (ID: {blueprint_id})")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull marketplace listings from CardTrader API')
    parser.add_argument('--blueprints', type=int, default=10,
                        help='Number of blueprints to process (default: 10, use 0 for all matching blueprints)')
    parser.add_argument('--threads', type=int, default=5,
                        help='Number of threads to use (default: 5)')
    parser.add_argument('--rate', type=float, default=1.0,
                        help='API request rate limit in requests per second (default: 1.0)')
    parser.add_argument('--game', type=int, default=None,
                        help='Filter blueprints by game_id (default: None)')
    parser.add_argument('--random', action='store_true', default=True,
                        help='Select random blueprints (default: True)')
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Update rate limiter based on command line argument
    global rate_limiter
    rate_limiter = RateLimiter(rate=args.rate)
    
    start_time = time.time()
    logger.info(f"Starting CardTrader marketplace data collection with threading")
    logger.info(f"Configuration: {args.blueprints} blueprints, {args.threads} threads, {args.rate} req/sec, game_id: {args.game}")
    
    # Get blueprints
    blueprints = get_blueprints_by_game(args.game, args.blueprints, args.random)
    
    if not blueprints:
        logger.error("No blueprints found. Exiting.")
        return
    
    # Create marketprices collection if it doesn't exist
    if 'marketprices' not in cardtrader_db.list_collection_names():
        logger.info("Creating marketprices collection")
        cardtrader_db.create_collection('marketprices')
    
    # Create a queue for results
    result_queue = queue.Queue()
    
    # Determine number of threads (adjust based on your system capabilities)
    # We'll use a smaller number to avoid overwhelming the API
    num_threads = min(args.threads, len(blueprints))
    logger.info(f"Using {num_threads} threads to process {len(blueprints)} blueprints")
    
    # Create a session for each thread
    sessions = [create_session() for _ in range(num_threads)]
    
    # Process blueprints using thread pool
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Submit all tasks
        futures = []
        for i, blueprint in enumerate(blueprints):
            # Use the session for this thread
            session_index = i % num_threads
            futures.append(executor.submit(process_blueprint, blueprint, result_queue, sessions[session_index]))
    
    # Process all results from the queue
    processed_count = 0
    listings_count = 0
    successful_count = 0
    
    while not result_queue.empty():
        blueprint, listings = result_queue.get()
        if listings:
            save_to_marketprices(blueprint, listings)
            listings_count += len(listings)
            successful_count += 1
        processed_count += 1
        
        # Print progress
        if processed_count % 10 == 0 or processed_count == len(blueprints):
            logger.info(f"Progress: {processed_count}/{len(blueprints)} blueprints processed")
    
    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {processed_count} blueprints")
    logger.info(f"Successfully retrieved data for {successful_count}/{processed_count} blueprints")
    logger.info(f"Found {listings_count} total marketplace listings")
    logger.info(f"Average processing time: {duration/processed_count:.2f} seconds per blueprint")
    
    # Count documents in marketprices collection
    marketprices_count = cardtrader_db.marketprices.count_documents({})
    logger.info(f"Total documents in marketprices collection: {marketprices_count}")

if __name__ == "__main__":
    main()
