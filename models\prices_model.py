
from mongoengine import Document, IntField, FloatField, StringField, DateTimeField, DictField

class prices(Document):
    # Product identification
    productId = IntField(required=True)
    subTypeName = StringField(required=True)
    
    # Store all price fields in a dictionary to avoid schema validation issues
    priceData = DictField()

    meta = {
        'collection': 'prices',
        'allow_inheritance': True,
        'indexes': [
            {'fields': ['productId', 'subTypeName']},
        ]
    }

    def __init__(self, *args, **values):
        # Move any unknown fields into priceData
        known_fields = {'productId', 'subTypeName', 'priceData'}
        price_data = {}
        
        # Extract unknown fields into price_data
        field_names = list(values.keys())
        for field in field_names:
            if field not in known_fields:
                price_data[field] = values.pop(field)
        
        # Add price_data to values if we found any
        if price_data:
            values['priceData'] = price_data
            
        super().__init__(*args, **values)
