from mongoengine import (
    Document, StringField, DictField, FloatField, ListField, 
    BooleanField, IntField, EmbeddedDocument, EmbeddedDocumentListField
)

class InventoryModifierRule(EmbeddedDocument):
    min_value = IntField(required=True, min_value=0)
    max_value = IntField(required=True, min_value=1)
    percentage = IntField(required=True, min_value=0, max_value=100)

class UserSettings(Document):
    # Language settings
    langPercent = DictField(default={})  # Store language percentages (e.g., {'JP': 50, 'DE': 75})
    
    # User identification
    username = <PERSON><PERSON>ield(required=True, unique=True)
    
    # Additional fields found in the database
    use_v2_template = BooleanField(default=False)  # Toggle for V2 Template
    useCardmarket = BooleanField(default=False)  # Toggle for using Cardmarket
    __v = IntField()  # Version field used by MongoDB
    
    # Basic settings
    settings = DictField()
    pin = StringField()
    currency = StringField(default='USD')
    
    # Price calculation settings
    minPrice = FloatField(default=0.50)  # Default minimum price
    price_point = StringField(default='Low Price')  # Options: Low Price, Mid Price, Market Price, High Price
    price_rounding_enabled = BooleanField(default=False)  # Enable/disable price rounding
    price_rounding_thresholds = ListField(IntField(), default=[49, 99])  # List of rounding thresholds
    
    # Price comparison settings
    use_highest_price = BooleanField(default=False)  # Use highest price from comparison pairs
    price_comparison_pairs = ListField(ListField(StringField()), default=[])  # List of price type pairs to compare
    price_modifiers = DictField(default={})  # Modifiers for each price type
    price_preference_order = ListField(StringField(), default=['lowPrice', 'marketPrice', 'midPrice', 'highPrice', 'lowestListedPrice'])
    
    # Game-specific settings
    game_minimum_prices = DictField(default={})  # Game-specific minimum prices
    advancedPricingRules = DictField(default={})  # Advanced pricing rules by vendor/product/expansion
    customStepping = DictField(default={
        'nm': 100,
        'lp': 80,
        'mp': 70,
        'hp': 65,
        'dm': 50
    })
    
    # TCG trend settings
    tcg_trend_increasing = FloatField(default=0.0)
    tcg_trend_decreasing = FloatField(default=0.0)
    
    # Store settings
    instoreCash = FloatField(default=70.0)  # Default 70% for cash
    instoreCredit = FloatField(default=100.0)  # Default 100% for credit
    defaultCashPercentage = FloatField()
    defaultCreditPercentage = FloatField()
    defaultBuylistFloorPrice = FloatField()
    use_own_buylist_prices = BooleanField(default=False)
    
    # Other settings
    floorPrice = FloatField()
    excludedSku = ListField(StringField())
    pricingUpdateFrequency = StringField()
    buylistEmail = StringField()
    pricing_rules = ListField(DictField())
    conditions = ListField(StringField())
    enabledGames = ListField(StringField())
    tncS = StringField()
    bulkSettings = DictField()
    sellerShippingAddress = StringField()
    expansionRules = ListField(DictField())
    box1 = StringField()
    preferred_marketplace = StringField(default='TCGPlayer')
    template = StringField(default='original')  # Options: original, V2
    custom_buylist_button_label = StringField(default='Add Custom Item')  # Label for the custom buylist button
    # Inventory Modifier settings
    inventoryModifier = BooleanField(default=False)  # Toggle for Inventory Modifier
    inventory_modifiers = EmbeddedDocumentListField('InventoryModifierRule')
    
    # Release Date settings
    release_date_enabled = BooleanField(default=False)  # Toggle for Release Date Exclusion
    release_date_days = IntField(default=30)  # Number of days after release to exclude items
    
    # Buylist Cash Limit setting
    cash_limit = FloatField(default=0.0)  # Maximum cash payout per buylist order (0 = no limit)

    meta = {'collection': 'user_settings', 'strict': False}

class InventoryModifierRule(EmbeddedDocument):
    min_value = FloatField(required=True)
    max_value = FloatField(required=True)
    percentage = FloatField(required=True)
