import logging
from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from pymongo import MongoClient, UpdateOne
import concurrent.futures
import time
from bson import ObjectId
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import json
from datetime import datetime

logger = logging.getLogger(__name__)

# Create blueprint
shopify_bulk_edit_bp = Blueprint('shopify_bulk_edit', __name__, url_prefix='/shopify/bulk-edit')

def get_mongo_client():
    """Get MongoDB client from app context"""
    return current_app.mongo_client

def get_db_name():
    """Get database name from app config"""
    return current_app.config.get('MONGO_DBNAME', 'test')

@shopify_bulk_edit_bp.route('/')
@login_required
def bulk_edit_page():
    """Render the bulk edit page"""
    logger.info("Rendering bulk edit page")
    return render_template('shopify_bulk_edit.html')

@shopify_bulk_edit_bp.route('/image-repair')
@login_required
def image_repair_page():
    """Render the image repair page"""
    logger.info("Rendering image repair page")
    return render_template('shopify_bulk_edit.html')

@shopify_bulk_edit_bp.route('/test')
def test_endpoint():
    """Test endpoint to check if the server is responding"""
    logger.info("Test endpoint called")
    return jsonify({
        'status': 'success',
        'message': 'Shopify bulk edit API is working',
        'timestamp': datetime.now().isoformat()
    })

@shopify_bulk_edit_bp.route('/api/products-needing-image-repair')
@login_required
def get_products_needing_image_repair():
    """Get products that need image repair (tcgItems=true but no image)"""
    game_filter = request.args.get('game', '')
    expansion_filter = request.args.get('expansion', '')
    image_filter = request.args.get('image_filter', 'true').lower() == 'true'  # Default to true
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 25))

    # Calculate skip value for pagination
    skip = (page - 1) * per_page

    # Get MongoDB client
    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']

    # Build base query
    query = {
        'username': current_user.username,
        'tcgItem': True
    }

    # Add image filter if requested
    if image_filter:
        image_query = {
            '$or': [
                {'image': None},
                {'image': ''},
                {'image': {'$exists': False}},
                {'image.src': None},
                {'image.src': ''},
                {'image.src': {'$exists': False}}
            ]
        }
        query.update(image_query)

    # Add game filter if provided
    if game_filter:
        query['product_type'] = game_filter

    # Add expansion filter if provided
    if expansion_filter:
        query['expansionName'] = expansion_filter

    # Log the query
    logger.info(f"Products query: {query}")

    # Get total count
    total_count = shopify_collection.count_documents(query)
    logger.info(f"Found {total_count} products matching the query")

    # Get paginated products
    products = list(shopify_collection.find(query).skip(skip).limit(per_page))
    logger.info(f"Returning {len(products)} products for page {page}")

    # Convert ObjectId to string for JSON serialization
    for i, product in enumerate(products):
        if '_id' in product:
            product['_id'] = str(product['_id'])

        # Ensure number field is included
        if 'number' not in product:
            product['number'] = None

        # Log the first few products for debugging
        if i < 3:
            logger.info(f"Product {i+1} details: title={product.get('title')}, number={product.get('number')}, _id={product.get('_id')}")

    # Return products and pagination info
    return jsonify({
        'products': products,
        'total': total_count,
        'page': page,
        'per_page': per_page,
        'total_pages': (total_count + per_page - 1) // per_page
    })

@shopify_bulk_edit_bp.route('/api/game-types')
@login_required
def get_game_types():
    """Get all game types (product_types) for filtering"""
    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']

    # Get ALL distinct product types without any filters except username
    query = {'username': current_user.username}

    # Log the query
    logger.info(f"Game types query: {query}")

    # Get distinct product types
    game_types = shopify_collection.distinct('product_type', query)

    # Log the number of game types found and the actual values
    logger.info(f"Found {len(game_types)} game types for user {current_user.username}")
    logger.info(f"Game types: {game_types}")

    return jsonify({'game_types': game_types})

@shopify_bulk_edit_bp.route('/api/expansion-names')
@login_required
def get_expansion_names():
    """Get all expansion names for filtering"""
    game_filter = request.args.get('game', '')

    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']

    # Build query without any filters except username
    query = {'username': current_user.username}

    # Add game filter if provided
    if game_filter:
        query['product_type'] = game_filter

    # Log the query
    logger.info(f"Expansion names query: {query}")

    # Get distinct expansion names
    expansion_names = shopify_collection.distinct('expansionName', query)

    # Log the number of expansion names found and the actual values
    logger.info(f"Found {len(expansion_names)} expansion names for user {current_user.username} with game filter: {game_filter}")
    logger.info(f"Expansion names: {expansion_names}")

    # Filter out None or empty values and sort
    expansion_names = sorted([exp for exp in expansion_names if exp])

    return jsonify({'expansion_names': expansion_names})

@shopify_bulk_edit_bp.route('/api/repair-image', methods=['POST'])
@login_required
def repair_image():
    """Repair image for a single product"""
    data = request.json
    product_id = data.get('product_id')

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']
    catalog_collection = db['catalog']
    user_collection = db['user']

    # Get product from MongoDB
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    # Get TCGPlayer ID
    tcg_product_id = product.get('productId')
    if not tcg_product_id:
        return jsonify({'error': 'Product does not have a TCGPlayer ID'}), 400

    # Look up catalog item
    catalog_item = catalog_collection.find_one({'productId': int(tcg_product_id)})
    if not catalog_item:
        return jsonify({'error': 'Catalog item not found'}), 404

    # Get image URL from catalog
    image_url = catalog_item.get('image')
    if not image_url:
        # Try alternative fields
        image_url = catalog_item.get('imageUrl')
        if not image_url:
            return jsonify({'error': 'No image URL found in catalog'}), 404

    # Update product in MongoDB
    result = shopify_collection.update_one(
        {'_id': ObjectId(product_id)},
        {'$set': {
            'image': {'src': image_url},
            'needsPushing': True,
            'last_updated': datetime.utcnow()
        }}
    )

    if result.modified_count == 0:
        return jsonify({'error': 'Failed to update product'}), 500

    # Get user profile for Shopify credentials
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile.get('shopifyStoreName')
    shopify_access_token = user_profile.get('shopifyAccessToken')

    if not shopify_store_name or not shopify_access_token:
        return jsonify({'error': 'Shopify credentials not found'}), 400

    # Get Shopify product ID
    shopify_id = product.get('id')
    if not shopify_id:
        return jsonify({'error': 'Shopify ID not found'}), 400

    # Prepare product data for Shopify
    product_data = {
        'product': {
            'id': shopify_id,
            'images': [{'src': image_url}]
        }
    }

    # Push to Shopify
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopify_access_token
    }

    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_id}.json"

    try:
        response = requests.put(url, headers=headers, json=product_data)

        if response.status_code == 200:
            # Verify the image was actually added to the product
            shopify_response = response.json()

            if 'product' in shopify_response and 'images' in shopify_response['product'] and len(shopify_response['product']['images']) > 0:
                # Image exists in the response
                shopify_image_url = shopify_response['product']['images'][0].get('src', '')

                if shopify_image_url:
                    # Update needsPushing flag
                    shopify_collection.update_one(
                        {'_id': ObjectId(product_id)},
                        {'$set': {
                            'needsPushing': False,
                            'shopify_image_verified': True,
                            'last_updated': datetime.utcnow()
                        }}
                    )

                    # Return success
                    return jsonify({
                        'success': True,
                        'message': 'Image repaired and verified on Shopify',
                        'image_url': image_url,
                        'shopify_image_url': shopify_image_url,
                        'shopify_response': shopify_response
                    })
                else:
                    logger.error(f"Image URL missing in Shopify response for product {product_id}")
                    return jsonify({
                        'success': False,
                        'message': 'Image URL missing in Shopify response',
                        'image_url': image_url,
                        'shopify_response': shopify_response
                    }), 500
            else:
                logger.error(f"No images found in Shopify response for product {product_id}")
                return jsonify({
                    'success': False,
                    'message': 'No images found in Shopify response',
                    'image_url': image_url,
                    'shopify_response': shopify_response
                }), 500
        else:
            logger.error(f"Failed to push to Shopify: {response.text}")
            return jsonify({
                'success': False,
                'message': f'Failed to push to Shopify: {response.text}',
                'image_url': image_url,
                'status_code': response.status_code
            }), 500
    except Exception as e:
        logger.error(f"Error pushing to Shopify: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error pushing to Shopify: {str(e)}',
            'image_url': image_url
        }), 500

@shopify_bulk_edit_bp.route('/api/repair-all-images', methods=['POST'])
@login_required
def repair_all_images():
    """Repair images for all products matching the filter"""
    data = request.json
    game_filter = data.get('game', '')
    expansion_filter = data.get('expansion', '')
    image_filter = data.get('image_filter', True)
    selected_product_ids = data.get('selected_product_ids', [])

    logger.info(f"Repair all images request with filters: game={game_filter}, expansion={expansion_filter}, image_filter={image_filter}, selected_ids={len(selected_product_ids)}")

    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']
    catalog_collection = db['catalog']
    user_collection = db['user']

    # Get user profile for Shopify credentials
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile.get('shopifyStoreName')
    shopify_access_token = user_profile.get('shopifyAccessToken')

    if not shopify_store_name or not shopify_access_token:
        return jsonify({'error': 'Shopify credentials not found'}), 400

    # Set up headers for Shopify API
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopify_access_token
    }

    # Build base query
    query = {
        'username': current_user.username,
        'tcgItem': True
    }

    # Add image filter if requested
    if image_filter:
        image_query = {
            '$or': [
                {'image': None},
                {'image': ''},
                {'image': {'$exists': False}},
                {'image.src': None},
                {'image.src': ''},
                {'image.src': {'$exists': False}}
            ]
        }
        query.update(image_query)

    # Add game filter if provided
    if game_filter:
        query['product_type'] = game_filter

    # Add expansion filter if provided
    if expansion_filter:
        query['expansionName'] = expansion_filter

    # Add selected product IDs filter if provided
    if selected_product_ids:
        query['_id'] = {'$in': [ObjectId(pid) for pid in selected_product_ids]}

    # Get products
    products = list(shopify_collection.find(query))

    # Process products in batches
    batch_size = 50
    total_products = len(products)
    success_count = 0
    error_count = 0
    shopify_success_count = 0
    shopify_error_count = 0

    # Prepare bulk operations for MongoDB updates
    bulk_operations = []

    for product in products:
        try:
            # Get TCGPlayer ID
            tcg_product_id = product.get('productId')
            if not tcg_product_id:
                error_count += 1
                continue

            # Look up catalog item
            catalog_item = catalog_collection.find_one({'productId': int(tcg_product_id)})
            if not catalog_item:
                error_count += 1
                continue

            # Get image URL from catalog - try different possible fields
            image_url = catalog_item.get('image')
            if not image_url:
                # Try alternative fields
                image_url = catalog_item.get('imageUrl')
                if not image_url:
                    error_count += 1
                    continue

            # Add update operation for MongoDB
            bulk_operations.append(
                UpdateOne(
                    {'_id': product['_id']},
                    {'$set': {
                        'image': {'src': image_url},
                        'needsPushing': True,
                        'last_updated': datetime.utcnow()
                    }}
                )
            )

            success_count += 1

            # Execute bulk operations in batches
            if len(bulk_operations) >= batch_size:
                shopify_collection.bulk_write(bulk_operations)
                bulk_operations = []

            # Push to Shopify
            shopify_id = product.get('id')
            if shopify_id:
                try:
                    # Prepare product data for Shopify
                    product_data = {
                        'product': {
                            'id': shopify_id,
                            'images': [{'src': image_url}]
                        }
                    }

                    # Push to Shopify
                    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_id}.json"
                    response = requests.put(url, headers=headers, json=product_data)

                    if response.status_code == 200:
                        # Verify the image was actually added to the product
                        shopify_response = response.json()

                        if 'product' in shopify_response and 'images' in shopify_response['product'] and len(shopify_response['product']['images']) > 0:
                            # Image exists in the response
                            shopify_image_url = shopify_response['product']['images'][0].get('src', '')

                            if shopify_image_url:
                                # Update needsPushing flag and mark as verified
                                shopify_collection.update_one(
                                    {'_id': product['_id']},
                                    {'$set': {
                                        'needsPushing': False,
                                        'shopify_image_verified': True,
                                        'last_updated': datetime.utcnow()
                                    }}
                                )
                                shopify_success_count += 1
                            else:
                                logger.error(f"Image URL missing in Shopify response for product {product.get('_id')}")
                                shopify_error_count += 1
                        else:
                            logger.error(f"No images found in Shopify response for product {product.get('_id')}")
                            shopify_error_count += 1
                    else:
                        logger.error(f"Error pushing product {product.get('_id')} to Shopify: {response.text}")
                        shopify_error_count += 1
                except Exception as e:
                    logger.error(f"Error pushing product {product.get('_id')} to Shopify: {str(e)}")
                    shopify_error_count += 1

        except Exception as e:
            logger.error(f"Error processing product {product.get('_id')}: {str(e)}")
            error_count += 1

    # Execute any remaining bulk operations
    if bulk_operations:
        shopify_collection.bulk_write(bulk_operations)

    # Return results
    return jsonify({
        'success': True,
        'message': f'Processed {total_products} products',
        'success_count': success_count,
        'error_count': error_count,
        'shopify_success_count': shopify_success_count,
        'shopify_error_count': shopify_error_count
    })

@shopify_bulk_edit_bp.route('/api/export-csv')
@login_required
def export_csv():
    """Export products to CSV with card number and expansion"""
    game_filter = request.args.get('game', '')
    expansion_filter = request.args.get('expansion', '')
    image_filter = request.args.get('image_filter', 'true').lower() == 'true'  # Default to true

    # Get MongoDB client
    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']

    # Build base query
    query = {
        'username': current_user.username,
        'tcgItem': True
    }

    # Add image filter if requested
    if image_filter:
        image_query = {
            '$or': [
                {'image': None},
                {'image': ''},
                {'image': {'$exists': False}},
                {'image.src': None},
                {'image.src': ''},
                {'image.src': {'$exists': False}}
            ]
        }
        query.update(image_query)

    # Add game filter if provided
    if game_filter:
        query['product_type'] = game_filter

    # Add expansion filter if provided
    if expansion_filter:
        query['expansionName'] = expansion_filter

    # Get products
    products = list(shopify_collection.find(query))

    # Generate CSV content
    from io import StringIO
    import csv
    from flask import Response

    csv_data = StringIO()
    csv_writer = csv.writer(csv_data)

    # Write header
    csv_writer.writerow(['Title', 'Card Number', 'Expansion', 'Game'])

    # Write data
    for product in products:
        csv_writer.writerow([
            product.get('title', ''),
            product.get('number', ''),
            product.get('expansionName', ''),
            product.get('product_type', '')
        ])

    # Create response
    response = Response(
        csv_data.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': 'attachment; filename=shopify_products.csv'
        }
    )

    return response

@shopify_bulk_edit_bp.route('/api/push-to-shopify', methods=['POST'])
@login_required
def push_to_shopify():
    """Push updated products to Shopify"""
    data = request.json
    product_ids = data.get('product_ids', [])

    if not product_ids:
        return jsonify({'error': 'No product IDs provided'}), 400

    mongo_client = get_mongo_client()
    db_name = get_db_name()
    db = mongo_client[db_name]
    shopify_collection = db['shProducts']
    user_collection = db['user']

    # Get user profile for Shopify credentials
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile.get('shopifyStoreName')
    shopify_access_token = user_profile.get('shopifyAccessToken')

    if not shopify_store_name or not shopify_access_token:
        return jsonify({'error': 'Shopify credentials not found'}), 400

    # Set up headers for Shopify API
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopify_access_token
    }

    # Process products
    results = []

    for product_id in product_ids:
        try:
            # Get product from MongoDB
            product = shopify_collection.find_one({'_id': ObjectId(product_id)})
            if not product:
                results.append({
                    'product_id': product_id,
                    'success': False,
                    'message': 'Product not found'
                })
                continue

            # Get Shopify product ID
            shopify_id = product.get('id')
            if not shopify_id:
                results.append({
                    'product_id': product_id,
                    'success': False,
                    'message': 'Shopify ID not found'
                })
                continue

            # Check if product has an image
            if not product.get('image') or not product['image'].get('src'):
                results.append({
                    'product_id': product_id,
                    'success': False,
                    'message': 'Product does not have an image'
                })
                continue

            # Prepare product data for Shopify
            product_data = {
                'product': {
                    'id': shopify_id,
                    'images': [{'src': product['image']['src']}]
                }
            }

            # Add additional metadata to help with debugging
            product_data['product']['metafields'] = [
                {
                    'namespace': 'tcgsync',
                    'key': 'image_repair_timestamp',
                    'value': datetime.utcnow().isoformat(),
                    'type': 'string'
                }
            ]

            # Push to Shopify
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_id}.json"
            response = requests.put(url, headers=headers, json=product_data)

            if response.status_code == 200:
                # Verify the image was actually added to the product
                shopify_response = response.json()

                if 'product' in shopify_response and 'images' in shopify_response['product'] and len(shopify_response['product']['images']) > 0:
                    # Image exists in the response
                    shopify_image_url = shopify_response['product']['images'][0].get('src', '')

                    if shopify_image_url:
                        # Update needsPushing flag and mark as verified
                        shopify_collection.update_one(
                            {'_id': ObjectId(product_id)},
                            {'$set': {
                                'needsPushing': False,
                                'shopify_image_verified': True,
                                'last_updated': datetime.utcnow()
                            }}
                        )

                        results.append({
                            'product_id': product_id,
                            'success': True,
                            'message': 'Product updated and image verified on Shopify',
                            'shopify_image_url': shopify_image_url,
                            'shopify_response': shopify_response
                        })
                    else:
                        results.append({
                            'product_id': product_id,
                            'success': False,
                            'message': 'Image URL missing in Shopify response',
                            'shopify_response': shopify_response
                        })
                else:
                    results.append({
                        'product_id': product_id,
                        'success': False,
                        'message': 'No images found in Shopify response',
                        'shopify_response': shopify_response
                    })
            else:
                results.append({
                    'product_id': product_id,
                    'success': False,
                    'message': f'Failed to update product: {response.text}'
                })

        except Exception as e:
            logger.error(f"Error pushing product {product_id} to Shopify: {str(e)}")
            results.append({
                'product_id': product_id,
                'success': False,
                'message': f'Error: {str(e)}'
            })

    # Return results
    return jsonify({
        'success': True,
        'results': results
    })
