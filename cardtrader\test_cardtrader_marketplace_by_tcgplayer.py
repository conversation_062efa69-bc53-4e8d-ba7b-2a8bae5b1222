# Script to pull marketplace listings from CardTrader API for a specific TCGPlayer ID
import asyncio
import aiohttp
import time
import motor.motor_asyncio
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import argparse
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# Async MongoDB client
motor_client = motor.motor_asyncio.AsyncIOMotorClient(mongo_uri)
motor_db = motor_client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Rate limiting with adaptive adjustment
class AdaptiveRateLimiter:
    def __init__(self, initial_rate=5.0, min_rate=1.0, max_rate=10.0):
        self.rate_limit = initial_rate  # requests per second
        self.min_rate = min_rate
        self.max_rate = max_rate
        self.tokens = initial_rate
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        self.consecutive_successes = 0
        self.consecutive_failures = 0
    
    async def acquire(self):
        async with self.lock:
            now = time.time()
            time_passed = now - self.last_update
            self.tokens = min(self.rate_limit, self.tokens + time_passed * self.rate_limit)
            self.last_update = now
            
            if self.tokens < 1:
                # Not enough tokens, calculate sleep time
                sleep_time = (1 - self.tokens) / self.rate_limit
                await asyncio.sleep(sleep_time)
                self.tokens = 0
                self.last_update = time.time()
            else:
                self.tokens -= 1
    
    def success(self):
        """Record a successful request"""
        self.consecutive_successes += 1
        self.consecutive_failures = 0
        
        # Increase rate limit after 10 consecutive successes
        if self.consecutive_successes >= 10:
            self.rate_limit = min(self.rate_limit + 0.5, self.max_rate)
            logger.info(f"Increasing rate limit to {self.rate_limit} req/sec after {self.consecutive_successes} consecutive successes")
            self.consecutive_successes = 0
    
    def failure(self):
        """Record a failed request due to rate limiting"""
        self.consecutive_failures += 1
        self.consecutive_successes = 0
        
        # Decrease rate limit after 3 consecutive failures
        if self.consecutive_failures >= 3:
            self.rate_limit = max(self.rate_limit - 0.5, self.min_rate)
            logger.info(f"Decreasing rate limit to {self.rate_limit} req/sec after {self.consecutive_failures} consecutive failures")
            self.consecutive_failures = 0

# Global rate limiter
rate_limiter = None  # Will be initialized in main()

def get_matched_blueprint_by_tcgplayer_id(tcgplayer_id):
    """Get a blueprint from the matchedIds collection by TCGPlayer ID"""
    logger.info(f"Looking up blueprint with TCGPlayer ID: {tcgplayer_id}")
    
    # Check if matchedIds collection exists
    if 'matchedIds' not in cardtrader_db.list_collection_names():
        logger.error("matchedIds collection not found in cardtrader database")
        return None
    
    # Build the query
    query = {"tcg_player_id": int(tcgplayer_id)}  # Convert to int since TCGPlayer IDs are stored as integers
    
    # Find the matching record
    matched_record = cardtrader_db.matchedIds.find_one(query)
    
    if not matched_record:
        logger.error(f"No matching record found for TCGPlayer ID: {tcgplayer_id}")
        return None
    
    # Get the blueprint ID from the matched record
    blueprint_id = matched_record.get('blueprint_id')  # Using the correct field name
    
    if not blueprint_id:
        logger.error(f"No blueprint_id found in matched record for TCGPlayer ID: {tcgplayer_id}")
        return None
    
    # Get the blueprint from the blueprints collection
    blueprint = cardtrader_db.blueprints.find_one({"id": blueprint_id})
    
    if not blueprint:
        logger.error(f"No blueprint found with ID: {blueprint_id}")
        return None
    
    logger.info(f"Found blueprint: {blueprint.get('name')} (ID: {blueprint_id})")
    
    # Add TCGPlayer ID to the blueprint for reference
    blueprint['tcg_player_id'] = tcgplayer_id
    
    return blueprint

def extract_market_summary(listings):
    """Extract summary market data from listings"""
    if not listings:
        return {
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None,
                "currency": "EUR"
            }
        }
    
    # Calculate total quantity
    total_quantity = sum(listing.get("quantity", 0) for listing in listings)
    
    # Extract prices (in cents)
    prices = []
    for listing in listings:
        price_info = listing.get("price", {})
        if isinstance(price_info, dict) and "cents" in price_info:
            # Convert cents to dollars/euros for easier reading
            price = float(price_info["cents"]) / 100
            prices.append(price)
    
    # Calculate price statistics
    price_data = {
        "min": min(prices) if prices else None,
        "max": max(prices) if prices else None,
        "avg": sum(prices) / len(prices) if prices else None,
        "currency": listings[0].get("price", {}).get("currency", "EUR") if listings else "EUR"
    }
    
    return {
        "total_quantity": total_quantity,
        "price_data": price_data
    }

async def fetch_marketplace_listings(session, blueprint_id, max_retries=3):
    """Fetch marketplace listings for a specific blueprint ID using aiohttp"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"blueprint_id": blueprint_id}
    
    for retry in range(max_retries):
        try:
            # Acquire token from rate limiter
            await rate_limiter.acquire()
            
            logger.info(f"Fetching marketplace listings for blueprint ID: {blueprint_id}")
            
            # Make the API request
            async with session.get(url, params=params, headers=HEADERS, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    # The response is an object with blueprint IDs as keys and arrays of products as values
                    listings = data.get(str(blueprint_id), [])
                    logger.info(f"Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                    rate_limiter.success()
                    return listings
                elif response.status == 429:
                    # Rate limited
                    error_text = await response.text()
                    logger.warning(f"Rate limited: {error_text}")
                    rate_limiter.failure()
                    
                    # Back off exponentially
                    backoff_time = 2 ** retry
                    logger.info(f"Backing off for {backoff_time:.2f} seconds")
                    await asyncio.sleep(backoff_time)
                    
                    logger.info(f"Retry {retry+1}/{max_retries} for blueprint ID: {blueprint_id}")
                else:
                    error_text = await response.text()
                    logger.error(f"API request failed with status code {response.status}: {error_text}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
            await asyncio.sleep(1)
    
    logger.error(f"All retries failed for blueprint ID {blueprint_id}")
    return []

async def save_to_marketprices(blueprint, listings):
    """Save the marketplace listings summary to the marketprices collection using motor"""
    if not listings:
        logger.info(f"No listings to save for blueprint ID: {blueprint.get('id')}")
        return None
    
    # Extract market summary data
    market_summary = extract_market_summary(listings)
    
    # Create a document to insert
    document = {
        "blueprint_id": blueprint.get("id"),
        "blueprint_name": blueprint.get("name"),
        "expansion_id": blueprint.get("expansion_id"),
        "game_id": blueprint.get("game_id"),
        "tcg_player_id": blueprint.get("tcg_player_id"),  # Include TCGPlayer ID
        "total_quantity": market_summary["total_quantity"],
        "price_data": market_summary["price_data"],
        "listings_count": len(listings),  # Store the count of listings for reference
        "listings": listings,  # Store the full listings data
        "fetched_at": datetime.now()
    }
    
    try:
        # Insert or update the document in the marketprices collection
        result = await motor_db.marketprices.update_one(
            {"blueprint_id": blueprint.get("id")},
            {"$set": document},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Inserted new document for blueprint ID: {blueprint.get('id')}")
        else:
            logger.info(f"Updated existing document for blueprint ID: {blueprint.get('id')}")
        
        return document
    except Exception as e:
        logger.error(f"Error saving to marketprices collection: {str(e)}")
        return None

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull marketplace listings from CardTrader API for a specific TCGPlayer ID')
    parser.add_argument('--tcgplayer-id', type=str, required=True,
                        help='TCGPlayer ID to look up')
    parser.add_argument('--rate-limit', type=float, default=5.0,
                        help='Initial API rate limit in requests per second (default: 5.0)')
    parser.add_argument('--output', choices=['json', 'summary'], default='summary',
                        help='Output format (json or summary, default: summary)')
    return parser.parse_args()

async def main_async():
    # Parse command line arguments
    args = parse_arguments()
    
    # Initialize rate limiter
    global rate_limiter
    rate_limiter = AdaptiveRateLimiter(initial_rate=args.rate_limit)
    
    start_time = time.time()
    logger.info(f"Starting CardTrader marketplace data collection for TCGPlayer ID: {args.tcgplayer_id}")
    
    # Get blueprint by TCGPlayer ID
    blueprint = get_matched_blueprint_by_tcgplayer_id(args.tcgplayer_id)
    
    if not blueprint:
        logger.error(f"Could not find blueprint for TCGPlayer ID: {args.tcgplayer_id}")
        return
    
    # Create marketprices collection if it doesn't exist
    if 'marketprices' not in cardtrader_db.list_collection_names():
        logger.info("Creating marketprices collection")
        cardtrader_db.create_collection('marketprices')
    
    # Create a ClientSession that will be used for all requests
    async with aiohttp.ClientSession() as session:
        # Fetch marketplace listings
        listings = await fetch_marketplace_listings(session, blueprint.get('id'))
        
        # Save to marketprices collection
        result = await save_to_marketprices(blueprint, listings)
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"Completed in {duration:.2f} seconds")
    
    # Output results based on format
    if args.output == 'json' and result:
        # Print full JSON result
        print(json.dumps(result, default=str, indent=2))
    elif result:
        # Print summary
        print("\n=== MARKETPLACE SUMMARY ===")
        print(f"Card: {result.get('blueprint_name')}")
        print(f"TCGPlayer ID: {result.get('tcg_player_id')}")
        print(f"CardTrader ID: {result.get('blueprint_id')}")
        print(f"Total Listings: {result.get('listings_count')}")
        print(f"Total Quantity: {result.get('total_quantity')}")
        
        price_data = result.get('price_data', {})
        currency = price_data.get('currency', 'EUR')
        print("\n=== PRICE DATA ===")
        print(f"Min Price: {price_data.get('min')} {currency}")
        print(f"Max Price: {price_data.get('max')} {currency}")
        print(f"Avg Price: {price_data.get('avg')} {currency}")
        
        # Print first 5 listings as a sample
        if result.get('listings_count', 0) > 0:
            print("\n=== SAMPLE LISTINGS ===")
            for i, listing in enumerate(result.get('listings', [])[:5]):
                seller = listing.get('seller', {}).get('username', 'Unknown')
                quantity = listing.get('quantity', 0)
                price = listing.get('price', {}).get('cents', 0) / 100
                condition = listing.get('condition', 'Unknown')
                print(f"{i+1}. Seller: {seller}, Quantity: {quantity}, Price: {price} {currency}, Condition: {condition}")
            
            if result.get('listings_count', 0) > 5:
                print(f"... and {result.get('listings_count') - 5} more listings")

def main():
    """Entry point for the script"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
