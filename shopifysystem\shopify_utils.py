import shopify
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'shopify_utils_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Log that the shopify module is imported
logger.info("Shopify module imported successfully")

# Import the MongoDB connection manager
from mongo_connection_manager import get_mongo_client
db = get_mongo_client().test
catalog_collection = db['catalog']

def send_game_to_shopify(game_id, shopify_store_name, shopify_access_token):
    # Initialize the Shopify session
    shop_url = f"https://{shopify_store_name}.myshopify.com/admin"
    shopify.ShopifyResource.set_site(shop_url)
    shopify.ShopifyResource.set_user(shopify_access_token)

    # Fetch the game details from your database
    game = fetch_game_from_database(game_id)

    if not game:
        return {"error": "Game not found"}

    try:
        # Create a new product in Shopify
        new_product = shopify.Product()
        new_product.title = game['name']
        new_product.body_html = game.get('description', '')
        new_product.vendor = game.get('manufacturer', 'Unknown')
        new_product.product_type = "Trading Card Game"
        new_product.tags = game.get('tags', [])

        # Create variants for each card in the game
        for card in game['cards']:
            variant = shopify.Variant({
                'price': str(card['price']),
                'sku': card['sku'],
                'option1': card['name'],
                'inventory_management': 'shopify',
                'inventory_quantity': card['count']
            })
            new_product.variants.append(variant)

        # Save the product
        if new_product.save():
            logger.info(f"Successfully created product in Shopify: {new_product.id}")
            return {"success": True, "product_id": new_product.id}
        else:
            logger.error(f"Failed to create product in Shopify: {new_product.errors.full_messages()}")
            return {"error": "Failed to create product in Shopify", "details": new_product.errors.full_messages()}

    except Exception as e:
        logger.error(f"Error sending game to Shopify: {str(e)}")
        return {"error": f"An error occurred while sending the game to Shopify: {str(e)}"}

def fetch_game_from_database(game_id):
    try:
        # Fetch game details
        game = catalog_collection.find_one({"idGame": int(game_id)})
        if not game:
            logger.error(f"Game with id {game_id} not found")
            return None

        # Prepare the game data
        game_data = {
            "name": game.get("name", "Unknown Game"),
            "description": game.get("description", ""),
            "manufacturer": game.get("manufacturer", "Unknown"),
            "tags": game.get("tags", []),
            "cards": []
        }

        # Prepare card data
        for card in game.get("cards", []):
            card_data = {
                "name": card.get("name", "Unknown Card"),
                "sku": card.get("sku", ""),
                "price": card.get("price", 0),
                "count": card.get("count", 0)
            }
            game_data["cards"].append(card_data)

        logger.info(f"Successfully fetched game data for game id {game_id}")
        return game_data

    except Exception as e:
        logger.error(f"Error fetching game from database: {str(e)}")
        return None

def update_shopify_product(shopify_id, update_data, username=None):
    """
    Update a Shopify product with the provided data using direct API calls
    
    Args:
        shopify_id (str): The Shopify product ID
        update_data (dict): The data to update, e.g. {'title': 'New Title', 'tags': 'tag1, tag2'}
        username (str, optional): The username of the user whose Shopify credentials to use. 
                                 If None, will try to use "admintcg" as a fallback.
    
    Returns:
        dict: Result of the update operation
    """
    logger.info(f"=== SHOPIFY UPDATE CALLED === Updating product {shopify_id} with data: {update_data}")
    try:
        # Get the user's Shopify credentials from the database
        user_collection = db['user']
        
        # First try to find the user by username
        if not username:
            username = "admintcg"  # Use as fallback if no username provided
        
        logger.info(f"Looking for user with username: {username}")
        user = user_collection.find_one({"username": username})
        
        logger.info(f"Looking for user with username: {username}")
        logger.info(f"Found user: {user.get('username') if user else 'None'}")
        
        if not user or not user.get('shopifyAccessToken') or not user.get('shopifyStoreName'):
            logger.error(f"No user with username {username} and Shopify credentials found")
            
            # Try to find any user with Shopify credentials as a fallback
            user = user_collection.find_one({"shopifyAccessToken": {"$exists": True}, "shopifyStoreName": {"$exists": True}})
            logger.info(f"Fallback user: {user.get('username') if user else 'None'}")
            
            if not user:
                logger.error("No user with Shopify credentials found")
                return {"error": "No Shopify credentials found"}
        
        # Use direct API calls with requests
        import requests
        import json
        
        # Set up the API URL and headers
        shop_name = user['shopifyStoreName']
        access_token = user['shopifyAccessToken']
        api_version = '2023-07'  # Use a recent API version
        
        headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
        
        # Skip fetching the current product data since we're using the product ID directly
        # This avoids the need to make an extra API call
        logger.info(f"Skipping fetch of current product data since we're using the product ID directly")
        
        # Prepare the update payload
        update_payload = {
            'product': {
                'id': shopify_id
            }
        }
        
        # Only include the fields we want to update
        for key, value in update_data.items():
            update_payload['product'][key] = value
        
        logger.info(f"Update payload: {json.dumps(update_payload)}")
        
        # Send the update request
        logger.info(f"Sending update request to Shopify API")
        put_url = f"https://{shop_name}.myshopify.com/admin/api/{api_version}/products/{shopify_id}.json"
        logger.info(f"API URL: {put_url}")
        logger.info(f"Headers: {headers}")
        response = requests.put(put_url, headers=headers, json=update_payload)
        
        if response.status_code == 200:
            logger.info(f"Successfully updated product in Shopify: {shopify_id}")
            
            # Update the product in the local database
            logger.info("Updating product in local database")
            shopify_collection = db['shProducts']
            
            # First find the product by Shopify ID and username
            username = user.get('username')
            logger.info(f"Looking up product with Shopify ID: {shopify_id} and username: {username}")
            
            # Try to find the product with the Shopify ID as an integer first
            try:
                shopify_id_int = int(shopify_id)
                product = shopify_collection.find_one({
                    "id": shopify_id_int,
                    "username": username
                })
                logger.info(f"Lookup result with integer ID: {product is not None}")
            except (ValueError, TypeError):
                # If the Shopify ID is not a valid integer, try as a string
                product = None
                logger.warning(f"Shopify ID {shopify_id} is not a valid integer")
            
            # If not found with integer ID, try with string ID
            if not product:
                product = shopify_collection.find_one({
                    "id": shopify_id,
                    "username": username
                })
                logger.info(f"Lookup result with string ID: {product is not None}")
            if product:
                # Update the product
                db_result = shopify_collection.update_one(
                    {
                        "id": int(shopify_id),
                        "username": username
                    },
                    {"$set": update_data}
                )
                logger.info(f"Database update result: {db_result.modified_count} documents modified")
                
                # Also remove any discrepancies for this product
                discrepancy_collection = db['productDiscrepancies']
                delete_result = discrepancy_collection.delete_many({
                    "product_id_int": product.get('productId'),
                    "username": username
                })
                logger.info(f"Deleted {delete_result.deleted_count} discrepancy records for product {product.get('productId')}")
            else:
                logger.warning(f"Product with Shopify ID {shopify_id} not found in local database")
            
            return {"success": True, "product_id": shopify_id}
        else:
            logger.error(f"Failed to update product in Shopify API: {response.status_code} {response.text}")
            return {"error": f"Failed to update product in Shopify API: {response.status_code} {response.text}"}
    
    except Exception as e:
        logger.error(f"Error updating product in Shopify: {str(e)}")
        logger.exception("Full exception details:")
        return {"error": f"An error occurred while updating the product in Shopify: {str(e)}"}
