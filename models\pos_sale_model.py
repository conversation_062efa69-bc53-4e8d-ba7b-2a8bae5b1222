from mongoengine import Document, StringField, FloatField, ListField, DictField, DateTimeField, BooleanField, ReferenceField, EmbeddedDocument, EmbeddedDocumentField
from datetime import datetime

class PaymentMethodDetail(EmbeddedDocument):
    """
    Embedded document for storing payment method details.
    Used for split payments in the POS system.
    """
    type = StringField(required=True)  # 'cash', 'card', etc.
    amount = FloatField(required=True)
    reference_id = StringField()  # For card payments, store the transaction reference
    timestamp = DateTimeField(default=datetime.utcnow)

    meta = {'strict': False}

class POSSale(Document):
    """
    Model for storing POS sales data.
    This model tracks all transactions made through the POS system.
    """
    id = StringField(primary_key=True)
    username = StringField(required=True)
    till_id = StringField(required=True)

    # Customer information
    customer_id = StringField()
    customer_name = StringField()
    customer_email = StringField()

    # Employee information
    employee_id = StringField()
    employee_name = StringField(required=True)

    # Payment information
    payment_method = StringField()  # For backward compatibility
    payment_methods = ListField(EmbeddedDocumentField(PaymentMethodDetail))  # For split payments
    is_split_payment = BooleanField(default=False)  # Flag to indicate if this is a split payment
    total = FloatField(required=True)
    subtotal = FloatField(required=True)
    tax_amount = FloatField(required=True)
    discount_amount = FloatField(default=0)
    discount_type = StringField()  # percentage, fixed
    discount_value = FloatField(default=0)

    # Gift card and store credit
    gift_card_id = StringField()
    gift_card_amount = FloatField(default=0)
    store_credit_account_id = StringField()
    store_credit_amount = FloatField(default=0)

    # Items purchased
    items = ListField(DictField(), required=True)
    tax_lines = ListField(DictField())

    # Timestamps
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField()

    # Status
    is_refunded = BooleanField(default=False)
    refund_amount = FloatField(default=0)
    refund_reason = StringField()
    refund_date = DateTimeField()

    # Shopify order ID if synced
    shopify_order_id = StringField()

    meta = {
        'collection': 'pos_sales',
        'indexes': [
            'username',
            'till_id',
            'customer_id',
            'employee_name',
            'payment_method',
            'created_at',
            'is_refunded'
        ]
    }

    def to_dict(self):
        """Convert the document to a dictionary."""
        return {
            "id": self.id,
            "till_id": self.till_id,
            "customer_id": self.customer_id,
            "customer_name": self.customer_name,
            "customer_email": self.customer_email,
            "employee_id": self.employee_id,
            "employee_name": self.employee_name,
            "payment_method": self.payment_method,
            "total": self.total,
            "subtotal": self.subtotal,
            "tax_amount": self.tax_amount,
            "discount_amount": self.discount_amount,
            "discount_type": self.discount_type,
            "discount_value": self.discount_value,
            "gift_card_id": self.gift_card_id,
            "gift_card_amount": self.gift_card_amount,
            "store_credit_account_id": self.store_credit_account_id,
            "store_credit_amount": self.store_credit_amount,
            "items": self.items,
            "tax_lines": self.tax_lines,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_refunded": self.is_refunded,
            "refund_amount": self.refund_amount,
            "refund_reason": self.refund_reason,
            "refund_date": self.refund_date.isoformat() if self.refund_date else None,
            "shopify_order_id": self.shopify_order_id
        }
