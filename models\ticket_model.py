from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, ReferenceField, <PERSON>Field
from datetime import datetime

class Ticket(Document):
    title = StringField(required=True)
    description = StringField(required=True)
    priority = StringField(choices=['Low', 'Medium', 'High'], default='Low')
    status = StringField(choices=['Open', 'In Progress', 'Closed'], default='Open')
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    username = StringField(required=True)  # The owner of the ticket
    responses = ListField(ReferenceField('TicketResponse'))

class TicketResponse(Document):
    ticket = ReferenceField(Ticket, required=True)
    username = String<PERSON>ield(required=True)  # The user who responded
    message = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
