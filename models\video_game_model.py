from mongoengine import Document, StringField, IntField, FloatField, ListField, BooleanField

class VideoGame(Document):
    name = StringField(required=True)
    release_year = IntField()
    rank = IntField()
    rating = FloatField()
    total_ratings = IntField()
    min_players = IntField()
    max_players = IntField()
    age_rating = StringField()
    play_time = IntField()
    description = StringField()
    image_url = StringField()
    developers = ListField(StringField())
    publishers = ListField(StringField())
    genres = ListField(StringField())
    platforms = ListField(StringField())
    metacritic_score = FloatField()
    user_score = FloatField()
    is_multiplayer = BooleanField()

    meta = {
        'db_alias': 'test',
        'collection': 'videogames',
        'indexes': [
            'name',
            'release_year',
            'rank',
            'rating',
            'min_players',
            'max_players',
            'play_time',
            'genres',
            'platforms',
            'metacritic_score',
            'user_score'
        ]
    }
