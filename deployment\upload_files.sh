#!/bin/bash
# Script to upload files to the server

# Set up error handling
set -e

echo "File Upload Script"
echo "================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not installed."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip install -r deployment/requirements.txt

# Get the path to upload
read -p "Enter the path to the file or directory you want to upload: " LOCAL_PATH

# Run the uploader script
echo "Running file uploader..."
python3 deployment/upload_files.py "$LOCAL_PATH"

echo "Done!"
