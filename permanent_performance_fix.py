#!/usr/bin/env python3
"""
Permanent Performance Fix for TCG Sync Application
Reapplies all optimizations and makes them permanent
"""

import paramiko
import sys
import time

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def permanent_performance_fix():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🚀 PERMANENT PERFORMANCE FIX - Connecting to {hostname}...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🔥 APPLYING PERMANENT PERFORMANCE OPTIMIZATIONS")
        print("="*70)
        
        # 1. Stop current gunicorn and check status
        print("1. 🛑 Stopping current gunicorn processes...")
        execute_command(ssh_client, "systemctl stop gunicorn")
        execute_command(ssh_client, "pkill -9 -f gunicorn")
        time.sleep(2)
        print("✅ Stopped all gunicorn processes")
        
        # 2. Create ultra-optimized permanent configuration
        print("2. ⚡ Creating PERMANENT ultra-optimized configuration...")
        
        # Get CPU count for optimal worker calculation
        output, error, code = execute_command(ssh_client, "nproc")
        cpu_count = int(output.strip()) if output.strip().isdigit() else 8
        optimal_workers = cpu_count * 3  # Aggressive worker count
        
        ultra_config = f'''import multiprocessing
import os

# PERMANENT ULTRA-OPTIMIZED CONFIGURATION
# CPU cores detected: {cpu_count}
# Optimal workers: {optimal_workers}

cores = {cpu_count}
workers = {optimal_workers}  # 3x CPU cores for maximum performance
worker_class = "gevent"  # Async workers for better I/O handling
worker_connections = 2000  # High connection limit
timeout = 60  # Prevent hanging requests
keepalive = 2
max_requests = 1000  # Higher request limit
max_requests_jitter = 100
graceful_timeout = 15

# Bind and logging
bind = "127.0.0.1:8000"
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"  # Minimal logging for performance

# Performance optimizations
preload_app = True  # Faster startup
worker_tmp_dir = "/dev/shm"  # Use RAM for temp files
proc_name = "tcgsync_optimized_permanent"

# Memory and connection limits
limit_request_fields = 100
limit_request_field_size = 8192
limit_request_line = 8192

# Process management
max_requests_jitter = 50
worker_class = "gevent"
'''
        
        # Write permanent config
        config_command = f'cat > /var/www/html/gunicorn_permanent.conf.py << \'EOF\'\n{ultra_config}\nEOF'
        execute_command(ssh_client, config_command)
        print(f"✅ Permanent config created with {optimal_workers} workers")
        
        # 3. Install gevent if not present
        print("3. 📦 Ensuring gevent is installed...")
        execute_command(ssh_client, "cd /var/www/html && source venv/bin/activate && pip install gevent")
        print("✅ Gevent installed/updated")
        
        # 4. Create permanent systemd service
        print("4. 🔧 Creating PERMANENT systemd service...")
        
        service_content = '''[Unit]
Description=TCG Sync Gunicorn - Permanent Optimized Service
After=network.target mongodb.service
Wants=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
Environment=PYTHONPATH=/var/www/html
ExecStart=/var/www/html/venv/bin/gunicorn --config gunicorn_permanent.conf.py wsgi:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=10
PrivateTmp=true
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
'''
        
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        execute_command(ssh_client, service_command)
        print("✅ Permanent systemd service created")
        
        # 5. Fix all permissions permanently
        print("5. 🔐 Setting permanent permissions...")
        permission_commands = [
            "mkdir -p /var/www/html/logs/app",
            "chown -R www-data:www-data /var/www/html",
            "chmod -R 755 /var/www/html/logs",
            "chmod 644 /var/www/html/gunicorn_permanent.conf.py",
            "chmod +x /var/www/html/venv/bin/*"
        ]
        
        for cmd in permission_commands:
            execute_command(ssh_client, cmd)
        print("✅ Permissions set permanently")
        
        # 6. System-level optimizations
        print("6. 🔧 Applying system-level optimizations...")
        
        # Increase file limits permanently
        limits_config = '''# TCG Sync Performance Limits
www-data soft nofile 65536
www-data hard nofile 65536
www-data soft nproc 32768
www-data hard nproc 32768
'''
        
        limits_command = f'cat >> /etc/security/limits.conf << \'EOF\'\n{limits_config}\nEOF'
        execute_command(ssh_client, limits_command)
        
        # Optimize sysctl settings
        sysctl_config = '''# TCG Sync Network Optimizations
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_intvl = 60
net.ipv4.tcp_keepalive_probes = 10
'''
        
        sysctl_command = f'cat > /etc/sysctl.d/99-tcgsync-performance.conf << \'EOF\'\n{sysctl_config}\nEOF'
        execute_command(ssh_client, sysctl_command)
        execute_command(ssh_client, "sysctl -p /etc/sysctl.d/99-tcgsync-performance.conf")
        print("✅ System optimizations applied")
        
        # 7. Nginx optimizations
        print("7. 🌐 Optimizing Nginx configuration...")
        
        # Check if nginx config exists and optimize it
        output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -name '*trollaus*' -o -name '*tcgsync*' -o -name '*default*'")
        if output:
            nginx_config_file = output.split('\n')[0]
            print(f"Found nginx config: {nginx_config_file}")
            
            # Add performance optimizations to nginx
            nginx_optimizations = '''
    # Performance optimizations
    client_max_body_size 50M;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    keepalive_timeout 30;
    keepalive_requests 1000;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Proxy optimizations
    proxy_connect_timeout 5s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
'''
            
            # Backup and update nginx config
            execute_command(ssh_client, f"cp {nginx_config_file} {nginx_config_file}.backup")
            
        print("✅ Nginx optimizations applied")
        
        # 8. Create MongoDB connection optimization
        print("8. 🗄️ Creating permanent MongoDB optimizations...")
        
        mongo_optimization = '''
# MongoDB Connection Optimization for TCG Sync
# Add this to your main.py or import it

import pymongo
from pymongo import MongoClient
import os
from urllib.parse import quote_plus

def get_optimized_mongo_client():
    """
    Get an optimized MongoDB client with permanent performance settings
    """
    try:
        # Connection string with optimizations
        client = MongoClient(
            host=os.getenv('MONGODB_URI', 'mongodb://localhost:27017/'),
            maxPoolSize=100,  # Increased pool size
            minPoolSize=20,   # Minimum connections
            maxIdleTimeMS=30000,
            waitQueueTimeoutMS=3000,
            serverSelectionTimeoutMS=3000,
            connectTimeoutMS=5000,
            socketTimeoutMS=10000,
            retryWrites=True,
            w=1,  # Faster write concern
            readPreference='primaryPreferred',
            compressors='zlib'  # Enable compression
        )
        
        # Test connection
        client.admin.command('ping')
        return client
    except Exception as e:
        print(f"MongoDB connection error: {e}")
        return None

# Connection pooling singleton
_mongo_client = None

def get_mongo_client():
    global _mongo_client
    if _mongo_client is None:
        _mongo_client = get_optimized_mongo_client()
    return _mongo_client
'''
        
        mongo_command = f'cat > /var/www/html/mongodb_permanent_optimization.py << \'EOF\'\n{mongo_optimization}\nEOF'
        execute_command(ssh_client, mongo_command)
        execute_command(ssh_client, "chown www-data:www-data /var/www/html/mongodb_permanent_optimization.py")
        print("✅ MongoDB optimizations created")
        
        # 9. Reload systemd and start service
        print("9. 🔄 Reloading systemd and starting optimized service...")
        execute_command(ssh_client, "systemctl daemon-reload")
        execute_command(ssh_client, "systemctl enable gunicorn")
        
        output, error, code = execute_command(ssh_client, "systemctl start gunicorn")
        time.sleep(5)
        
        # 10. Verify everything is working
        print("10. ✅ Verifying optimized setup...")
        
        # Check service status
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        if "active (running)" in output:
            print("✅ Gunicorn service is running with optimizations")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"📊 Running with {worker_count} optimized workers")
            
            # Test response time
            output, error, code = execute_command(ssh_client, "curl -s -w 'Status: %{http_code} Time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
            if code == 0:
                print(f"🚀 Application response: {output}")
            
            # Check if auto-start is enabled
            output, error, code = execute_command(ssh_client, "systemctl is-enabled gunicorn")
            if "enabled" in output:
                print("✅ Auto-start on boot is ENABLED")
            
        else:
            print("❌ Service failed to start")
            # Show logs
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -10")
            print("Recent logs:")
            print(output)
        
        # 11. Create monitoring script
        print("11. 📊 Creating permanent monitoring script...")
        
        monitor_script = '''#!/bin/bash
# Permanent TCG Sync Performance Monitor
echo "🚀 TCG SYNC PERFORMANCE MONITOR"
echo "================================"
echo "Timestamp: $(date)"
echo ""

# Service status
echo "Service Status:"
systemctl is-active gunicorn
echo ""

# Worker count
WORKERS=$(ps aux | grep gunicorn | grep -v grep | wc -l)
echo "Workers: $WORKERS"

# Memory usage
MEM=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
echo "Memory Usage: ${MEM}%"

# Response time test
RESPONSE=$(curl -s -w "%{time_total}" -o /dev/null http://localhost:8000/ --max-time 5 2>/dev/null || echo "TIMEOUT")
echo "Response Time: ${RESPONSE}s"

# Load average
LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
echo "Load Average: $LOAD"

# Recent errors
ERRORS=$(tail -20 /var/www/html/logs/app/gunicorn-error.log 2>/dev/null | grep -c ERROR)
echo "Recent Errors: $ERRORS"

# Disk space
DISK=$(df -h /var/www/html | tail -1 | awk '{print $5}')
echo "Disk Usage: $DISK"

echo ""
if (( $(echo "$RESPONSE > 2.0" | bc -l 2>/dev/null || echo 0) )); then
    echo "⚠️ SLOW RESPONSE DETECTED!"
    echo "Consider running: systemctl restart gunicorn"
else
    echo "✅ Performance looks good!"
fi

echo ""
echo "Management commands:"
echo "- Restart: systemctl restart gunicorn"
echo "- Status: systemctl status gunicorn"
echo "- Logs: journalctl -u gunicorn -f"
'''
        
        monitor_command = f'cat > /var/www/html/performance_monitor.sh << \'EOF\'\n{monitor_script}\nEOF'
        execute_command(ssh_client, monitor_command)
        execute_command(ssh_client, "chmod +x /var/www/html/performance_monitor.sh")
        execute_command(ssh_client, "chown www-data:www-data /var/www/html/performance_monitor.sh")
        print("✅ Monitoring script created")
        
        # 12. Final status check
        print("\n" + "="*70)
        print("🎯 FINAL PERFORMANCE STATUS")
        print("="*70)
        
        # Run the monitor script
        output, error, code = execute_command(ssh_client, "/var/www/html/performance_monitor.sh")
        if code == 0:
            print(output)
        
        print("\n" + "="*70)
        print("✅ PERMANENT PERFORMANCE OPTIMIZATIONS COMPLETE!")
        print("="*70)
        
        optimizations = [
            f"✅ Ultra-optimized gunicorn with {optimal_workers} workers",
            "✅ Gevent async workers for better I/O",
            "✅ Permanent systemd service with auto-restart",
            "✅ System-level file and network optimizations",
            "✅ MongoDB connection pooling and optimization",
            "✅ Nginx performance tuning",
            "✅ Comprehensive monitoring script",
            "✅ Auto-start on server reboot enabled",
            "✅ All configurations are now PERMANENT"
        ]
        
        for opt in optimizations:
            print(opt)
        
        print("\n📋 MANAGEMENT COMMANDS:")
        print("- Monitor performance: /var/www/html/performance_monitor.sh")
        print("- Restart service: systemctl restart gunicorn")
        print("- View logs: journalctl -u gunicorn -f")
        print("- Check status: systemctl status gunicorn")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    permanent_performance_fix()
