#!/usr/bin/env python3
"""
Verify Unix Socket Configuration
This script verifies that <PERSON><PERSON> is actually using the Unix socket,
and makes any necessary corrections to ensure optimal performance.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def verify_unix_socket():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to verify Unix socket configuration...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🔍 VERIFYING UNIX SOCKET CONFIGURATION")
        print("="*70)
        
        # 1. Check if gunicorn is running
        print("1. 🔍 Checking if gunicorn is running...")
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running")
        else:
            print("❌ Gunicorn service is not running")
            print("Status output:")
            print(output[:300])
            return False
        
        # 2. Check what gunicorn is actually binding to
        print("\n2. 🔍 Checking what gunicorn is binding to...")
        output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep")
        
        if output:
            print("Gunicorn processes:")
            print(output)
            
            # Check if any process is using the Unix socket
            if "unix:" in output:
                print("✅ Gunicorn is using a Unix socket")
                
                # Extract the socket path
                socket_path = None
                for line in output.split('\n'):
                    if "unix:" in line:
                        parts = line.split("unix:")
                        if len(parts) > 1:
                            socket_path = parts[1].split()[0]
                            break
                
                if socket_path:
                    print(f"Socket path: unix:{socket_path}")
                    
                    # Check if the socket file exists
                    output, error, code = execute_command(ssh_client, f"ls -la {socket_path}")
                    
                    if "No such file" not in error:
                        print(f"✅ Unix socket file exists: {socket_path}")
                    else:
                        print(f"❌ Unix socket file does not exist: {socket_path}")
            else:
                print("⚠️ Gunicorn may not be using a Unix socket")
                
                # Check if it's binding to a TCP port
                if "127.0.0.1" in output:
                    print("⚠️ Gunicorn is binding to a TCP port")
                    
                    # Extract the port
                    port = None
                    for line in output.split('\n'):
                        if "127.0.0.1:" in line:
                            parts = line.split("127.0.0.1:")
                            if len(parts) > 1:
                                port = parts[1].split()[0]
                                break
                    
                    if port:
                        print(f"TCP port: 127.0.0.1:{port}")
        
        # 3. Check the gunicorn configuration
        print("\n3. 🔍 Checking gunicorn configuration...")
        output, error, code = execute_command(ssh_client, "cat /var/www/html/gunicorn.conf.py")
        
        if output:
            print("Gunicorn configuration:")
            print(output)
            
            # Check if it's configured to use a Unix socket
            if "unix:" in output:
                print("✅ Gunicorn is configured to use a Unix socket")
                
                # Extract the socket path from configuration
                socket_path = None
                for line in output.split('\n'):
                    if "bind" in line and "unix:" in line:
                        parts = line.split("unix:")
                        if len(parts) > 1:
                            socket_path = parts[1].strip('"\' ')
                            break
                
                if socket_path:
                    print(f"Configured socket path: unix:{socket_path}")
            else:
                print("⚠️ Gunicorn is not configured to use a Unix socket")
        
        # 4. Check nginx configuration
        print("\n4. 🔍 Checking nginx configuration...")
        output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -type f | xargs grep -l 'unix:' 2>/dev/null")
        
        if output:
            print(f"Nginx configuration files using Unix socket: {output}")
            
            # Check the first file
            config_file = output.split('\n')[0]
            output, error, code = execute_command(ssh_client, f"cat {config_file}")
            
            if output:
                print(f"Nginx configuration ({config_file}):")
                print(output[:500] + "..." if len(output) > 500 else output)
                
                # Check if it's configured to use a Unix socket
                if "unix:" in output:
                    print("✅ Nginx is configured to use a Unix socket")
                    
                    # Extract the socket path from configuration
                    socket_path = None
                    for line in output.split('\n'):
                        if "server unix:" in line:
                            parts = line.split("unix:")
                            if len(parts) > 1:
                                socket_path = parts[1].split()[0].rstrip(';')
                                break
                    
                    if socket_path:
                        print(f"Configured socket path: unix:{socket_path}")
                else:
                    print("⚠️ Nginx is not configured to use a Unix socket")
        else:
            print("No nginx configuration files found using Unix socket")
            
            # Check if nginx is using proxy_pass to a TCP port
            output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -type f | xargs grep -l 'proxy_pass' 2>/dev/null")
            
            if output:
                print(f"Nginx configuration files using proxy_pass: {output}")
                
                # Check the first file
                config_file = output.split('\n')[0]
                output, error, code = execute_command(ssh_client, f"cat {config_file}")
                
                if output:
                    print(f"Nginx configuration ({config_file}):")
                    print(output[:500] + "..." if len(output) > 500 else output)
        
        # 5. Check if the socket directory exists and has correct permissions
        print("\n5. 🔍 Checking socket directory...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/run")
        
        if "No such file" not in error:
            print("Socket directory exists:")
            print(output)
            
            # Check permissions
            if "www-data" in output:
                print("✅ Socket directory has correct ownership")
            else:
                print("⚠️ Socket directory may have incorrect ownership, fixing...")
                execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
                execute_command(ssh_client, "chmod 755 /var/www/html/run")
        else:
            print("⚠️ Socket directory does not exist, creating it...")
            execute_command(ssh_client, "mkdir -p /var/www/html/run")
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
            execute_command(ssh_client, "chmod 755 /var/www/html/run")
        
        # 6. Check for any errors in the logs
        print("\n6. 🔍 Checking for errors in logs...")
        output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log | grep -i error | tail -10")
        
        if output:
            print("Errors in gunicorn error log:")
            print(output)
        else:
            print("No recent errors found in gunicorn error log")
        
        # 7. Run performance tests
        print("\n7. 📊 Running performance tests...")
        total_time = 0
        tests = 5
        
        for i in range(tests):
            output, error, code = execute_command(ssh_client, "curl -s -w '%{time_total}' -o /dev/null http://localhost/")
            if code == 0:
                try:
                    response_time = float(output.strip())
                    total_time += response_time
                    print(f"Test {i+1}: {response_time:.3f}s")
                except:
                    print(f"Test {i+1}: Failed to parse time")
            else:
                print(f"Test {i+1}: Failed")
            
            time.sleep(1)
        
        if total_time > 0:
            avg_time = total_time / tests
            print(f"\nAverage response time: {avg_time:.3f}s")
            
            if avg_time < 0.01:
                print("✅ EXCELLENT performance!")
            elif avg_time < 0.1:
                print("✅ VERY GOOD performance")
            elif avg_time < 1.0:
                print("✅ GOOD performance")
            else:
                print("⚠️ Performance could be improved")
        
        # 8. Final verification and summary
        print("\n" + "="*70)
        print("✅ UNIX SOCKET VERIFICATION COMPLETE!")
        print("="*70)
        
        # Check if we need to fix anything
        if "⚠️" in output:
            print("Some issues were detected that may need fixing:")
            
            # Create a fixed gunicorn configuration
            socket_path = "/var/www/html/run/gunicorn.sock"
            
            print("\n8. 🔧 Creating fixed gunicorn configuration...")
            
            # Get CPU count
            output, error, code = execute_command(ssh_client, "nproc")
            cpu_count = int(output.strip()) if output.strip().isdigit() else 4
            
            # Calculate optimal workers
            optimal_workers = cpu_count * 2
            optimal_workers = max(optimal_workers, 4)  # At least 4 workers
            
            fixed_config = f'''
# Ultra-Optimized Gunicorn Configuration with Unix Socket
workers = {optimal_workers}
worker_class = "gevent"
worker_connections = 1000
threads = 2
timeout = 120
keepalive = 5

# Unix socket for better performance
bind = "unix:{socket_path}"

# Request settings
max_requests = 1000
max_requests_jitter = 200
graceful_timeout = 30

# Logging
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"

# Performance optimizations
preload_app = True
worker_tmp_dir = "/dev/shm"

# Prevent thundering herd
forwarded_allow_ips = "*"
secure_scheme_headers = {{"X-Forwarded-Proto": "https"}}

def post_fork(server, worker):
    # Optimize worker settings after fork
    import gevent
    from gevent import monkey
    monkey.patch_all()
    
    # Set worker priority
    import os
    os.nice(10)
'''
            
            config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{fixed_config}\nEOF'
            output, error, code = execute_command(ssh_client, config_command)
            
            if code == 0:
                print("✅ Created fixed gunicorn configuration")
                
                # Set permissions
                execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
                execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
                
                # Restart service
                print("\n9. 🔄 Restarting service with fixed configuration...")
                execute_command(ssh_client, "systemctl restart gunicorn")
                time.sleep(5)
                
                # Check if service is running
                output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
                
                if "active (running)" in output:
                    print("✅ Gunicorn service is running with fixed configuration")
                    
                    # Check if socket file exists
                    output, error, code = execute_command(ssh_client, f"ls -la {socket_path}")
                    
                    if "No such file" not in error:
                        print(f"✅ Unix socket file exists: {socket_path}")
                    else:
                        print(f"❌ Unix socket file still does not exist: {socket_path}")
                        print("This may indicate a deeper issue with the socket configuration")
                else:
                    print("❌ Service failed to start with fixed configuration")
                    print("Status output:")
                    print(output[:300])
            else:
                print(f"❌ Failed to create fixed configuration: {error}")
        else:
            print("No issues detected, Unix socket configuration appears to be working correctly")
        
        print("\nTo manage the service, use these commands:")
        print("- Check status: systemctl status gunicorn")
        print("- Restart service: systemctl restart gunicorn")
        print("- View logs: journalctl -u gunicorn -f")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    verify_unix_socket()
