from mongoengine import Document, StringField, DateTimeField, FloatField, ReferenceField, BooleanField
from datetime import datetime
from models.staff_hours_model import StaffMember

class RotaEntry(Document):
    """Model for storing staff rota/schedule entries"""
    staff_member = ReferenceField(StaffMember, required=True)
    day = StringField(required=True, choices=['monday', 'tuesday', 'wednesday', 'thursday', 'friday'])
    start_time = StringField(required=True)  # Format: "HH:MM"
    end_time = StringField(required=True)    # Format: "HH:MM"
    week = StringField(required=True)        # Format: "YYYY-WXX" (ISO week format)
    created_by = StringField(required=True)  # Username of the creator
    created_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'rota_entries',
        'indexes': [
            'staff_member',
            'week',
            'day',
            'created_by'
        ]
    }

    def calculate_hours(self):
        """Calculate the number of hours for this shift"""
        start_hour, start_minute = map(int, self.start_time.split(':'))
        end_hour, end_minute = map(int, self.end_time.split(':'))

        # Convert to decimal hours
        start_decimal = start_hour + (start_minute / 60)
        end_decimal = end_hour + (end_minute / 60)

        # Handle overnight shifts
        if end_decimal < start_decimal:
            end_decimal += 24

        return round(end_decimal - start_decimal, 2)

    def calculate_earnings(self):
        """Calculate earnings for this shift based on staff member's hourly rate"""
        hours = self.calculate_hours()
        return round(hours * self.staff_member.hourly_rate, 2)
