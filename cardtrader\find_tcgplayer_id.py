from pymongo import MongoClient
import json

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# Find a sample record from matchedIds collection
record = cardtrader_db.matchedIds.find_one()

if record:
    print("Sample record structure:")
    # Convert ObjectId to string for JSON serialization
    record_dict = {k: str(v) if k == '_id' else v for k, v in record.items()}
    print(json.dumps(record_dict, indent=2))
    
    # Check available fields
    print("\nAvailable fields:")
    for field in record.keys():
        print(f"- {field}: {type(record[field]).__name__}")
    
    # Find records with TCGPlayer ID
    tcgplayer_records = list(cardtrader_db.matchedIds.find(
        {"tcg_player_id": {"$exists": True, "$ne": None}}
    ).limit(5))
    
    print(f"\nFound {len(tcgplayer_records)} records with TCGPlayer ID")
    for i, rec in enumerate(tcgplayer_records, 1):
        print(f"{i}. TCGPlayer ID: {rec.get('tcg_player_id')}")
        for field, value in rec.items():
            if field != '_id' and field != 'tcg_player_id':
                print(f"   - {field}: {value}")
else:
    print("No records found in matchedIds collection")
