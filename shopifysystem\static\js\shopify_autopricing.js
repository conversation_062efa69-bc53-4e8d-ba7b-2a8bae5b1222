// Toast notification function
function showToast(message, type = 'success') {
    const toastEl = document.getElementById('settingsToast');
    const toastMessage = document.getElementById('toastMessage');
    if (!toastEl || !toastMessage) return;
    const toast = new bootstrap.Toast(toastEl);

    toastMessage.textContent = message;
    toastEl.classList.toggle('bg-success', type === 'success');
    toastEl.classList.toggle('bg-danger', type === 'error');
    
    toast.show();
}

// Price Type List Movement Functions
function moveItem(element, direction) {
    const list = document.getElementById('priceTypeList');
    const items = Array.from(list.children);
    const index = items.indexOf(element);
    
    if (direction === 'up' && index > 0) {
        list.insertBefore(element, items[index - 1]);
    } else if (direction === 'down' && index < items.length - 1) {
        list.insertBefore(element, items[index + 1].nextSibling);
    }

    // Update button states
    updateMoveButtonStates();
}

// Update move button states based on position
function updateMoveButtonStates() {
    const items = Array.from(document.getElementById('priceTypeList').children);
    items.forEach((item, index) => {
        const upButton = item.querySelector('.move-up');
        const downButton = item.querySelector('.move-down');
        
        if (upButton) upButton.disabled = index === 0;
        if (downButton) downButton.disabled = index === items.length - 1;
    });
}

// Event listeners for move buttons
document.addEventListener('click', function(e) {
    const listItem = e.target.closest('.list-group-item');
    if (!listItem) return;

    if (e.target.closest('.move-up')) {
        moveItem(listItem, 'up');
    } else if (e.target.closest('.move-down')) {
        moveItem(listItem, 'down');
    }
});

// Function to save price type preference
function savePriceTypePreference() {
    const priceTypeList = document.getElementById('priceTypeList');
    if (!priceTypeList) {
        showToast('Price type list not found', 'error');
        return;
    }
    
    const priceTypes = Array.from(priceTypeList.children).map(item => item.dataset.type);
    console.log('Saving price types:', priceTypes);
    
    fetch('/api/price-preference', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ price_preference: priceTypes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showToast(data.error, 'error');
        } else {
            showToast(data.message || 'Price type preference saved successfully', 'success');
        }
    })
    .catch(error => {
        console.error('Error saving price type preference:', error);
        showToast('Error saving price type preference', 'error');
    });
}

// Function to save price comparison settings
function savePriceComparisonSettings() {
    // Get all price pairs
    const pairs = Array.from(document.querySelectorAll('.price-pair-container .input-group')).map(group => {
        const selects = group.querySelectorAll('select');
        return [selects[0].value, selects[1].value];
    });

    // Get existing modifiers
    const modifiers = {};
    document.querySelectorAll('.price-modifier').forEach(input => {
        const value = parseFloat(input.value);
        if (!isNaN(value) && value !== 0) {
            modifiers[input.dataset.type] = value;
        }
    });

    const settings = {
        use_highest_price: document.getElementById('useHighestPrice').checked,
        price_comparison_pairs: pairs,
        price_modifiers: modifiers
    };

    fetch('/api/price-comparison', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showToast(data.error, 'error');
        } else {
            showToast(data.message || 'Price comparison settings saved successfully', 'success');
            loadPriceComparisonSettings();
        }
    })
    .catch(error => {
        console.error('Error saving price comparison settings:', error);
        showToast('Error saving price comparison settings', 'error');
    });
}


// Function to create a select element
function createSelect(id, value = '', options = [
    { value: 'lowPrice', text: 'Low Price' },
    { value: 'marketPrice', text: 'Market Price' },
    { value: 'midPrice', text: 'Mid Price' },
    { value: 'highPrice', text: 'High Price' }
]) {
    const select = document.createElement('select');
    select.className = 'form-select';
    select.id = id;
    
    options.forEach(option => {
        const opt = document.createElement('option');
        opt.value = option.value;
        opt.textContent = option.text;
        opt.selected = option.value === value;
        select.appendChild(opt);
    });
    
    return select;
}

// Function to add a new price pair
function addPricePair(price1 = '', price2 = '') {
    const container = document.querySelector('.price-pair-container');
    if (!container) return;
    
    const pairDiv = document.createElement('div');
    pairDiv.className = 'input-group mb-2';
    
    const timestamp = Date.now();
    
    const innerDiv = document.createElement('div');
    innerDiv.className = 'd-flex align-items-center w-100';
    
    const select1 = createSelect(`price1_${timestamp}`, price1);
    innerDiv.appendChild(select1);
    
    const vsSpan = document.createElement('span');
    vsSpan.className = 'mx-2 text-light';
    vsSpan.textContent = 'vs';
    innerDiv.appendChild(vsSpan);
    
    const select2 = createSelect(`price2_${timestamp}`, price2);
    innerDiv.appendChild(select2);
    
    const removeButton = document.createElement('button');
    removeButton.className = 'btn btn-outline-danger ms-2 remove-pair';
    removeButton.title = 'Remove pair';
    removeButton.innerHTML = '<i class="fas fa-times"></i>';
    innerDiv.appendChild(removeButton);
    
    pairDiv.appendChild(innerDiv);
    container.appendChild(pairDiv);
}

// Function to load price comparison settings
function loadPriceComparisonSettings() {
    const container = document.querySelector('.price-pair-container');
    if (container) {
        container.innerHTML = '';
    }
    
    fetch('/api/price-comparison')
        .then(response => response.json())
        .then(data => {
            const useHighest = document.getElementById('useHighestPrice');
            if (useHighest) {
                useHighest.checked = data.use_highest_price || false;
            }
            
            if (data.price_comparison_pairs && Array.isArray(data.price_comparison_pairs)) {
                data.price_comparison_pairs.forEach(pair => {
                    if (Array.isArray(pair) && pair.length === 2) {
                        addPricePair(pair[0], pair[1]);
                    }
                });
            } else {
                addPricePair('lowPrice', 'marketPrice');
            }
            
            // Reset all modifiers to 0
            document.querySelectorAll('.price-modifier').forEach(input => {
                input.value = 0;
            });
            
            // Load price modifiers if they exist
            if (data.price_modifiers) {
                Object.entries(data.price_modifiers).forEach(([type, value]) => {
                    const input = document.querySelector(`.price-modifier[data-type="${type}"]`);
                    if (input) {
                        input.value = value;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error loading price comparison settings:', error);
            showToast('Error loading price comparison settings', 'error');
        });
}

// Add price pair button handler
const addPricePairBtn = document.getElementById('addPricePair');
if (addPricePairBtn) {
    addPricePairBtn.addEventListener('click', () => {
        addPricePair('lowPrice', 'marketPrice');
    });
}

// Remove price pair handler
const pricePairContainer = document.querySelector('.price-pair-container');
if (pricePairContainer) {
    pricePairContainer.addEventListener('click', (e) => {
        if (e.target.closest('.remove-pair')) {
            e.target.closest('.input-group').remove();
        }
    });
}

// Load price type preferences
function loadPriceTypePreferences() {
    fetch('/api/price-preference')
        .then(response => response.json())
        .then(data => {
            if (data.price_preference && Array.isArray(data.price_preference)) {
                const priceTypeList = document.getElementById('priceTypeList');
                const items = Array.from(priceTypeList.children);
                
                // Sort items according to saved preference
                data.price_preference.forEach(priceType => {
                    const item = items.find(item => item.dataset.type === priceType);
                    if (item) {
                        priceTypeList.appendChild(item);
                    }
                });
                
                updateMoveButtonStates();
            }
        })
        .catch(error => {
            console.error('Error loading price type preferences:', error);
            showToast('Error loading price type preferences', 'error');
        });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add click event listener to the Save Preference button
    const savePriceTypeBtn = document.getElementById('savePriceTypePreference');
    if (savePriceTypeBtn) {
        savePriceTypeBtn.addEventListener('click', savePriceTypePreference);
    }

    // Load settings
    loadPriceComparisonSettings();
    loadAutopricingSettings();
    loadPriceTypePreferences();

    // Initialize tooltips
    try {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } catch (error) {
        console.error('Error initializing tooltips:', error);
    }
});

// Load autopricing settings
function loadAutopricingSettings() {
    fetch('/api/settings')
        .then(response => response.json())
        .then(data => {
            const skuIdToggle = document.getElementById('useSkuIdPricing');
            if (skuIdToggle) {
                skuIdToggle.checked = data.use_skuid_pricing || false;
            }
            
            const skuIdWarning = document.getElementById('skuIdWarning');
            if (skuIdWarning && skuIdToggle) {
                skuIdWarning.style.display = data.use_skuid_pricing ? 'block' : 'none';
            }
            
            const roundingToggle = document.getElementById('priceRoundingEnabled');
            if (roundingToggle) {
                roundingToggle.checked = data.price_rounding_enabled || false;
            }
            
            const roundingSettings = document.getElementById('priceRoundingSettings');
            if (roundingSettings && roundingToggle) {
                roundingSettings.style.display = data.price_rounding_enabled ? 'block' : 'none';
            }
            
            // Clear only additional threshold inputs (keep first two and add button)
            const thresholdInputs = document.getElementById('thresholdInputs');
            if (thresholdInputs) {
                while (thresholdInputs.children.length > 3) {  // Keep first two inputs and add button
                    thresholdInputs.removeChild(thresholdInputs.lastChild);
                }
                
                // Ensure we have at least two input fields
                const existingInputs = thresholdInputs.querySelectorAll('.threshold-input');
                if (existingInputs.length < 2) {
                    // Add missing input fields
                    for (let i = existingInputs.length; i < 2; i++) {
                        addThresholdInput();
                    }
                }
            }
            
            // Set threshold values - always use first two from settings or defaults
            const thresholds = data.price_rounding_thresholds || [49, 99];
            if (thresholdInputs) {
                const inputs = thresholdInputs.querySelectorAll('.threshold-input');
                
                // Set first two inputs
                inputs[0].value = thresholds[0] || 49;
                inputs[1].value = thresholds[1] || 99;
                
                // Add any additional thresholds beyond the first two
                for (let i = 2; i < thresholds.length; i++) {
                    addThresholdInput(thresholds[i]);
                }
            }
            
            // Set other inputs
            if (document.getElementById('nmInput')) {
                document.getElementById('nmInput').value = data.nm_percent || '';
            }
            if (document.getElementById('lpInput')) {
                document.getElementById('lpInput').value = data.lp_percent || '';
            }
            if (document.getElementById('mpInput')) {
                document.getElementById('mpInput').value = data.mp_percent || '';
            }
            if (document.getElementById('hpInput')) {
                document.getElementById('hpInput').value = data.hp_percent || '';
            }
            if (document.getElementById('dmInput')) {
                document.getElementById('dmInput').value = data.dm_percent || '';
            }
            if (document.getElementById('minPriceInput')) {
                document.getElementById('minPriceInput').value = data.min_price || '';
            }
        })
        .catch(error => {
            console.error('Error loading autopricing settings:', error);
            showToast('Error loading autopricing settings', 'error');
        });
}

// Function to add a new threshold input
function addThresholdInput(value = '') {
    const thresholdInputs = document.getElementById('thresholdInputs');
    if (!thresholdInputs) return;
    
    const input = document.createElement('input');
    input.type = 'number';
    input.className = 'form-control threshold-input';
    input.min = 0;
    input.max = 99;
    input.placeholder = 'e.g. 49';
    input.value = value;
    
    const removeBtn = document.createElement('button');
    removeBtn.className = 'btn btn-outline-danger';
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
    removeBtn.onclick = function() {
        thresholdInputs.removeChild(input);
        thresholdInputs.removeChild(removeBtn);
    };
    
    thresholdInputs.insertBefore(input, thresholdInputs.lastChild);
    thresholdInputs.insertBefore(removeBtn, thresholdInputs.lastChild);
}

// Save autopricing settings
const saveAutopricingBtn = document.getElementById('saveAutopricingSettings');
if (saveAutopricingBtn) {
    saveAutopricingBtn.addEventListener('click', function() {
        // Get all threshold values
        const thresholdInputs = document.querySelectorAll('.threshold-input');
        const thresholds = Array.from(thresholdInputs)
            .map(input => parseInt(input.value))
            .filter(value => !isNaN(value) && value >= 0 && value <= 99);

        const settings = {
            use_skuid_pricing: document.getElementById('useSkuIdPricing').checked,
            price_rounding_enabled: document.getElementById('priceRoundingEnabled').checked,
            price_rounding_thresholds: thresholds,
            nm_percent: parseFloat(document.getElementById('nmInput').value) || 0,
            lp_percent: parseFloat(document.getElementById('lpInput').value) || 0,
            mp_percent: parseFloat(document.getElementById('mpInput').value) || 0,
            hp_percent: parseFloat(document.getElementById('hpInput').value) || 0,
            dm_percent: parseFloat(document.getElementById('dmInput').value) || 0,
            min_price: parseFloat(document.getElementById('minPriceInput').value) || 0
        };

        fetch('/api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showToast(data.error, 'error');
            } else {
                showToast(data.message, 'success');
                loadAutopricingSettings();
            }
        })
        .catch(error => {
            console.error('Error saving autopricing settings:', error);
            showToast('Error saving autopricing settings', 'error');
        });
    });
}

// Toggle price rounding settings visibility
const priceRoundingToggle = document.getElementById('priceRoundingEnabled');
if (priceRoundingToggle) {
    priceRoundingToggle.addEventListener('change', function() {
        const roundingSettings = document.getElementById('priceRoundingSettings');
        if (roundingSettings) {
            roundingSettings.style.display = this.checked ? 'block' : 'none';
        }
    });
}

// Add threshold button handler
const addThresholdBtn = document.getElementById('addThreshold');
if (addThresholdBtn) {
    addThresholdBtn.addEventListener('click', function() {
        addThresholdInput();
    });
}


// Toggle SKU ID warning visibility
const skuIdToggle = document.getElementById('useSkuIdPricing');
if (skuIdToggle) {
    skuIdToggle.addEventListener('change', function() {
        const skuIdWarning = document.getElementById('skuIdWarning');
        if (skuIdWarning) {
            skuIdWarning.style.display = this.checked ? 'block' : 'none';
        }
    });
}
