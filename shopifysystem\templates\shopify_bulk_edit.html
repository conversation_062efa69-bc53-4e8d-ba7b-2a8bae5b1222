v3
{% extends "base.html" %}

{% block title %}Shopify Bulk Editor - Image Repair{% endblock %}

{% block content %}
<div class="container-fluid mt-5">
    <div class="dashboard-header mb-4 text-center">
        <h1 style="color: red; font-weight: bold;">v3</h1>
        <h1 class="h2">Shopify Bulk Editor - Image Repair</h1>
        <p class="lead">Repair missing images for Shopify products</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header" style="background-color: #343a40; color: white;">
                    <h4>Filter Products</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 mb-2">
                            <label for="gameFilter">Game Type:</label>
                            <select id="gameFilter" class="form-control">
                                <option value="">All Games</option>
                                <!-- Game types will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="expansionFilter">Expansion:</label>
                            <select id="expansionFilter" class="form-control">
                                <option value="">All Expansions</option>
                                <!-- Expansion names will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="form-check mt-4">
                                <input type="checkbox" id="imageFilterToggle" class="form-check-input" checked>
                                <label class="form-check-label" for="imageFilterToggle">Show Only Products Needing Images</label>
                            </div>
                            <div class="form-check mt-2">
                                <input type="checkbox" id="selectAllProducts" class="form-check-input">
                                <label class="form-check-label" for="selectAllProducts">Select All Products</label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2 d-flex align-items-end">
                            <button id="repairImagesBtn" class="btn btn-primary me-2">Repair Images</button>
                            <button id="pushToShopifyBtn" class="btn btn-success me-2">Push to Shopify</button>
                            <button id="exportCsvBtn" class="btn btn-info me-2">Export CSV</button>
                            <a href="/shopify/products" class="btn btn-secondary text-white">Return to Products</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-2">
                <div id="resultCounter" class="text-white"></div>
                <div id="selectedCount" class="text-white"></div>
            </div>

            <div class="table-responsive">
                <table id="productTable" class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-white" style="width: 40px;">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                </div>
                            </th>
                            <th class="text-white">Image</th>
                            <th class="text-white">Title</th>
                            <th class="text-white">Card #</th>
                            <th class="text-white">Game</th>
                            <th class="text-white">Expansion</th>
                            <th class="text-white">Status</th>
                            <th class="text-white">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Products will be dynamically inserted here -->
                        <tr>
                            <td colspan="8" class="text-center text-white">
                                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                                    <p>Loading products...</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <nav aria-label="Product pagination">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination items will be dynamically inserted here -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- Modal for image preview -->
    <div class="modal fade" id="imagePreviewModal" tabindex="-1" role="dialog" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header">
                    <h5 class="modal-title" id="imagePreviewModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="previewImage" src="" alt="Preview" class="img-fluid">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="loading-text mt-3" id="loadingText">Processing...</div>
        <div class="progress mt-3" style="width: 80%; max-width: 500px; display: none;" id="progressContainer">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="progressBar"></div>
        </div>
        <div class="text-light mt-2" id="progressText" style="display: none;">0 of 0 completed</div>
    </div>
</div>

<style>
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        cursor: pointer;
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-needs-repair {
        background-color: #dc3545;
    }

    .status-repaired {
        background-color: #28a745;
    }

    .status-pushed {
        background-color: #17a2b8;
    }

    .status-verified {
        background-color: #28a745;
        border: 2px solid #fff;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }

    /* Ensure all table text is white */
    #productTable tbody tr {
        color: white !important;
    }

    #productTable tbody tr td {
        color: white !important;
    }
</style>

{% endblock %}

{% block scripts %}
<script>
    let currentPage = 1;
    let selectedProductIds = [];
    let allProducts = [];
    let gameFilter = '';
    let expansionFilter = '';
    let imageFilter = true;

    // Function to check server connection
    async function checkServerConnection() {
        try {
            console.log('Checking server connection...');
            const startTime = new Date().getTime();
            const response = await fetch('/shopify/bulk-edit/');
            const endTime = new Date().getTime();
            const responseTime = endTime - startTime;

            console.log(`Server connection check: status ${response.status}, response time ${responseTime}ms`);
            return true;
        } catch (error) {
            console.error('Server connection check failed:', error);
            showNotification('Error', 'Failed to connect to server. Please check if the server is running on port 8000.', 'error');
            return false;
        }
    }

    document.addEventListener('DOMContentLoaded', async function() {
        console.log('DOMContentLoaded event fired');

        try {
            // Check server connection first
            console.log('Checking server connection...');
            const serverConnected = await checkServerConnection();
            console.log('Server connection check result:', serverConnected);

            if (!serverConnected) {
                console.error('Server connection check failed, aborting data loading');
                return;
            }

            // Load game types
            console.log('Calling fetchGameTypes()');
            await fetchGameTypes();
            console.log('fetchGameTypes() completed');

            // Load initial products
            console.log('Calling fetchProducts(1)');
            await fetchProducts(1);
            console.log('fetchProducts(1) completed');
        } catch (error) {
            console.error('Error in DOMContentLoaded event handler:', error);
            alert('Error initializing page: ' + error.message);
        }

        // Add event listeners
    document.getElementById('gameFilter').addEventListener('change', function() {
        gameFilter = this.value;
        // When game changes, reload expansion names
        fetchExpansionNames();
        // Reset expansion filter
        expansionFilter = '';
        document.getElementById('expansionFilter').value = '';
        // Reload products with new game filter
        fetchProducts(1);
    });

    document.getElementById('expansionFilter').addEventListener('change', function() {
        expansionFilter = this.value;
        // Reload products with new expansion filter
        fetchProducts(1);
    });

        document.getElementById('imageFilterToggle').addEventListener('change', function() {
            imageFilter = this.checked;
            console.log('Image filter changed to:', imageFilter);
            fetchProducts(1);
        });

        document.getElementById('selectAllCheckbox').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                const productId = checkbox.getAttribute('data-product-id');
                if (this.checked) {
                    if (!selectedProductIds.includes(productId)) {
                        selectedProductIds.push(productId);
                    }
                } else {
                    selectedProductIds = selectedProductIds.filter(id => id !== productId);
                }
            });
            updateSelectedCount();
        });

        document.getElementById('selectAllProducts').addEventListener('change', function() {
            if (this.checked) {
                // Select all products across all pages
                selectedProductIds = allProducts.map(product => product._id);
            } else {
                // Deselect all products
                selectedProductIds = [];
            }
            // Update checkboxes on current page
            const checkboxes = document.querySelectorAll('.product-checkbox');
            checkboxes.forEach(checkbox => {
                const productId = checkbox.getAttribute('data-product-id');
                checkbox.checked = selectedProductIds.includes(productId);
            });
            updateSelectedCount();
        });

        document.getElementById('repairImagesBtn').addEventListener('click', function() {
            if (selectedProductIds.length === 0) {
                showNotification('Warning', 'Please select at least one product to repair', 'warning');
                return;
            }
            repairSelectedImages();
        });

        document.getElementById('pushToShopifyBtn').addEventListener('click', function() {
            if (selectedProductIds.length === 0) {
                showNotification('Warning', 'Please select at least one product to push to Shopify', 'warning');
                return;
            }
            pushToShopify();
        });

        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            exportToCsv();
        });

        // Event delegation for product table
        document.getElementById('productTable').addEventListener('click', function(event) {
            // Handle checkbox clicks
            if (event.target.classList.contains('product-checkbox')) {
                const checkbox = event.target;
                const productId = checkbox.getAttribute('data-product-id');
                if (checkbox.checked) {
                    if (!selectedProductIds.includes(productId)) {
                        selectedProductIds.push(productId);
                    }
                } else {
                    selectedProductIds = selectedProductIds.filter(id => id !== productId);
                }
                updateSelectedCount();
            }

            // Handle repair button clicks
            if (event.target.classList.contains('repair-btn')) {
                const productId = event.target.getAttribute('data-product-id');
                repairImage(productId);
            }

            // Handle image preview clicks
            if (event.target.classList.contains('product-image')) {
                const imageUrl = event.target.getAttribute('src');
                showImagePreview(imageUrl);
            }
        });
    });

    async function fetchGameTypes() {
        try {
            showLoading();
            console.log('Fetching game types...');

            const response = await fetch('/shopify/bulk-edit/api/game-types');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Game types response data:', data);

            const gameFilter = document.getElementById('gameFilter');
            gameFilter.innerHTML = '<option value="">All Games</option>';

            if (data.game_types && data.game_types.length > 0) {
                // Sort game types alphabetically
                const sortedGameTypes = data.game_types.sort();
                
                sortedGameTypes.forEach(gameType => {
                    if (gameType) {
                        const option = document.createElement('option');
                        option.value = gameType;
                        option.textContent = gameType;
                        gameFilter.appendChild(option);
                    }
                });
            } else {
                console.warn('No game types found in response');
            }
        } catch (error) {
            console.error('Error fetching product types:', error);
            showNotification('Error', 'Failed to load product types: ' + error.message, 'error');
        } finally {
            hideLoading();
        }
    }

    async function fetchExpansionNames() {
        try {
            showLoading();
            console.log('Fetching expansion names with game filter:', gameFilter);
            
            const expansionFilter = document.getElementById('expansionFilter');
            expansionFilter.innerHTML = '<option value="">All Expansions</option>';
            expansionFilter.disabled = true;
            
            if (!gameFilter) {
                hideLoading();
                return;
            }
            
            const response = await fetch(`/shopify/bulk-edit/api/expansion-names?game=${encodeURIComponent(gameFilter)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Expansion names response:', data);

            if (data.expansion_names && data.expansion_names.length > 0) {
                // Sort expansion names alphabetically
                const sortedExpansionNames = data.expansion_names.sort();
                
                sortedExpansionNames.forEach(expansionName => {
                    if (expansionName && typeof expansionName === 'string' && expansionName.trim()) {
                        const option = document.createElement('option');
                        option.value = expansionName;
                        option.textContent = expansionName;
                        expansionFilter.appendChild(option);
                    }
                });
                
                // Enable the expansion filter
                expansionFilter.disabled = false;
            } else {
                console.warn('No expansion names found in response');
            }
        } catch (error) {
            console.error('Error fetching expansion names:', error);
            showNotification('Error', 'Failed to load expansion names', 'error');
        } finally {
            hideLoading();
        }
    }

    async function fetchProducts(page) {
        try {
            showLoading();
            currentPage = page;

            console.log(`Fetching products for page ${page} with filters:`, {
                game_filter: gameFilter,
                expansion_filter: expansionFilter,
                image_filter: imageFilter
            });

            const params = new URLSearchParams({
                image_filter: imageFilter.toString(),
                page: page,
                per_page: 25
            });

            // Add game filter if provided
            if (gameFilter) {
                params.append('game', gameFilter);
            }

            // Add expansion filter if provided
            if (expansionFilter) {
                params.append('expansion', expansionFilter);
            }

            const response = await fetch(`/shopify/bulk-edit/api/products-needing-image-repair?${params.toString()}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log(`Received ${data.products.length} products out of ${data.total} total`);

            allProducts = data.products;

            renderProducts(data.products);
            renderPagination(data.page, data.total_pages);

            // Update result counter
            const resultCounter = document.getElementById('resultCounter');
            resultCounter.textContent = `Showing ${data.products.length} of ${data.total} products`;

            // Update selected count
            updateSelectedCount();
            
            // Update select all checkbox state
            document.getElementById('selectAllCheckbox').checked = false;
            document.getElementById('selectAllProducts').checked = false;
        } catch (error) {
            console.error('Error fetching products:', error);
            showNotification('Error', 'Failed to load products', 'error');
        } finally {
            hideLoading();
        }
    }

    function renderProducts(products) {
        const tbody = document.querySelector('#productTable tbody');
        tbody.innerHTML = '';

        console.log('Rendering products:', products);

        if (products.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-white">
                        <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                            <p>No products found that need image repair</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        // Filter out products with no title
        const validProducts = products.filter(product => product.title);
        console.log('Valid products (with titles):', validProducts.length);

        if (validProducts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-white">
                        <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                            <p>No products with valid titles found</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        validProducts.forEach((product, index) => {
            // Debug: Log card number for first few products
            if (index < 3) {
                console.log(`Product ${index + 1} card number:`, product.number);
            }

            const row = document.createElement('tr');
            row.classList.add('text-white');

            // Determine status
            let statusClass = 'status-needs-repair';
            let statusText = 'Needs Repair';

            if (product.image && product.image.src) {
                if (product.shopify_image_verified) {
                    statusClass = 'status-verified';
                    statusText = 'Verified on Shopify';
                } else if (product.needsPushing) {
                    statusClass = 'status-repaired';
                    statusText = 'Repaired (Not Pushed)';
                } else {
                    statusClass = 'status-pushed';
                    statusText = 'Pushed (Not Verified)';
                }
            }

            // Debug: Create card number cell content
            const cardNumberContent = product.number ? product.number : '<span class="text-muted">N/A</span>';
            if (index < 3) {
                console.log(`Product ${index + 1} card number content:`, cardNumberContent);
            }

            row.innerHTML = `
                <td class="text-center">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input product-checkbox"
                            data-product-id="${product._id}"
                            ${selectedProductIds.includes(product._id) ? 'checked' : ''}>
                    </div>
                </td>
                <td class="text-center">
                    ${product.image && product.image.src
                        ? `<img src="${product.image.src}" alt="${product.title}" class="product-image">`
                        : '<span class="badge bg-danger">No Image</span>'}
                </td>
                <td>${product.title}</td>
                <td>${cardNumberContent}</td>
                <td>${product.product_type || '<span class="text-muted">Unknown</span>'}</td>
                <td>${product.expansionName || '<span class="text-muted">Unknown</span>'}</td>
                <td>
                    <span class="badge status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <button class="btn btn-primary btn-sm repair-btn" data-product-id="${product._id}">
                        Repair Image
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });

        // Show a message about filtered products
        if (validProducts.length < products.length) {
            const filteredCount = products.length - validProducts.length;
            const infoRow = document.createElement('tr');
            infoRow.classList.add('text-white', 'bg-info', 'bg-opacity-25');
            infoRow.innerHTML = `
                <td colspan="8" class="text-center">
                    <small>${filteredCount} product(s) without titles were filtered out</small>
                </td>
            `;
            tbody.appendChild(infoRow);
        }
    }

    function renderPagination(currentPage, totalPages) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (totalPages <= 1) {
            return;
        }

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="#" ${currentPage > 1 ? `onclick="fetchProducts(${currentPage - 1}); return false;"` : ''}>
                Previous
            </a>
        `;
        pagination.appendChild(prevLi);

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `
                <a class="page-link" href="#" onclick="fetchProducts(${i}); return false;">
                    ${i}
                </a>
            `;
            pagination.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="#" ${currentPage < totalPages ? `onclick="fetchProducts(${currentPage + 1}); return false;"` : ''}>
                Next
            </a>
        `;
        pagination.appendChild(nextLi);
    }

    // Define changePage function for backward compatibility
    function changePage(page) {
        fetchProducts(page);
    }

    function updateSelectedCount() {
        const selectedCount = document.getElementById('selectedCount');
        selectedCount.textContent = `${selectedProductIds.length} products selected`;
    }

    async function repairImage(productId) {
        try {
            showLoading('Repairing image...');

            const response = await fetch('/shopify/bulk-edit/api/repair-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ product_id: productId })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Refresh products
                fetchProducts(currentPage);

                showNotification('Success', 'Image repaired and verified on Shopify', 'success');
            } else {
                throw new Error(data.message || 'Failed to repair image');
            }
        } catch (error) {
            console.error('Error repairing image:', error);
            showNotification('Error', error.message, 'error');
        } finally {
            hideLoading();
        }
    }

    async function repairSelectedImages() {
        try {
            // Show loading overlay with progress bar
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingText = document.getElementById('loadingText');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            loadingText.textContent = 'Repairing selected images...';
            progressContainer.style.display = 'block';
            progressText.style.display = 'block';
            loadingOverlay.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent scrolling while loading
            
            const totalProducts = selectedProductIds.length;
            let successCount = 0;
            let errorCount = 0;
            
            // Update initial progress
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
            progressText.textContent = `0 of ${totalProducts} completed`;
            
            // Process each product one by one
            for (let i = 0; i < totalProducts; i++) {
                const productId = selectedProductIds[i];
                
                try {
                    // Update progress text
                    loadingText.textContent = `Repairing image ${i + 1} of ${totalProducts}...`;
                    progressText.textContent = `${i} of ${totalProducts} completed`;
                    
                    // Call the repair API for this product
                    const response = await fetch('/shopify/bulk-edit/api/repair-image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ product_id: productId })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        successCount++;
                    } else {
                        errorCount++;
                        console.error(`Failed to repair image for product ${productId}: ${data.message || 'Unknown error'}`);
                    }
                } catch (error) {
                    errorCount++;
                    console.error(`Error repairing image for product ${productId}:`, error);
                }
                
                // Update progress bar
                const progress = ((i + 1) / totalProducts) * 100;
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressText.textContent = `${i + 1} of ${totalProducts} completed`;
                
                // Small delay to prevent UI freezing and allow progress bar to update
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // Refresh products
            await fetchProducts(currentPage);
            
            // Show completion notification
            showNotification(
                'Repair Complete',
                `Processed ${totalProducts} products: ${successCount} successful, ${errorCount} failed.`,
                successCount > 0 ? 'success' : 'warning'
            );
        } catch (error) {
            console.error('Error in bulk repair process:', error);
            showNotification('Error', `An error occurred during the repair process: ${error.message}`, 'error');
        } finally {
            // Hide progress elements
            const progressContainer = document.getElementById('progressContainer');
            const progressText = document.getElementById('progressText');
            progressContainer.style.display = 'none';
            progressText.style.display = 'none';
            
            // Hide loading overlay
            hideLoading();
        }
    }

    async function pushToShopify() {
        try {
            showLoading('Pushing to Shopify...');

            const response = await fetch('/shopify/bulk-edit/api/push-to-shopify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: selectedProductIds
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to push to Shopify');
            }

            const data = await response.json();

            // Count successes and failures
            const successCount = data.results.filter(result => result.success).length;
            const errorCount = data.results.filter(result => !result.success).length;

            // Refresh products
            fetchProducts(currentPage);

            showNotification(
                'Success',
                `Pushed ${successCount + errorCount} products to Shopify: ${successCount} verified, ${errorCount} failed`,
                'success'
            );
        } catch (error) {
            console.error('Error pushing to Shopify:', error);
            showNotification('Error', error.message, 'error');
        } finally {
            hideLoading();
        }
    }

    function showImagePreview(imageUrl) {
        const previewImage = document.getElementById('previewImage');
        previewImage.src = imageUrl;

        const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
        modal.show();
    }

    function showLoading(text = 'Loading...') {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingText = document.getElementById('loadingText');
        loadingText.textContent = text;
        loadingOverlay.style.display = 'flex';
    }

    function hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.style.display = 'none';
    }

    async function exportToCsv() {
        try {
            showLoading('Generating CSV...');

            // Build URL with image filter only
            const params = new URLSearchParams({
                image_filter: imageFilter.toString()
            });

            const url = `/shopify/bulk-edit/api/export-csv?${params.toString()}`;

            // Open the URL in a new tab to download the file
            window.open(url, '_blank');

            showNotification('Success', 'CSV export started. Check your downloads folder.', 'success');
        } catch (error) {
            console.error('Error exporting to CSV:', error);
            showNotification('Error', 'Failed to export CSV: ' + error.message, 'error');
        } finally {
            hideLoading();
        }
    }

    function showNotification(title, message, type = 'info') {
        console.log(`Showing notification: ${type} - ${title}: ${message}`);

        try {
            const notificationContainer = document.getElementById('notificationContainer') || createNotificationContainer();
            const notification = createNotificationElement(title, message, type);

            notificationContainer.appendChild(notification);

            // Check if bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap is not defined. Cannot show toast notification.');
                // Fallback to alert for critical errors
                if (type === 'error') {
                    alert(`${title}: ${message}`);
                }
                return;
            }

            const bsToast = new bootstrap.Toast(notification, { autohide: true, delay: 5000 });
            bsToast.show();

            notification.addEventListener('hidden.bs.toast', function () {
                notification.remove();
                if (notificationContainer.children.length === 0) {
                    notificationContainer.remove();
                }
            });
        } catch (error) {
            console.error('Error showing notification:', error);
            // Fallback to alert for critical errors
            if (type === 'error') {
                alert(`${title}: ${message}`);
            }
        }
    }

    function createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '10000';
        document.body.appendChild(container);
        return container;
    }

    function createNotificationElement(title, message, type) {
        const notification = document.createElement('div');
        notification.className = `toast bg-${type} text-white`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'assertive');
        notification.setAttribute('aria-atomic', 'true');

        notification.innerHTML = `
            <div class="toast-header bg-${type} text-white">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        return notification;
    }

    // Global function for pagination
    window.changePage = function(page) {
        fetchProducts(page);
    };
</script>
{% endblock %}
