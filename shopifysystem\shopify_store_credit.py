from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required

# Create a Blueprint for store credit routes
store_credit_bp = Blueprint('shopify_store_credit', __name__)

@store_credit_bp.route('/store_credit', methods=['GET'])
@login_required
def store_credit():
    """Render the store credit import page."""
    return render_template('binderstorecredit.html')

# Add this blueprint to your app in app.py
# app.register_blueprint(store_credit_bp, url_prefix='/shopify_store_credit')
