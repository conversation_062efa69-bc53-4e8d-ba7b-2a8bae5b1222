#!/usr/bin/env python3
"""
Verify Gunicorn Reboot Startup
This script verifies that gun<PERSON> is properly configured to start on system reboot,
and ensures all necessary configurations are in place for a successful startup.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def verify_reboot_startup():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to verify reboot startup...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🔍 VERIFYING GUNICORN REBOOT STARTUP")
        print("="*70)
        
        # 1. Check if gunicorn service is enabled
        print("1. 🔍 Checking if gunicorn service is enabled...")
        output, error, code = execute_command(ssh_client, "systemctl is-enabled gunicorn")
        
        if "enabled" in output:
            print("✅ Gunicorn service is ENABLED to start on boot")
        else:
            print(f"❌ Gunicorn service is NOT enabled to start on boot: {output}")
            
            # Enable the service
            print("Enabling gunicorn service...")
            execute_command(ssh_client, "systemctl enable gunicorn")
            
            # Check again
            output, error, code = execute_command(ssh_client, "systemctl is-enabled gunicorn")
            if "enabled" in output:
                print("✅ Gunicorn service is now ENABLED to start on boot")
            else:
                print(f"❌ Failed to enable gunicorn service: {output}")
        
        # 2. Check systemd service file
        print("\n2. 🔍 Checking systemd service file...")
        output, error, code = execute_command(ssh_client, "cat /etc/systemd/system/gunicorn.service")
        
        if output:
            print("Gunicorn service file exists:")
            print(output)
            
            # Check for After=network.target
            if "After=network.target" in output:
                print("✅ Service file has correct 'After=network.target' directive")
            else:
                print("⚠️ Service file may be missing 'After=network.target' directive")
            
            # Check for WantedBy=multi-user.target
            if "WantedBy=multi-user.target" in output:
                print("✅ Service file has correct 'WantedBy=multi-user.target' directive")
            else:
                print("⚠️ Service file may be missing 'WantedBy=multi-user.target' directive")
            
            # Check for Restart=always
            if "Restart=always" in output:
                print("✅ Service file has correct 'Restart=always' directive")
            else:
                print("⚠️ Service file may be missing 'Restart=always' directive")
        else:
            print(f"❌ Gunicorn service file not found: {error}")
        
        # 3. Check if gunicorn configuration file exists
        print("\n3. 🔍 Checking gunicorn configuration file...")
        output, error, code = execute_command(ssh_client, "cat /var/www/html/gunicorn.conf.py")
        
        if output:
            print("✅ Gunicorn configuration file exists")
        else:
            print(f"❌ Gunicorn configuration file not found: {error}")
        
        # 4. Check if socket directory exists and has correct permissions
        print("\n4. 🔍 Checking socket directory...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/run")
        
        if "No such file" not in error:
            print("✅ Socket directory exists")
            
            # Check permissions
            if "www-data" in output:
                print("✅ Socket directory has correct ownership")
            else:
                print("⚠️ Socket directory has incorrect ownership, fixing...")
                execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
                execute_command(ssh_client, "chmod 755 /var/www/html/run")
        else:
            print("⚠️ Socket directory does not exist, creating it...")
            execute_command(ssh_client, "mkdir -p /var/www/html/run")
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
            execute_command(ssh_client, "chmod 755 /var/www/html/run")
        
        # 5. Check if log directory exists and has correct permissions
        print("\n5. 🔍 Checking log directory...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/logs/app")
        
        if "No such file" not in error:
            print("✅ Log directory exists")
            
            # Check permissions
            if "www-data" in output:
                print("✅ Log directory has correct ownership")
            else:
                print("⚠️ Log directory has incorrect ownership, fixing...")
                execute_command(ssh_client, "chown -R www-data:www-data /var/www/html/logs")
                execute_command(ssh_client, "chmod -R 755 /var/www/html/logs")
        else:
            print("⚠️ Log directory does not exist, creating it...")
            execute_command(ssh_client, "mkdir -p /var/www/html/logs/app")
            execute_command(ssh_client, "chown -R www-data:www-data /var/www/html/logs")
            execute_command(ssh_client, "chmod -R 755 /var/www/html/logs")
        
        # 6. Check if wsgi.py file exists
        print("\n6. 🔍 Checking wsgi.py file...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/wsgi.py")
        
        if "No such file" not in error:
            print("✅ wsgi.py file exists")
        else:
            print(f"❌ wsgi.py file not found: {error}")
        
        # 7. Check if virtual environment exists
        print("\n7. 🔍 Checking virtual environment...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/venv/bin/python")
        
        if "No such file" not in error:
            print("✅ Virtual environment exists")
        else:
            print(f"❌ Virtual environment not found: {error}")
        
        # 8. Check if gunicorn is installed in virtual environment
        print("\n8. 🔍 Checking if gunicorn is installed...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/venv/bin/gunicorn")
        
        if "No such file" not in error:
            print("✅ Gunicorn is installed in virtual environment")
        else:
            print(f"❌ Gunicorn not found in virtual environment: {error}")
        
        # 9. Check if nginx is configured to work with gunicorn
        print("\n9. 🔍 Checking nginx configuration...")
        output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-enabled -type f | xargs grep -l 'proxy_pass' 2>/dev/null")
        
        if output:
            print("✅ Nginx configuration files found")
            
            # Check the first file
            config_file = output.split('\n')[0]
            output, error, code = execute_command(ssh_client, f"cat {config_file}")
            
            if "proxy_pass" in output:
                print("✅ Nginx is configured to proxy requests to gunicorn")
            else:
                print("⚠️ Nginx may not be properly configured to proxy requests to gunicorn")
        else:
            print("⚠️ No nginx configuration files found with proxy_pass")
        
        # 10. Simulate a reboot by restarting the service
        print("\n10. 🔄 Simulating reboot by restarting the service...")
        execute_command(ssh_client, "systemctl restart gunicorn")
        time.sleep(5)
        
        # Check if service started successfully
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service started successfully after restart")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Test response
            output, error, code = execute_command(ssh_client, "curl -s -w 'Response time: %{time_total}s\\n' -o /dev/null http://localhost/")
            
            if code == 0:
                print(f"✅ Application response after restart: {output}")
            else:
                print(f"❌ Failed to test response after restart: {error}")
        else:
            print("❌ Gunicorn service failed to start after restart")
            print("Status output:")
            print(output[:300])
        
        # 11. Final verification and summary
        print("\n" + "="*70)
        print("✅ REBOOT STARTUP VERIFICATION COMPLETE!")
        print("="*70)
        
        # Check for any warnings or errors
        if "❌" in locals().get('output', ''):
            print("⚠️ Some issues were detected that may affect startup on reboot")
            print("Please review the output above and fix any issues before rebooting")
        else:
            print("✅ All checks passed! Gunicorn should start correctly on system reboot")
            print("The following has been verified:")
            print("1. ✅ Gunicorn service is enabled to start on boot")
            print("2. ✅ Systemd service file is correctly configured")
            print("3. ✅ Gunicorn configuration file exists")
            print("4. ✅ Socket directory exists with correct permissions")
            print("5. ✅ Log directory exists with correct permissions")
            print("6. ✅ wsgi.py file exists")
            print("7. ✅ Virtual environment exists")
            print("8. ✅ Gunicorn is installed in virtual environment")
            print("9. ✅ Nginx is configured to work with gunicorn")
            print("10. ✅ Service starts successfully after restart")
        
        print("\nYou can now safely reboot the server to verify that gunicorn starts automatically")
        print("To reboot the server, use the command: 'reboot'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    verify_reboot_startup()
