#!/usr/bin/env python3
"""
Setup Gunicorn as a Systemd Service
Ensures gunicorn starts automatically on server reboot
"""

import paramiko
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def setup_gunicorn_service():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to setup gunicorn service...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*60)
        print("🚀 SETTING UP GUNICORN SYSTEMD SERVICE")
        print("="*60)
        
        # 1. Check current service status
        print("1. 📋 Checking current gunicorn service status...")
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        if "active (running)" in output:
            print("✅ Gunicorn service exists and is running")
        elif "inactive" in output or "failed" in output:
            print("⚠️ Gunicorn service exists but is not running")
        else:
            print("❌ No gunicorn service found")
        
        print(f"Current status: {output[:200]}...")
        
        # 2. Check if service file exists
        print("\n2. 📁 Checking for existing service file...")
        output, error, code = execute_command(ssh_client, "ls -la /etc/systemd/system/gunicorn.service")
        if code == 0:
            print("✅ Service file exists")
            # Show current content
            output, error, code = execute_command(ssh_client, "cat /etc/systemd/system/gunicorn.service")
            print("Current service file content:")
            print("-" * 40)
            print(output)
            print("-" * 40)
        else:
            print("❌ No service file found")
        
        # 3. Create optimized systemd service file
        print("\n3. 🔧 Creating optimized gunicorn systemd service...")
        
        service_content = '''[Unit]
Description=Gunicorn instance to serve TCG Sync Application
After=network.target
Wants=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
ExecStart=/var/www/html/venv/bin/gunicorn --config emergency.conf.py wsgi:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
'''
        
        # Write the service file
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        output, error, code = execute_command(ssh_client, service_command)
        if code == 0:
            print("✅ Service file created successfully")
        else:
            print(f"❌ Failed to create service file: {error}")
            return
        
        # 4. Reload systemd and enable service
        print("\n4. 🔄 Reloading systemd and enabling service...")
        
        # Reload systemd
        output, error, code = execute_command(ssh_client, "systemctl daemon-reload")
        if code == 0:
            print("✅ Systemd reloaded")
        else:
            print(f"❌ Failed to reload systemd: {error}")
        
        # Enable service to start on boot
        output, error, code = execute_command(ssh_client, "systemctl enable gunicorn")
        if code == 0:
            print("✅ Gunicorn service enabled for auto-start on boot")
        else:
            print(f"❌ Failed to enable service: {error}")
        
        # 5. Stop any existing gunicorn processes
        print("\n5. 🛑 Stopping any existing gunicorn processes...")
        execute_command(ssh_client, "pkill -f gunicorn")
        import time
        time.sleep(2)
        
        # 6. Start the service
        print("\n6. 🚀 Starting gunicorn service...")
        output, error, code = execute_command(ssh_client, "systemctl start gunicorn")
        if code == 0:
            print("✅ Gunicorn service started")
        else:
            print(f"❌ Failed to start service: {error}")
            print("Checking service logs...")
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l")
            print(output[-500:])  # Show last 500 chars
        
        # 7. Verify service status
        print("\n7. ✅ Verifying service status...")
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        if "active (running)" in output:
            print("✅ Gunicorn service is running!")
        else:
            print("❌ Service is not running properly")
        
        print("Service status:")
        print("-" * 40)
        print(output)
        print("-" * 40)
        
        # 8. Test the application
        print("\n8. 🧪 Testing application response...")
        output, error, code = execute_command(ssh_client, "curl -s -w 'Status: %{http_code} Time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
        if code == 0:
            print(f"✅ Application test: {output}")
        else:
            print(f"❌ Application test failed: {error}")
        
        # 9. Check if service is enabled for boot
        print("\n9. 🔍 Verifying auto-start configuration...")
        output, error, code = execute_command(ssh_client, "systemctl is-enabled gunicorn")
        if "enabled" in output:
            print("✅ Gunicorn will start automatically on boot")
        else:
            print("❌ Auto-start not configured properly")
        
        # 10. Show service management commands
        print("\n" + "="*60)
        print("📋 GUNICORN SERVICE MANAGEMENT COMMANDS")
        print("="*60)
        
        commands = [
            "# Check service status:",
            "systemctl status gunicorn",
            "",
            "# Start service:",
            "systemctl start gunicorn",
            "",
            "# Stop service:",
            "systemctl stop gunicorn",
            "",
            "# Restart service:",
            "systemctl restart gunicorn",
            "",
            "# Enable auto-start on boot:",
            "systemctl enable gunicorn",
            "",
            "# Disable auto-start:",
            "systemctl disable gunicorn",
            "",
            "# View service logs:",
            "journalctl -u gunicorn -f",
            "",
            "# Check if enabled for boot:",
            "systemctl is-enabled gunicorn"
        ]
        
        for cmd in commands:
            print(cmd)
        
        print("\n✅ SETUP COMPLETE!")
        print("Gunicorn is now configured as a systemd service and will:")
        print("- Start automatically on server reboot")
        print("- Restart automatically if it crashes")
        print("- Use the optimized emergency.conf.py configuration")
        print("- Run as www-data user for security")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    setup_gunicorn_service()
