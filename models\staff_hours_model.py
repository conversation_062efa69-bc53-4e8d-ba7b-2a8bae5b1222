from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>TimeField, <PERSON>loatField, ReferenceField, <PERSON>oleanField
from datetime import datetime
from models.user_model import User

class StaffMember(Document):
    """Model for storing staff member information"""
    name = StringField(required=True)
    email = StringField(required=True)
    phone = StringField()
    hourly_rate = FloatField(required=True, default=0.0)
    position = StringField()
    is_active = BooleanField(default=True)
    created_by = StringField(required=True)  # Username of the creator
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'staff_members',
        'indexes': [
            'name',
            'email',
            'created_by'
        ]
    }
    
    def __str__(self):
        return f"{self.name} ({self.position})"

class WorkingHours(Document):
    """Model for tracking staff working hours"""
    staff_member = ReferenceField(StaffMember, required=True)
    date = DateTimeField(required=True)
    start_time = DateTimeField(required=True)
    end_time = DateTimeField(required=True)
    break_duration = FloatField(default=0.0)  # Break duration in hours
    notes = StringField()
    created_by = StringField(required=True)  # Username of the creator
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'working_hours',
        'indexes': [
            'staff_member',
            'date',
            'created_by'
        ]
    }
    
    def calculate_hours(self):
        """Calculate the total working hours (excluding breaks)"""
        if not self.start_time or not self.end_time:
            return 0.0
            
        # Calculate duration in hours
        duration = (self.end_time - self.start_time).total_seconds() / 3600
        
        # Subtract break duration
        return max(0, duration - self.break_duration)
    
    def calculate_earnings(self):
        """Calculate earnings based on hours worked and hourly rate"""
        hours = self.calculate_hours()
        return hours * self.staff_member.hourly_rate
