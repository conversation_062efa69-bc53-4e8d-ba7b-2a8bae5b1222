from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from pymongo import MongoClient, ASCENDING, DESCENDING, UpdateOne
from bson import ObjectId
from datetime import datetime, timedelta
import requests
import json
import aiohttp
import asyncio
from models.customer_notes_model import CustomerNote

def create_customers_bp(mongo_client):
    notes_model = CustomerNote()
    # Test MongoDB connection at blueprint creation
    try:
        mongo_client.admin.command('ping')
        print("Initial MongoDB connection test successful")
    except Exception as mongo_error:
        print(f"Initial MongoDB connection error: {str(mongo_error)}")
        raise Exception(f"Failed to connect to MongoDB: {str(mongo_error)}")

    # Import store credit transfer routes
    from routes.shopify_customers.store_credit_transfer import init_routes as init_transfer_routes

    # Helper function for GraphQL requests
    async def execute_graphql_request(user, query, variables=None):
        """Execute a GraphQL request to Shopify"""
        if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
            raise Exception("Shopify credentials not found")

        shopify_store_name = user.shopifyStoreName
        api_key = user.shopifyAccessToken

        graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

        headers = {
            'X-Shopify-Access-Token': api_key,
            'Content-Type': 'application/json'
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                graphql_endpoint,
                headers=headers,
                json={"query": query, "variables": variables or {}}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Error from Shopify API: {response.status} - {error_text}")

    # Synchronous wrapper for GraphQL requests
    def execute_graphql(user, query, variables=None):
        """Synchronous wrapper for GraphQL requests"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(execute_graphql_request(user, query, variables))
            loop.close()
            return result
        except Exception as e:
            raise Exception(f"GraphQL request failed: {str(e)}")

    customers_bp = Blueprint('shopify_customers', __name__)

    # Initialize store credit transfer routes
    init_transfer_routes(customers_bp, mongo_client)

    @customers_bp.route('/shopify/customers', methods=['GET'])
    @login_required
    def get_customers():
        return render_template('shopify_customers.html')

    @customers_bp.route('/shopify/customers/api/customers', methods=['GET'])
    @login_required
    def fetch_customers():
        search_term = request.args.get('searchTerm', '')
        status = request.args.get('status', 'all')
        email_subscribed = request.args.get('emailSubscribed', 'false') == 'true'
        page = int(request.args.get('page', 1))
        per_page = 25
        sort_column = request.args.get('sortColumn', 'last_order_date')
        sort_order = request.args.get('sortOrder', 'desc')

        pipeline = [
            {'$match': {'username': current_user.username}}
        ]

        if search_term:
            pipeline.append({
                '$match': {
                    '$or': [
                        {'first_name': {'$regex': search_term, '$options': 'i'}},
                        {'last_name': {'$regex': search_term, '$options': 'i'}}
                    ]
                }
            })
        if status != 'all':
            pipeline.append({'$match': {'status': status}})
        if email_subscribed:
            pipeline.append({'$match': {'email_subscribed': email_subscribed}})

        # Sort before grouping to ensure we get the most recent record for each customer
        pipeline.append({'$sort': {'updated_at': -1}})

        pipeline.append({
            '$group': {
                '_id': '$id',
                'first_name': {'$first': '$first_name'},
                'last_name': {'$first': '$last_name'},
                'orders_count': {'$first': '$orders_count'},
                'total_spent': {'$first': {'$toDouble': '$total_spent'}},
                'updated_at': {'$first': '$updated_at'},
                'email_subscribed': {'$first': '$email_subscribed'},
                'status': {'$first': '$status'},
                'email': {'$first': '$email'}  # Include email in the projection
            }
        })

        if sort_column == 'name':
            sort_column = 'last_name'
        elif sort_column == 'last_order_date':
            sort_column = 'updated_at'

        sort_direction = -1 if sort_order == 'desc' else 1
        pipeline.append({'$sort': {sort_column: sort_direction}})

        pipeline.append({
            '$lookup': {
                'from': 'shGiftCards',
                'localField': '_id',
                'foreignField': 'customer_id',
                'as': 'gift_card'
            }
        })

        pipeline.append({
            '$project': {
                '_id': 0,
                'id': '$_id',
                'first_name': 1,
                'last_name': 1,
                'orders_count': 1,
                'total_spent': 1,
                'updated_at': 1,
                'last_order_date': '$updated_at',
                'accepts_marketing': 1,
                'email_subscribed': 1,
                'store_credit_balance': {
                    '$ifNull': [{'$arrayElemAt': ['$gift_card.balance', 0]}, 'N/A']
                }
            }
        })

        all_customers = list(mongo_client['test']['shCustomers'].aggregate(pipeline))
        total_customers = len(all_customers)
        paginated_customers = all_customers[(page - 1) * per_page : page * per_page]
        top_customers = sorted(all_customers, key=lambda x: x['total_spent'] or 0, reverse=True)[:4]

        for customer in paginated_customers + top_customers:
            if 'updated_at' in customer:
                customer['updated_at'] = customer['updated_at'].isoformat() if isinstance(customer['updated_at'], datetime) else customer['updated_at']
            if 'last_order_date' in customer:
                customer['last_order_date'] = customer['last_order_date'].isoformat() if isinstance(customer['last_order_date'], datetime) else customer['last_order_date']

        # Get user's currency
        from models.user_model import User
        user = User.objects(username=current_user.username).first()
        currency = user.currency if user else 'USD'

        return jsonify({
            'customers': paginated_customers,
            'total': total_customers,
            'page': page,
            'per_page': per_page,
            'top_customers': top_customers,
            'currency': currency
        })

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/store-credit/balance', methods=['GET'])
    @login_required
    def get_store_credit_balance(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # GraphQL query to get store credit balance
            query = """
            query {
              customer(id: "gid://shopify/Customer/%s") {
                id
                firstName
                lastName
                email
                storeCreditAccounts(first: 10) {
                  edges {
                    node {
                      id
                      balance {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """ % (customer_id)

            try:
                result = execute_graphql(user, query)

                # Extract customer data
                customer_data = result.get('data', {}).get('customer', {})
                if not customer_data:
                    return jsonify({"error": "Customer data not found in response"}), 404

                # Extract store credit accounts
                store_credit_accounts = customer_data.get('storeCreditAccounts', {}).get('edges', [])

                # Extract balance information
                balances = []
                for account_edge in store_credit_accounts:
                    account = account_edge.get('node', {})
                    account_id = account.get('id')
                    balance = account.get('balance', {})
                    amount = balance.get('amount')
                    currency = balance.get('currencyCode')

                    if amount and currency:
                        balances.append({
                            'account_id': account_id,
                            'amount': amount,
                            'currency': currency
                        })

                return jsonify({
                    "success": True,
                    "balances": balances,
                    "customer": {
                        "id": customer_data.get('id'),
                        "firstName": customer_data.get('firstName'),
                        "lastName": customer_data.get('lastName'),
                        "email": customer_data.get('email')
                    }
                })
            except Exception as e:
                return jsonify({"error": f"GraphQL request failed: {str(e)}"}), 500

        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/store-credit/add', methods=['POST'])
    @login_required
    def add_store_credit(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400

            amount = float(data['amount'])
            note = data.get('note', '')

            # Get user's currency
            currency = user.currency if hasattr(user, 'currency') and user.currency else 'GBP'

            # GraphQL mutation to add store credit
            mutation = """
            mutation {
              storeCreditAccountCredit(
                id: "gid://shopify/Customer/%s",
                creditInput: {
                  creditAmount: {
                    amount: "%s"
                    currencyCode: %s
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """ % (customer_id, str(amount), currency)

            try:
                result = execute_graphql(user, mutation)

                # Check for user errors
                user_errors = result.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
                if user_errors:
                    error_messages = [error.get('message') for error in user_errors]
                    return jsonify({"error": f"Shopify API errors: {', '.join(error_messages)}"}), 400

                # Check if credit was added successfully
                transaction = result.get('data', {}).get('storeCreditAccountCredit', {}).get('storeCreditAccountTransaction', {})
                if transaction:
                    amount_obj = transaction.get('amount', {})
                    amount = amount_obj.get('amount')
                    currency = amount_obj.get('currencyCode')

                    # Record the transaction in MongoDB for history
                    transaction_doc = {
                        'transaction_id': transaction.get('id'),
                        'customer_id': customer_id,
                        'amount': float(amount),
                        'currency': currency,
                        'type': 'credit',
                        'note': note,
                        'created_at': datetime.utcnow(),
                        'username': current_user.username
                    }
                    mongo_client['test']['shStoreCreditTransactions'].insert_one(transaction_doc)

                    return jsonify({
                        "success": True,
                        "message": f"Successfully added {amount} {currency} credit",
                        "transaction": transaction_doc
                    })
                else:
                    return jsonify({"error": "Unexpected response format from Shopify"}), 500
            except Exception as e:
                return jsonify({"error": f"GraphQL request failed: {str(e)}"}), 500

        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/store-credit/decrease', methods=['POST'])
    @login_required
    def decrease_store_credit(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400

            amount = float(data['amount'])
            note = data.get('note', '')

            # Get user's currency
            currency = user.currency if hasattr(user, 'currency') and user.currency else 'GBP'

            # GraphQL mutation to decrease store credit
            mutation = """
            mutation {
              storeCreditAccountDebit(
                id: "gid://shopify/Customer/%s",
                debitInput: {
                  debitAmount: {
                    amount: "%s"
                    currencyCode: %s
                  }
                }
              ) {
                storeCreditAccountTransaction {
                  id
                  amount {
                    amount
                    currencyCode
                  }
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """ % (customer_id, str(amount), currency)

            try:
                result = execute_graphql(user, mutation)

                # Check for user errors
                user_errors = result.get('data', {}).get('storeCreditAccountDebit', {}).get('userErrors', [])
                if user_errors:
                    error_messages = [error.get('message') for error in user_errors]
                    return jsonify({"error": f"Shopify API errors: {', '.join(error_messages)}"}), 400

                # Check if credit was decreased successfully
                transaction = result.get('data', {}).get('storeCreditAccountDebit', {}).get('storeCreditAccountTransaction', {})
                if transaction:
                    amount_obj = transaction.get('amount', {})
                    amount = amount_obj.get('amount')
                    currency = amount_obj.get('currencyCode')

                    # Record the transaction in MongoDB for history
                    transaction_doc = {
                        'transaction_id': transaction.get('id'),
                        'customer_id': customer_id,
                        'amount': float(amount),
                        'currency': currency,
                        'type': 'debit',
                        'note': note,
                        'created_at': datetime.utcnow(),
                        'username': current_user.username
                    }
                    mongo_client['test']['shStoreCreditTransactions'].insert_one(transaction_doc)

                    return jsonify({
                        "success": True,
                        "message": f"Successfully decreased {amount} {currency} credit",
                        "transaction": transaction_doc
                    })
                else:
                    return jsonify({"error": "Unexpected response format from Shopify"}), 500
            except Exception as e:
                return jsonify({"error": f"GraphQL request failed: {str(e)}"}), 500

        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/details', methods=['GET'])
    @login_required
    def get_customer_details(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Get customer details
            customer_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{customer_id}.json"
            customer_response = requests.get(customer_url, headers=headers)
            if customer_response.status_code != 200:
                return jsonify({"error": "Failed to fetch customer from Shopify"}), 500
            customer_data = customer_response.json()['customer']

            # Get customer orders
            orders_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{customer_id}/orders.json"
            orders_response = requests.get(orders_url, headers=headers)
            orders_data = []
            if orders_response.status_code == 200:
                orders_data = orders_response.json()['orders']

            # Get store credit balances using GraphQL
            try:
                # GraphQL query to get store credit balance
                query = """
                query {
                  customer(id: "gid://shopify/Customer/%s") {
                    storeCreditAccounts(first: 10) {
                      edges {
                        node {
                          id
                          balance {
                            amount
                            currencyCode
                          }
                        }
                      }
                    }
                  }
                }
                """ % (customer_id)

                store_credit_result = execute_graphql(user, query)

                # Extract store credit accounts
                store_credit_accounts = store_credit_result.get('data', {}).get('customer', {}).get('storeCreditAccounts', {}).get('edges', [])

                # Extract balance information
                store_credits = []
                for account_edge in store_credit_accounts:
                    account = account_edge.get('node', {})
                    account_id = account.get('id')
                    balance = account.get('balance', {})
                    amount = balance.get('amount')
                    currency = balance.get('currencyCode')

                    if amount and currency:
                        store_credits.append({
                            'code': account_id.split('/')[-1] if account_id else 'Unknown',
                            'balance': amount,
                            'created_at': datetime.utcnow().isoformat(),  # We don't have creation date from the API
                            'last_used_at': None
                        })
            except Exception as e:
                print(f"Error fetching store credit: {str(e)}")
                store_credits = []

            # For backward compatibility, also get gift cards
            gift_cards = list(mongo_client['test']['shGiftCards'].find(
                {'customer_id': customer_id},
                {'_id': 0, 'code': 1, 'balance': 1, 'created_at': 1, 'last_used_at': 1}
            ))

            # Combine store credits and gift cards, prioritizing store credits
            if store_credits:
                combined_credits = store_credits
            else:
                combined_credits = gift_cards

            return jsonify({
                "customer": customer_data,
                "orders": orders_data,
                "gift_cards": combined_credits
            })
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/gift-card', methods=['POST'])
    @login_required
    def create_gift_card(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            data = request.json
            if not data or 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Create gift card in Shopify
            shopify_data = {
                "gift_card": {
                    "note": data.get('note', ''),
                    "initial_value": data['amount'],
                    "customer_id": customer_id
                }
            }

            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards.json"
            response = requests.post(url, headers=headers, json=shopify_data)

            if response.status_code != 201:
                return jsonify({"error": f"Failed to create gift card in Shopify: {response.text}"}), 500

            gift_card_data = response.json()['gift_card']

            # Store in MongoDB
            gift_card_doc = {
                'id': str(gift_card_data['id']),
                'code': gift_card_data['code'],
                'balance': float(gift_card_data['balance']),
                'customer_id': customer_id,
                'created_at': datetime.utcnow(),
                'last_used_at': None,
                'note': data.get('note', ''),
                'username': current_user.username
            }

            mongo_client['test']['shGiftCards'].insert_one(gift_card_doc)

            return jsonify({
                "message": "Gift card created successfully",
                "gift_card": gift_card_doc
            }), 201
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/gift-card/<gift_card_id>/refresh', methods=['GET'])
    @login_required
    def refresh_gift_card_balance(customer_id, gift_card_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Try different ID formats
            possible_queries = [
                {'id': gift_card_id, 'customer_id': customer_id, 'username': current_user.username},
                {'id': str(gift_card_id), 'customer_id': customer_id, 'username': current_user.username},
                {'code': gift_card_id, 'customer_id': customer_id, 'username': current_user.username}
            ]

            gift_card = None
            for query in possible_queries:
                result = mongo_client['test']['shGiftCards'].find_one(query)
                if result:
                    gift_card = result
                    break

            if not gift_card:
                return jsonify({
                    "error": "Gift card not found",
                    "tried_queries": possible_queries
                }), 404

            # Get the actual gift card ID from our database record
            actual_gift_card_id = str(gift_card['id'])

            # Try to get the numeric ID from the URL if it's in that format
            if 'gid://shopify/GiftCard/' in str(actual_gift_card_id):
                actual_gift_card_id = str(actual_gift_card_id).split('/')[-1]
            elif not str(actual_gift_card_id).isdigit():
                # If not a URL and not numeric, try to get the ID from the database record
                actual_gift_card_id = str(gift_card['id'])
                if 'gid://shopify/GiftCard/' in actual_gift_card_id:
                    actual_gift_card_id = actual_gift_card_id.split('/')[-1]

            # Get gift card details from Shopify
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{actual_gift_card_id}.json"
            response = requests.get(url, headers=headers)

            if response.status_code != 200:
                return jsonify({"error": f"Failed to fetch gift card from Shopify: {response.text}"}), 500

            gift_card_data = response.json()['gift_card']
            current_balance = float(gift_card_data['balance'])

            # Update balance in MongoDB using the exact ID from the found document
            mongo_client['test']['shGiftCards'].update_one(
                {
                    'id': gift_card['id'],  # Use the exact ID from the found document
                    'customer_id': customer_id,
                    'username': current_user.username
                },
                {
                    '$set': {
                        'balance': current_balance
                    }
                }
            )

            return jsonify({
                "success": True,
                "balance": current_balance
            })
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/gift-card/<gift_card_id>/adjust', methods=['POST'])
    @login_required
    def adjust_gift_card_balance(customer_id, gift_card_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            print("Request data:", request.get_data())  # Debug raw request data
            print("Request form:", request.form)  # Debug form data
            print("Request JSON:", request.json)  # Debug JSON data

            # Try to get data from either JSON or form data
            data = request.json if request.is_json else request.form
            print("Processed data:", data)  # Debug processed data

            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Get adjustment amount from the 'amount' field
            if 'amount' not in data:
                return jsonify({"error": "Amount is required"}), 400
            if 'staff_name' not in data:
                return jsonify({"error": "Staff name is required"}), 400

            # Process the adjustment amount
            adjustment_amount = float(data['amount'])
            if not data.get('is_increase', True):  # If decreasing balance
                adjustment_amount = -adjustment_amount

            # First, let's see what gift cards exist for this customer
            all_customer_cards = list(mongo_client['test']['shGiftCards'].find({
                'customer_id': customer_id,
                'username': current_user.username
            }))
            print("All customer gift cards:", all_customer_cards)  # Debug print
            print("Looking for gift card ID:", gift_card_id)  # Debug print

            # Try different ID formats
            possible_queries = [
                {'id': gift_card_id, 'customer_id': customer_id, 'username': current_user.username},
                {'id': str(gift_card_id), 'customer_id': customer_id, 'username': current_user.username},
                {'code': gift_card_id, 'customer_id': customer_id, 'username': current_user.username}
            ]

            gift_card = None
            for query in possible_queries:
                print("Trying query:", query)  # Debug print
                result = mongo_client['test']['shGiftCards'].find_one(query)
                if result:
                    gift_card = result
                    print("Found gift card with query:", query)  # Debug print
                    break

            if not gift_card:
                return jsonify({
                    "error": "Gift card not found",
                    "tried_queries": possible_queries,
                    "available_cards": all_customer_cards
                }), 404

            current_balance = float(gift_card['balance'])
            new_balance = current_balance + adjustment_amount

            if new_balance < 0:
                return jsonify({"error": "Adjustment would result in negative balance"}), 400

            note = data.get('note', '')

            # Update gift card in Shopify
            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Create adjustment in Shopify
            adjustment_data = {
                "adjustment": {
                    "amount": adjustment_amount,
                    "note": note or "Balance adjustment"
                }
            }

            # Get the gift card ID from our database record
            actual_gift_card_id = gift_card_id
            print("Gift card from DB:", gift_card)  # Debug print
            print("Gift card ID from DB:", actual_gift_card_id)  # Debug print

            # Try to get the numeric ID from the URL if it's in that format
            if 'gid://shopify/GiftCard/' in str(actual_gift_card_id):
                actual_gift_card_id = str(actual_gift_card_id).split('/')[-1]
            elif not str(actual_gift_card_id).isdigit():
                # If not a URL and not numeric, try to get the ID from the database record
                actual_gift_card_id = str(gift_card['id'])
                if 'gid://shopify/GiftCard/' in actual_gift_card_id:
                    actual_gift_card_id = actual_gift_card_id.split('/')[-1]

            print("Using gift card ID:", actual_gift_card_id)  # Debug print
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{actual_gift_card_id}/adjustments.json"
            response = requests.post(url, headers=headers, json=adjustment_data)

            if response.status_code != 201:
                return jsonify({"error": f"Failed to update gift card in Shopify: {response.text}"}), 500

            # Update in MongoDB
            # Use the same ID we found in the gift card document
            update_result = mongo_client['test']['shGiftCards'].update_one(
                {
                    'id': gift_card['id'],  # Use the exact ID from the found document
                    'customer_id': customer_id,
                    'username': current_user.username
                },
                {
                    '$set': {
                        'balance': new_balance,
                        'last_used_at': datetime.utcnow(),
                        'last_adjusted_by': data['staff_name'],
                        'last_adjustment_note': note
                    }
                }
            )

            if update_result.modified_count == 0:
                return jsonify({"error": "Failed to update gift card in database"}), 500

            return jsonify({
                "message": "Gift card balance adjusted successfully",
                "previous_balance": current_balance,
                "adjustment": adjustment_amount,
                "new_balance": new_balance
            })
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>', methods=['GET'])
    @login_required
    def get_customer(customer_id):
        pipeline = [
            {'$match': {'id': customer_id, 'username': current_user.username}},
            {'$lookup': {
                'from': 'shGiftCards',
                'localField': 'id',
                'foreignField': 'customer_id',
                'as': 'gift_card'
            }},
            {'$project': {
                '_id': 0,
                'id': 1,
                'first_name': 1,
                'last_name': 1,
                'orders_count': 1,
                'total_spent': {'$toDouble': '$total_spent'},
                'updated_at': 1,
                'last_order_date': '$updated_at',
                'email': 1,
                'store_credit_balance': {
                    '$ifNull': [{'$arrayElemAt': ['$gift_card.balance', 0]}, 'N/A']
                }
            }}
        ]

        customer = list(mongo_client['test']['shCustomers'].aggregate(pipeline))

        if customer:
            customer = customer[0]
            if 'updated_at' in customer:
                customer['updated_at'] = customer['updated_at'].isoformat() if isinstance(customer['updated_at'], datetime) else customer['updated_at']
            if 'last_order_date' in customer:
                customer['last_order_date'] = customer['last_order_date'].isoformat() if isinstance(customer['last_order_date'], datetime) else customer['last_order_date']
            return jsonify(customer)
        else:
            return jsonify({"message": "Customer not found"}), 404

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>', methods=['PUT'])
    @login_required
    def update_customer(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # Test MongoDB connection
            try:
                mongo_client.admin.command('ping')
                print("MongoDB connection successful")
            except Exception as mongo_error:
                print(f"MongoDB connection error: {str(mongo_error)}")
                return jsonify({"error": f"Database connection error: {str(mongo_error)}"}), 500

            data = request.json
            if not data:
                return jsonify({"error": "No data provided"}), 400

            required_fields = ['firstName', 'lastName', 'email']
            missing_fields = [field for field in required_fields if field not in data]

            if missing_fields:
                return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400
                return jsonify({"error": "Missing required fields"}), 400

            # Update in Shopify first
            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            shopify_data = {
                "customer": {
                    "id": customer_id,
                    "first_name": data['firstName'],
                    "last_name": data['lastName'],
                    "email": data['email']
                }
            }

            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{customer_id}.json"
            response = requests.put(url, headers=headers, json=shopify_data)

            if response.status_code != 200:
                return jsonify({"error": f"Failed to update customer in Shopify: {response.text}"}), 500

            # If Shopify update successful, update in MongoDB
            update_result = mongo_client['test']['shCustomers'].update_one(
                {'id': customer_id, 'username': current_user.username},
                {'$set': {
                    'first_name': data['firstName'],
                    'last_name': data['lastName'],
                    'email': data['email']
                }}
            )

            if update_result.modified_count == 0:
                return jsonify({"error": "Customer not found in database"}), 404

            return jsonify({"message": "Customer updated successfully"})
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer', methods=['POST'])
    @login_required
    def create_customer():
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # Test MongoDB connection
            try:
                mongo_client.admin.command('ping')
                print("MongoDB connection successful")
            except Exception as mongo_error:
                print(f"MongoDB connection error: {str(mongo_error)}")
                return jsonify({"error": f"Database connection error: {str(mongo_error)}"}), 500

            data = request.json
            if not data:
                return jsonify({"error": "No data provided"}), 400

            required_fields = ['firstName', 'lastName', 'email']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400

            # Create in Shopify first
            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            shopify_data = {
                "customer": {
                    "first_name": data['firstName'],
                    "last_name": data['lastName'],
                    "email": data['email'],
                    "accepts_marketing": data.get('marketingConsent', False)
                }
            }

            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers.json"
            response = requests.post(url, headers=headers, json=shopify_data)

            response_data = response.json()
            if response.status_code != 201:
                return jsonify({"error": f"Failed to create customer in Shopify: {response_data}"}), 500

            customer_data = response_data.get('customer', {})
            if not customer_data:
                return jsonify({"error": "No customer data in Shopify response"}), 500

            # Store in MongoDB
            customer_doc = {
                'id': str(customer_data['id']),
                'first_name': customer_data['first_name'],
                'last_name': customer_data['last_name'],
                'email': customer_data['email'],
                'total_spent': float(customer_data.get('total_spent', 0)),
                'orders_count': int(customer_data.get('orders_count', 0)),
                'accepts_marketing': customer_data.get('accepts_marketing', False),
                'email_subscribed': customer_data.get('accepts_marketing', False),
                'updated_at': datetime.utcnow(),
                'username': current_user.username
            }

            mongo_client['test']['shCustomers'].insert_one(customer_doc)

            # Add initial store credit using the native store credit system
            initial_credit = float(data.get('initialCredit', 0))
            if initial_credit > 0:
                # Now that we have the customer ID, use the native store credit system
                customer_id = str(customer_data['id'])

                # Get user's currency
                currency = user.currency if hasattr(user, 'currency') and user.currency else 'GBP'

                # GraphQL mutation to add store credit
                mutation = """
                mutation {
                  storeCreditAccountCredit(
                    id: "gid://shopify/Customer/%s",
                    creditInput: {
                      creditAmount: {
                        amount: "%s"
                        currencyCode: %s
                      }
                    }
                  ) {
                    storeCreditAccountTransaction {
                      id
                      amount {
                        amount
                        currencyCode
                      }
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
                """ % (customer_id, str(initial_credit), currency)

                try:
                    result = execute_graphql(user, mutation)

                    # Check for user errors
                    user_errors = result.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
                    if user_errors:
                        error_messages = [error.get('message') for error in user_errors]
                        return jsonify({
                            "message": "Customer created successfully, but store credit creation failed",
                            "customer": customer_doc,
                            "error": f"Failed to create store credit: {', '.join(error_messages)}"
                        }), 201

                    # Check if credit was added successfully
                    transaction = result.get('data', {}).get('storeCreditAccountCredit', {}).get('storeCreditAccountTransaction', {})
                    if transaction:
                        amount_obj = transaction.get('amount', {})
                        amount = amount_obj.get('amount')
                        currency = amount_obj.get('currencyCode')

                        # Record the transaction in MongoDB for history
                        transaction_doc = {
                            'transaction_id': transaction.get('id'),
                            'customer_id': customer_id,
                            'amount': float(amount),
                            'currency': currency,
                            'type': 'credit',
                            'note': "Initial store credit",
                            'created_at': datetime.utcnow(),
                            'username': current_user.username
                        }
                        mongo_client['test']['shStoreCreditTransactions'].insert_one(transaction_doc)

                        return jsonify({
                            "success": True,
                            "message": "Customer created successfully with store credit",
                            "customer": customer_doc,
                            "transaction": transaction_doc
                        }), 201
                    else:
                        return jsonify({
                            "success": True,
                            "message": "Customer created successfully, but store credit creation had an unexpected response",
                            "customer": customer_doc
                        }), 201
                except Exception as e:
                    return jsonify({
                        "message": "Customer created successfully, but store credit creation failed",
                        "customer": customer_doc,
                        "error": f"Failed to create store credit: {str(e)}"
                    }), 201

            # If no initial credit was requested, just return success
            return jsonify({
                "success": True,
                "message": "Customer created successfully",
                "customer": customer_doc
            }), 201
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>', methods=['DELETE'])
    @login_required
    def delete_customer(customer_id):
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            # Delete from Shopify first
            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            # Delete customer using the dedicated deletion endpoint
            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{customer_id}/delete.json"
            response = requests.post(url, headers=headers)

            if response.status_code not in [200, 204]:
                return jsonify({"error": f"Failed to delete customer in Shopify: {response.text}"}), 500

            # If Shopify delete successful, delete from MongoDB
            result = mongo_client['test']['shCustomers'].delete_one({'id': customer_id, 'username': current_user.username})
            if result.deleted_count == 0:
                return jsonify({"error": "Customer not found in database"}), 404

            return jsonify({"message": "Customer deleted successfully"}), 200
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/refresh-customers', methods=['POST'])
    @login_required
    def refresh_customers():
        from models.user_model import User
        try:
            user = User.objects(username=current_user.username).first()
            if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                return jsonify({"error": "Shopify credentials not found"}), 400

            shopify_access_token = user.shopifyAccessToken
            shopify_store_name = user.shopifyStoreName

            headers = {
                'X-Shopify-Access-Token': shopify_access_token,
                'Content-Type': 'application/json'
            }

            updated_at_min = (datetime.utcnow() - timedelta(days=1)).isoformat()

            url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers.json"
            params = {
                'updated_at_min': updated_at_min,
                'limit': 250
            }

            new_customers = 0
            bulk_operations = []

            while url:
                response = requests.get(url, headers=headers, params=params)

                if response.status_code != 200:
                    return jsonify({"error": "Failed to fetch customers from Shopify"}), 500

                customers_data = response.json()['customers']

                for customer in customers_data:
                    total_spent = float(customer.get('total_spent', 0)) if customer.get('total_spent') else 0.0
                    orders_count = customer.get('orders_count', 0)

                    customer_doc = {
                        'id': str(customer['id']),
                        'first_name': customer['first_name'],
                        'last_name': customer['last_name'],
                        'email': customer['email'],
                        'total_spent': total_spent,
                        'orders_count': orders_count,
                        'accepts_marketing': customer.get('accepts_marketing', False),
                        'email_subscribed': customer.get('accepts_marketing', False),
                        'updated_at': customer['updated_at'],
                        'username': current_user.username
                    }

                    bulk_operations.append(
                        UpdateOne(
                            {'id': customer_doc['id'], 'username': current_user.username},
                            {'$set': customer_doc},
                            upsert=True
                        )
                    )

                new_customers += len(customers_data)

                link_header = response.headers.get('Link')
                url = None
                if link_header:
                    links = requests.utils.parse_header_links(link_header)
                    next_link = next((link for link in links if link['rel'] == 'next'), None)
                    if next_link:
                        url = next_link['url']
                        params = {}

            if bulk_operations:
                mongo_client['test']['shCustomers'].bulk_write(bulk_operations)

            return jsonify({"new_customers": new_customers})
        except Exception as e:
            return jsonify({"error": f"An error occurred: {str(e)}"}), 500

    @customers_bp.route('/shopify/customers/api/customer/<customer_id>/notes', methods=['POST', 'GET'])
    @login_required
    def handle_customer_notes(customer_id):
        try:
            if request.method == 'GET':
                notes = notes_model.get_customer_notes(mongo_client.tcg, customer_id)
                return jsonify({"success": True, "notes": notes})
            else:  # POST
                data = request.get_json()
                if "text" not in data or "staff_name" not in data:
                    return jsonify({"error": "Note text and staff name are required"}), 400

                note_id = notes_model.create_note(mongo_client.tcg, customer_id, data["text"], current_user.username, data["staff_name"])
                return jsonify({"success": True, "note_id": note_id})
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    # Create indexes for notes collection
    notes_model.create_indexes(mongo_client.tcg)

    return customers_bp

mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
try:
    client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    # Force a connection to verify it works
    client.admin.command('ping')
    print("MongoDB connection successful at module level")
    shopify_customers_bp = create_customers_bp(client)
except Exception as e:
    print(f"Failed to connect to MongoDB: {str(e)}")
    raise
