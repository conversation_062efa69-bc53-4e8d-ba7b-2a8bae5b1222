/* Enhanced Pricing Cards Styles */

.pricing-cards-container {
    margin: 2rem 0;
}

.pricing-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    will-change: transform, box-shadow;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.pricing-card:hover::before {
    opacity: 1;
}

.android-card {
    border-image: linear-gradient(135deg, #4CAF50, #66BB6A) 1;
}

.ios-card {
    border-image: linear-gradient(135deg, #ffffff, #f8f9fa) 1;
}

.bundle-card {
    border-image: linear-gradient(135deg, #ffc107, #ff9800) 1;
}

.platform-icon {
    position: relative;
    transition: all 0.3s ease;
}

.platform-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s ease;
    border-radius: inherit;
}

.pricing-card:hover .platform-icon::after {
    transform: translate(-50%, -50%) scale(1);
}

.price-display {
    position: relative;
    display: inline-block;
}

.price-display::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffc107, #ff9800);
    transform: translateX(-50%);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.bundle-card:hover .price-display::after {
    width: 80%;
}

.features-list {
    position: relative;
}

.features-list div {
    opacity: 0.9;
    transition: all 0.2s ease;
}

.pricing-card:hover .features-list div {
    opacity: 1;
    transform: translateX(5px);
}

.features-list div:nth-child(1) { transition-delay: 0.1s; }
.features-list div:nth-child(2) { transition-delay: 0.15s; }
.features-list div:nth-child(3) { transition-delay: 0.2s; }
.features-list div:nth-child(4) { transition-delay: 0.25s; }

.cta-button {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.cta-button:hover::before {
    left: 100%;
}

/* Popular badge styles removed */

/* Bounce animation removed */

.platform-badge {
    position: relative;
    z-index: 5;
    backdrop-filter: blur(10px);
}

.pricing-note {
    position: relative;
    overflow: hidden;
}

.pricing-note::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.8s ease;
}

.pricing-card:hover .pricing-note::before {
    left: 100%;
}

.guarantee {
    position: relative;
    transition: all 0.3s ease;
}

.pricing-card:hover .guarantee {
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .pricing-cards-container .col-lg-4 {
        margin-bottom: 2rem;
    }
    
    .bundle-card {
        transform: none !important;
    }
    
    .bundle-card:hover {
        transform: translateY(-8px) !important;
    }
}

@media (max-width: 768px) {
    .pricing-card {
        margin-bottom: 1.5rem;
    }
    
    .pricing-cards-container {
        gap: 1rem;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .pricing-card {
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }
    
    .pricing-card:hover {
        box-shadow: 0 24px 48px rgba(0,0,0,0.4);
    }
}

/* Accessibility improvements */
.pricing-card:focus-within {
    outline: 2px solid #ffc107;
    outline-offset: 4px;
}

.cta-button:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* Performance optimizations */
.pricing-card {
    contain: layout style paint;
}

.platform-icon {
    contain: layout style paint;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .pricing-card {
        border-width: 3px;
    }
    
    .platform-badge,
    .popular-badge {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .pricing-card,
    .platform-icon,
    .cta-button,
    .features-list div {
        transition: none;
    }
    
    .popular-badge {
        animation: none;
    }
}
