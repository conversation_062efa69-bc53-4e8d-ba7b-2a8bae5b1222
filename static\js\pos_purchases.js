/**
 * POS Purchases Tab JavaScript
 * Handles the purchases tab functionality including table display and order details
 */

let currentPurchasesPage = 1;
let currentPurchasesFilter = 'all';

// Initialize purchases tab when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for purchases tab
    const purchasesTab = document.querySelector('.pos-tab-item[data-tab="purchases-tab"]');
    if (purchasesTab) {
        purchasesTab.addEventListener('click', function() {
            loadPurchasesData();
            initializeInventorySyncButtons();
        });
    }

    // Add event listeners for purchases controls
    const refreshBtn = document.getElementById('refreshPurchasesBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadPurchasesData();
        });
    }

    const statusFilter = document.getElementById('purchaseStatusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPurchasesFilter = this.value;
            currentPurchasesPage = 1;
            loadPurchasesData();
        });
    }
});

// Initialize inventory sync buttons
function initializeInventorySyncButtons() {
    // Add bulk sync button to the main page
    const purchasesHeader = document.querySelector('#purchases-content .d-flex.justify-content-between.align-items-center');
    if (purchasesHeader && !document.getElementById('bulk-inventory-sync-btn')) {
        const bulkSyncBtn = document.createElement('button');
        bulkSyncBtn.id = 'bulk-inventory-sync-btn';
        bulkSyncBtn.className = 'btn btn-warning btn-sm me-2';
        bulkSyncBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Sync All Inventory';
        bulkSyncBtn.onclick = performBulkInventorySync;

        // Insert before the filter container
        const filterContainer = purchasesHeader.querySelector('.d-flex.align-items-center');
        if (filterContainer) {
            purchasesHeader.insertBefore(bulkSyncBtn, filterContainer);
        }
    }
}

/**
 * Load purchases data from the server
 */
function loadPurchasesData() {
    const tableBody = document.getElementById('purchasesTableBody');
    if (!tableBody) return;

    // Show loading state
    tableBody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading purchase orders...</p>
            </td>
        </tr>
    `;

    // Fetch data from server
    const params = new URLSearchParams({
        page: currentPurchasesPage,
        limit: 20,
        status: currentPurchasesFilter
    });

    fetch(`/pos/get_purchase_orders?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPurchasesTable(data.orders, data.pagination);
            } else {
                showPurchasesError(data.error || 'Failed to load purchase orders');
            }
        })
        .catch(error => {
            console.error('Error loading purchases:', error);
            showPurchasesError('Network error while loading purchase orders');
        });
}

/**
 * Display purchases in the table
 */
function displayPurchasesTable(orders, pagination) {
    const tableBody = document.getElementById('purchasesTableBody');
    if (!tableBody) return;

    if (orders.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x mb-3 text-muted"></i>
                    <p class="mb-0">No purchase orders found</p>
                    <small class="text-muted">Orders will appear here after using the Buy tab</small>
                </td>
            </tr>
        `;
        return;
    }

    let tableHTML = '';
    orders.forEach(order => {
        const date = new Date(order.created_at).toLocaleDateString();
        const time = new Date(order.created_at).toLocaleTimeString();
        const statusBadge = getStatusBadge(order.overall_status);
        const paymentBadge = getPaymentBadge(order.payment_method, order.cash_amount, order.credit_amount);

        tableHTML += `
            <tr class="purchase-row" data-order-id="${order.order_id}">
                <td>
                    <code class="text-primary">${order.order_id}</code>
                </td>
                <td>
                    <div>${date}</div>
                    <small class="text-muted">${time}</small>
                </td>
                <td>
                    <div>${order.customer_name}</div>
                    <small class="text-muted">${order.customer_email}</small>
                </td>
                <td>
                    <span class="badge bg-info">${order.products_count} items</span>
                </td>
                <td>
                    <strong>$${order.total_amount.toFixed(2)}</strong>
                </td>
                <td>
                    ${paymentBadge}
                </td>
                <td>
                    ${statusBadge}
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewOrderDetails('${order.order_id}')">
                        <i class="fas fa-eye me-1"></i>View
                    </button>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = tableHTML;

    // Add pagination if needed
    displayPurchasesPagination(pagination);
}

/**
 * Get status badge HTML
 */
function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">Pending</span>',
        'partial': '<span class="badge bg-info">Partial</span>',
        'completed': '<span class="badge bg-success">Completed</span>',
        'failed': '<span class="badge bg-danger">Failed</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

/**
 * Get payment badge HTML
 */
function getPaymentBadge(method, cashAmount, creditAmount) {
    if (method === 'combined') {
        let badges = '';
        if (cashAmount > 0) {
            badges += `<span class="badge bg-primary me-1">Cash: $${cashAmount.toFixed(2)}</span>`;
        }
        if (creditAmount > 0) {
            badges += `<span class="badge bg-success">Credit: $${creditAmount.toFixed(2)}</span>`;
        }
        return badges;
    } else if (method === 'cash') {
        return `<span class="badge bg-primary">Cash: $${cashAmount.toFixed(2)}</span>`;
    } else if (method === 'credit') {
        return `<span class="badge bg-success">Credit: $${creditAmount.toFixed(2)}</span>`;
    }
    return `<span class="badge bg-secondary">${method}</span>`;
}

/**
 * Display pagination controls
 */
function displayPurchasesPagination(pagination) {
    // TODO: Add pagination controls if needed
    // For now, we'll implement basic pagination
    console.log('Pagination:', pagination);
}

/**
 * Show error message in the table
 */
function showPurchasesError(message) {
    const tableBody = document.getElementById('purchasesTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-3 text-danger"></i>
                <p class="mb-0 text-danger">${message}</p>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadPurchasesData()">
                    <i class="fas fa-retry me-1"></i>Retry
                </button>
            </td>
        </tr>
    `;
}

/**
 * View order details in a modal
 */
function viewOrderDetails(orderId) {
    // Show loading modal first
    showOrderDetailsModal(orderId, null, true);

    // Fetch order details
    fetch(`/pos/get_purchase_order/${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOrderDetailsModal(orderId, data.order, false);
            } else {
                alert('Error loading order details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('Network error while loading order details');
        });
}

/**
 * Show order details modal
 */
function showOrderDetailsModal(orderId, orderData, isLoading) {
    // Remove existing modal if any
    const existingModal = document.getElementById('orderDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal HTML
    const modalHTML = createOrderDetailsModalHTML(orderId, orderData, isLoading);
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal
    const modal = document.getElementById('orderDetailsModal');
    modal.style.display = 'block';

    // Add close event listeners
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.btn-secondary');
    
    [closeBtn, cancelBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', function() {
                modal.style.display = 'none';
                setTimeout(() => modal.remove(), 300);
            });
        }
    });

    // Close on outside click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
            setTimeout(() => modal.remove(), 300);
        }
    });
}

/**
 * Create order details modal HTML
 */
function createOrderDetailsModalHTML(orderId, orderData, isLoading) {
    if (isLoading) {
        return `
            <div id="orderDetailsModal" class="modal" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0, 0, 0, 0.8);">
                <div class="modal-content" style="background-color: #1e293b; margin: 5% auto; padding: 30px; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; width: 80%; max-width: 900px; color: #f8fafc; position: relative;">
                    <span class="close" style="position: absolute; right: 20px; top: 15px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading order details...</p>
                    </div>
                </div>
            </div>
        `;
    }

    // Generate detailed order HTML
    return createDetailedOrderHTML(orderId, orderData);
}

/**
 * Create detailed order HTML
 */
function createDetailedOrderHTML(orderId, orderData) {
    const date = new Date(orderData.created_at).toLocaleString();

    // Generate products table
    let productsHTML = '';
    orderData.products.forEach(product => {
        const paymentBadge = product.payment_type === 'cash' ?
            '<span class="badge bg-primary">Cash</span>' :
            '<span class="badge bg-success">Credit</span>';

        const customBadge = product.is_custom ?
            '<span class="badge bg-warning ms-1">Custom</span>' : '';

        const inventoryStatus = product.inventory_updated ?
            '<span class="badge bg-success">Updated</span>' :
            '<span class="badge bg-warning">Pending</span>';

        productsHTML += `
            <tr>
                <td>
                    ${product.title}
                    ${product.variant_title ? `<br><small class="text-muted">${product.variant_title}</small>` : ''}
                    ${customBadge}
                </td>
                <td class="text-center">${product.quantity}</td>
                <td class="text-end">$${product.buy_price.toFixed(2)}</td>
                <td class="text-end">$${product.retail_price.toFixed(2)}</td>
                <td class="text-center">${paymentBadge}</td>
                <td class="text-center">${inventoryStatus}</td>
            </tr>
        `;
    });

    // Generate processing status
    const storeCreditStatus = getProcessingStatus(
        orderData.store_credit_processed,
        orderData.store_credit_success,
        orderData.store_credit_error,
        'store_credit',
        orderData.products,
        orderId,
        orderData.credit_amount,
        orderData.store_credit_refunded
    );

    const inventoryStatus = getProcessingStatus(
        orderData.inventory_processed,
        orderData.inventory_success,
        orderData.inventory_error,
        'inventory',
        orderData.products,
        orderId,
        0,
        false
    );

    return `
        <div id="orderDetailsModal" class="modal" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0, 0, 0, 0.8);">
            <div class="modal-content" style="background-color: #1e293b; margin: 2% auto; padding: 0; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; width: 90%; max-width: 1000px; color: #f8fafc; position: relative; max-height: 90vh; overflow-y: auto;">
                <div class="modal-header" style="padding: 20px 30px; border-bottom: 1px solid rgba(255, 255, 255, 0.1); position: sticky; top: 0; background-color: #1e293b; z-index: 10;">
                    <h4 class="modal-title mb-0">
                        <i class="fas fa-receipt me-2"></i>Purchase Order Details
                    </h4>
                    <span class="close" style="position: absolute; right: 20px; top: 15px; color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                </div>

                <div class="modal-body" style="padding: 30px;">
                    <!-- Order Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h6 class="card-title">Order Information</h6>
                                    <p><strong>Order ID:</strong> <code class="text-primary">${orderData.order_id}</code></p>
                                    <p><strong>Date:</strong> ${date}</p>
                                    <p><strong>Customer:</strong> ${orderData.customer_name}</p>
                                    <p><strong>Email:</strong> ${orderData.customer_email}</p>
                                    <p class="mb-0"><strong>Notes:</strong> ${orderData.notes || 'None'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h6 class="card-title">Payment Summary</h6>
                                    <p><strong>Payment Method:</strong> ${orderData.payment_method}</p>
                                    <p><strong>Cash Amount:</strong> $${orderData.cash_amount.toFixed(2)}</p>
                                    <p><strong>Credit Amount:</strong> $${orderData.credit_amount.toFixed(2)}</p>
                                    <p class="mb-0"><strong>Total Amount:</strong> <span class="text-success">$${orderData.total_amount.toFixed(2)}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Processing Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h6 class="card-title">Store Credit Processing</h6>
                                    ${storeCreditStatus}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h6 class="card-title">Inventory Processing</h6>
                                    ${inventoryStatus}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="card" style="background-color: #2d3748; border: 1px solid rgba(255, 255, 255, 0.1);">
                        <div class="card-body">
                            <h6 class="card-title">Products (${orderData.products.length} items)</h6>
                            <div class="table-responsive">
                                <table class="table table-dark table-sm">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th class="text-center">Qty</th>
                                            <th class="text-end">Buy Price</th>
                                            <th class="text-end">Retail Price</th>
                                            <th class="text-center">Payment</th>
                                            <th class="text-center">Inventory</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${productsHTML}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer" style="padding: 20px 30px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    ${getInventorySyncButton(orderData, orderId)}
                    <button type="button" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Get inventory sync button HTML based on current status
 */
function getInventorySyncButton(orderData, orderId) {
    // Check if there are any non-custom products
    const nonCustomProducts = orderData.products.filter(p => !p.is_custom);
    if (nonCustomProducts.length === 0) {
        return ''; // No sync button needed for custom-only orders
    }

    // Check inventory status
    if (orderData.inventory_processed && orderData.inventory_success) {
        // All products successfully synced
        const allSynced = nonCustomProducts.every(p => p.inventory_updated);
        if (allSynced) {
            return `<button type="button" class="btn btn-success me-2" disabled>
                <i class="fas fa-check me-1"></i>Inventory Synced
            </button>`;
        } else {
            // Some products failed, allow re-sync
            return `<button type="button" class="btn btn-warning me-2" onclick="performOrderInventorySync('${orderId}')">
                <i class="fas fa-redo me-1"></i>Re-sync Failed Items
            </button>`;
        }
    } else if (orderData.inventory_processed && !orderData.inventory_success) {
        // Failed, allow retry
        return `<button type="button" class="btn btn-danger me-2" onclick="performOrderInventorySync('${orderId}')">
            <i class="fas fa-exclamation-triangle me-1"></i>Retry Inventory Sync
        </button>`;
    } else {
        // Not processed yet, allow initial sync
        return `<button type="button" class="btn btn-warning me-2" onclick="performOrderInventorySync('${orderId}')">
            <i class="fas fa-sync-alt me-1"></i>Sync Inventory
        </button>`;
    }
}

/**
 * Get processing status HTML
 */
function getProcessingStatus(processed, success, error, type, products, orderId, creditAmount, refunded) {
    if (!processed) {
        return '<span class="badge bg-warning">Pending</span><p class="mt-2 mb-0 small">Processing has not started yet</p>';
    } else if (success) {
        let statusText = 'Processing completed successfully';
        let actionButton = '';

        // Add more details for inventory processing
        if (type === 'inventory' && products) {
            const updatedCount = products.filter(p => p.inventory_updated).length;
            const totalCount = products.filter(p => !p.is_custom).length; // Only count non-custom items
            statusText = `Successfully updated ${updatedCount}/${totalCount} products`;
        }

        // Add undo button for store credit (only if not already refunded)
        if (type === 'store_credit' && creditAmount > 0 && !refunded) {
            actionButton = `<button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="undoStoreCredit('${orderId}')">
                <i class="fas fa-undo me-1"></i>Undo Store Credit
            </button>`;
        }

        return `<span class="badge bg-success">Completed</span><p class="mt-2 mb-0 small">${statusText}</p>${actionButton}`;
    } else {
        return `<span class="badge bg-danger">Failed</span><p class="mt-2 mb-0 small text-danger">${error || 'Processing failed'}</p>`;
    }
}

// Perform bulk inventory sync for all pending orders
async function performBulkInventorySync() {
    const button = document.getElementById('bulk-inventory-sync-btn');
    if (!button) return;

    // Disable button and show loading state
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Syncing...';

    try {
        const response = await fetch('/pos/manual_inventory_sync_all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', `Bulk inventory sync completed! Processed ${data.processed_orders} orders, updated ${data.total_products_updated} products.`);

            // Refresh the purchases table to show updated statuses
            loadPurchasesData();
        } else {
            showAlert('error', `Bulk inventory sync failed: ${data.error}`);
        }
    } catch (error) {
        console.error('Error performing bulk inventory sync:', error);
        showAlert('error', 'Failed to perform bulk inventory sync. Please try again.');
    } finally {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// Perform inventory sync for a specific order
async function performOrderInventorySync(orderId) {
    const button = document.querySelector(`[onclick="performOrderInventorySync('${orderId}')"]`);
    if (!button) return;

    // Check if this is a re-sync attempt (button text indicates previous sync)
    const isRetry = button.innerHTML.includes('Re-sync') || button.innerHTML.includes('Retry');

    // Add confirmation for re-sync attempts
    if (isRetry) {
        if (!confirm('This will re-attempt inventory sync for failed products only. Products that were already successfully synced will not be affected. Continue?')) {
            return;
        }
    }

    // Disable button and show loading state
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Syncing...';

    try {
        const response = await fetch(`/pos/manual_inventory_sync/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            if (data.updated_count > 0) {
                showAlert('success', `Inventory sync completed! Updated ${data.updated_count}/${data.total_products} products.`);
            } else {
                showAlert('info', 'No products required inventory updates - all products are already synced.');
            }

            // Refresh the order details modal to show updated status
            setTimeout(() => {
                viewOrderDetails(orderId);
            }, 500); // Small delay to ensure database is updated

            // Refresh the purchases table
            loadPurchasesData();
        } else {
            showAlert('error', `Inventory sync failed: ${data.error}`);
        }
    } catch (error) {
        console.error('Error performing inventory sync:', error);
        showAlert('error', 'Failed to perform inventory sync. Please try again.');
    } finally {
        // Re-enable button (will be replaced when modal refreshes)
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// Undo store credit for a specific order
async function undoStoreCredit(orderId) {
    // Confirm action with user
    if (!confirm('Are you sure you want to undo the store credit for this order? This will deduct the credit amount from the customer\'s account.')) {
        return;
    }

    const button = document.querySelector(`[onclick="undoStoreCredit('${orderId}')"]`);
    if (!button) return;

    // Disable button and show loading state
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Undoing...';

    try {
        const response = await fetch(`/pos/undo_store_credit/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', `Store credit undone! ${data.message}`);

            // Refresh the order details modal to show updated status
            setTimeout(() => {
                viewOrderDetails(orderId);
            }, 500); // Small delay to ensure database is updated

            // Refresh the purchases table
            loadPurchasesData();
        } else {
            showAlert('error', `Failed to undo store credit: ${data.error}`);
        }
    } catch (error) {
        console.error('Error undoing store credit:', error);
        showAlert('error', 'Failed to undo store credit. Please try again.');
    } finally {
        // Re-enable button (will be replaced when modal refreshes)
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// Show alert function (assuming it exists in the main POS system)
function showAlert(type, message) {
    // Try to use existing alert system
    if (typeof window.showAlert === 'function') {
        window.showAlert(type, message);
    } else if (typeof window.showCustomAlert === 'function') {
        window.showCustomAlert(message, type);
    } else {
        // Fallback to browser alert
        alert(message);
    }
}
