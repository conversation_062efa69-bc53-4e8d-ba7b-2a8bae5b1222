from concurrent.futures import ThreadPoolExecutor
from pymongo import MongoClient, UpdateOne
from pymongo.errors import PyMongoError
from requests.adapters import HTTPAdapter 
from requests.packages.urllib3.util.retry import Retry
from datetime import datetime, timedelta, timezone
from tqdm import tqdm
from itertools import islice
import requests
import sys
import time
import schedule
from threading import Lock
import signal
import re
import logging
from functools import wraps
from models.database import mongo

logger = logging.getLogger(__name__)

# Pre-compile regex patterns
CONDITION_PATTERNS = {
   re.compile(r'near\s*mint|nm\b', re.I): 'nm',
   re.compile(r'lightly\s*played|lp\b', re.I): 'lp',
   re.compile(r'moderately\s*played|mp\b', re.I): 'mp',
   re.compile(r'heavily\s*played|hp\b', re.I): 'hp',
   re.compile(r'damaged|dm\b', re.I): 'dm'
}

SEALED_PATTERN = re.compile(r'.*seal.*', re.I)

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
BATCH_SIZE = 500
DEFAULT_MIN_PRICE_USD = 0.2
TEST_USERNAME = "Khaoz"
MONGO_POOL_SIZE = 200

# Enhanced session configuration
session = requests.Session()
retries = Retry(
   total=5,
   backoff_factor=0.5,
   status_forcelist=[500, 502, 503, 504],
   allowed_methods=frozenset(['GET'])
)
adapter = HTTPAdapter(
   max_retries=retries,
   pool_connections=MONGO_POOL_SIZE,
   pool_maxsize=MONGO_POOL_SIZE,
   pool_block=False
)
session.mount('https://', adapter)
session.mount('http://', adapter)

# MongoDB collections
shopify_collection = None
user_collection = None
tcgplayer_key_collection = None
autopricer_collection = None
pricing_transactions_collection = None
verification_queue_collection = None
prices_collection = None

def init_collections(db=None):
    """Initialize MongoDB collections using direct connection or provided db"""
    global shopify_collection, user_collection, tcgplayer_key_collection, autopricer_collection, pricing_transactions_collection, verification_queue_collection, prices_collection
    
    # If collections are already initialized, return early
    if shopify_collection is not None and user_collection is not None:
        return
        
    # Try to use the provided db connection first
    if db is not None:
        logger.info("Initializing collections with provided db connection")
        shopify_collection = db['shProducts']
        user_collection = db['user']
        tcgplayer_key_collection = db['tcgplayerKey']
        autopricer_collection = db['autopricerShopify']
        pricing_transactions_collection = db['pricingTransactions']
        verification_queue_collection = db['verificationQueue']
        prices_collection = db['prices']
        return
    
    # If no db provided, create a direct connection
    try:
        from pymongo import MongoClient
        logger.info("Creating direct MongoDB connection")
        mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
        mongo_dbname = 'test'
        client = MongoClient(mongo_uri)
        db = client[mongo_dbname]
        
        shopify_collection = db['shProducts']
        user_collection = db['user']
        tcgplayer_key_collection = db['tcgplayerKey']
        autopricer_collection = db['autopricerShopify']
        pricing_transactions_collection = db['pricingTransactions']
        verification_queue_collection = db['verificationQueue']
        prices_collection = db['prices']
        logger.info("Successfully initialized collections with direct connection")
    except Exception as e:
        logger.error(f"Failed to initialize collections with direct connection: {str(e)}")
        raise RuntimeError(f"Could not initialize database collections: {str(e)}")

# Retry decorator for database operations
def retry_database_operation(max_attempts=3, initial_backoff=1):
    """
    Decorator to retry database operations with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_backoff: Initial backoff time in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            backoff = initial_backoff
            last_error = None
            
            while attempt < max_attempts:
                try:
                    result = func(*args, **kwargs)
                    # Verify the result
                    if hasattr(result, 'acknowledged') and not result.acknowledged:
                        raise PyMongoError("Operation not acknowledged by MongoDB")
                    return result
                except Exception as e:
                    last_error = e
                    attempt += 1
                    if attempt >= max_attempts:
                        logger.error(f"Failed after {max_attempts} attempts: {str(e)}")
                        break
                    
                    logger.warning(f"Attempt {attempt} failed: {str(e)}. Retrying in {backoff} seconds...")
                    time.sleep(backoff)
                    backoff *= 2  # Exponential backoff
            
            raise last_error
        return wrapper
    return decorator

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_bulk_write(collection, operations, ordered=False):
    """
    Perform a bulk write operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        operations: List of write operations
        ordered: Whether operations should be executed in order
        
    Returns:
        BulkWriteResult object
    """
    result = collection.bulk_write(operations, ordered=ordered)
    
    # Verify the result
    if result.acknowledged:
        logger.info(f"Bulk write successful: {result.bulk_api_result}")
        return result
    else:
        raise PyMongoError("Bulk write not acknowledged by MongoDB")

@retry_database_operation(max_attempts=3, initial_backoff=1)
def verified_update_one(collection, filter_dict, update_dict):
    """
    Perform an update_one operation with verification and retry logic.
    
    Args:
        collection: MongoDB collection
        filter_dict: Filter criteria
        update_dict: Update operations
        
    Returns:
        UpdateResult object
    """
    result = collection.update_one(filter_dict, update_dict)
    
    # Verify the result
    if result.acknowledged:
        logger.info(f"Update successful: matched={result.matched_count}, modified={result.modified_count}")
        return result
    else:
        raise PyMongoError("Update not acknowledged by MongoDB")

def log_pricing_transaction(username, product_id, variant_id, old_price, new_price, status, error=None):
    """
    Log a pricing transaction to track the status of price changes.
    
    Args:
        username: The username
        product_id: The product ID
        variant_id: The variant ID
        old_price: The old price
        new_price: The new price
        status: The status of the transaction (success, error)
        error: Error message if status is error
    """
    try:
        pricing_transactions_collection.insert_one({
            "timestamp": datetime.now(timezone.utc),
            "username": username,
            "product_id": product_id,
            "variant_id": variant_id,
            "old_price": old_price,
            "new_price": new_price,
            "status": status,
            "error": error,
            "verified": False
        })
    except Exception as e:
        logger.error(f"Failed to log pricing transaction: {str(e)}")

def schedule_verification(product_id, expected_prices):
    """
    Schedule a verification task for a product.
    
    Args:
        product_id: The product ID
        expected_prices: Dictionary mapping variant IDs to expected prices
    """
    try:
        verification_queue_collection.insert_one({
            "product_id": product_id,
            "expected_prices": expected_prices,
            "created_at": datetime.now(timezone.utc),
            "status": "pending",
            "attempts": 0,
            "max_attempts": 3
        })
    except Exception as e:
        logger.error(f"Failed to schedule verification: {str(e)}")

# Currency cache implementation
class CurrencyCache:
   def __init__(self, ttl_hours=24):
       self.cache = {}
       self.ttl = timedelta(hours=ttl_hours)
       self.lock = Lock()
       self.last_cleanup = datetime.now(timezone.utc)

   def get(self, currency):
       with self.lock:
           if currency in self.cache:
               rate_data = self.cache[currency]
               if datetime.now(timezone.utc) - rate_data['timestamp'] < self.ttl:
                   return rate_data['rate']
               else:
                   del self.cache[currency]
           return None

   def set(self, currency, rate):
       with self.lock:
           self.cache[currency] = {
               'rate': rate,
               'timestamp': datetime.now(timezone.utc)
           }

   def clear_expired(self):
       with self.lock:
           current_time = datetime.now(timezone.utc)
           expired = [currency for currency, data in self.cache.items() 
                     if current_time - data['timestamp'] >= self.ttl]
           for currency in expired:
               del self.cache[currency]
           self.last_cleanup = current_time

# TCGPlayer price cache implementation
class TCGPriceCache:
    def __init__(self, ttl_minutes=60):  # Increased to 60 minutes
        self.cache = {}
        self.ttl = timedelta(minutes=ttl_minutes)
        self.lock = Lock()
        self.hits = 0
        self.misses = 0
        self.last_cleanup = datetime.now(timezone.utc)
        self.max_cache_size = 10000  # Maximum number of items to store
    
    def get_stats(self):
        total = self.hits + self.misses
        hit_rate = (self.hits / total * 100) if total > 0 else 0
        return {
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.now(timezone.utc)
            expired = [pid for pid, data in self.cache.items()
                      if current_time - data['timestamp'] >= self.ttl]
            for pid in expired:
                del self.cache[pid]
            self.last_cleanup = current_time

    def get_batch(self, product_ids):
        now = datetime.now(timezone.utc)
        cached = {}
        missing = []
        
        with self.lock:
            for pid in product_ids:
                if pid in self.cache:
                    data = self.cache[pid]
                    if now - data['timestamp'] < self.ttl:
                        cached[pid] = data['pricing']
                        self.hits += 1
                    else:
                        missing.append(pid)
                        del self.cache[pid]
                        self.misses += 1
                else:
                    missing.append(pid)
                    self.misses += 1
                    
            # Cleanup if cache gets too large
            if len(self.cache) > self.max_cache_size:
                self.clear_expired()
                
        return cached, missing

    def update_batch(self, pricing_data):
        with self.lock:
            now = datetime.now(timezone.utc)
            
            # Clear expired entries if we haven't done so recently
            if now - self.last_cleanup > timedelta(minutes=30):
                self.clear_expired()
            
            for pid, data in pricing_data.items():
                self.cache[pid] = {
                    'pricing': data,
                    'timestamp': now
                }

# Initialize caches
currency_cache = CurrencyCache()
tcgplayer_cache = TCGPriceCache()

def get_exchange_rate(target_currency):
   if target_currency == 'USD':
       return 1.0
   
   cached_rate = currency_cache.get(target_currency)
   if cached_rate is not None:
       return cached_rate
   
   try:
       url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
       response = session.get(url, timeout=10)
       response.raise_for_status()
       rates = response.json().get('conversion_rates', {})
       rate = rates.get(target_currency)
       
       if rate is None:
           return 1.0
           
       currency_cache.set(target_currency, rate)
       return rate
   except Exception:
       return 1.0

def get_pricing_rules(user_profile, product):
   advanced_key = f"{product.get('vendor')}_{product.get('product_type')}_{product.get('expansionName')}"
   
   if advanced_key in user_profile.get('advancedPricingRules', {}):
       return user_profile['advancedPricingRules'][advanced_key]
   return user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})

def has_valid_price(price_data):
    """Check if a price data object has any valid prices."""
    return (price_data.get('marketPrice') is not None or 
            price_data.get('lowPrice') is not None or 
            price_data.get('directLowPrice') is not None)

def determine_printing_type(variant_title, valid_subtypes):
    """
    Determine the printing type from variant title and valid TCGPlayer subtypes.
    Prioritizes matching against subtypes that have valid prices.
    """
    if not variant_title:
        return 'Normal'

    # Clean up variant title
    variant_title = variant_title.lower()
    variant_title = re.sub(r'\s+', ' ', variant_title)
    
    # Remove condition prefixes
    for cond in ['near mint', 'nm', 'lightly played', 'lp', 'moderately played', 'mp', 
                'heavily played', 'hp', 'damaged', 'dmg']:
        variant_title = variant_title.replace(cond, '').strip()
    variant_title = variant_title.strip('- ').strip()
    
    # First try exact match against valid subtypes
    for subtype in valid_subtypes:
        if subtype and variant_title == subtype.lower():
            return subtype
    
    # Then try partial matches
    if 'cold foil' in variant_title:
        return 'Cold Foil'
    if 'rainbow foil' in variant_title:
        return 'Rainbow Foil'
    if 'reverse holofoil' in variant_title or 'reverse holo' in variant_title:
        return 'Reverse Holofoil'
    if 'etched foil' in variant_title:
        return 'Etched Foil'
    if 'etched' in variant_title:
        return 'Etched'
    if 'gilded' in variant_title:
        return 'Gilded'
    if any(s in variant_title for s in ['holofoil', 'holo foil']):
        return 'Holofoil'
    if '1st edition' in variant_title:
        return '1st Edition'
    if 'unlimited' in variant_title:
        return 'Unlimited'
    if 'foil' in variant_title:
        for subtype in valid_subtypes:
            if subtype and 'foil' in subtype.lower():
                return subtype
        return 'Foil'
    
    # If no specific match found, return Normal
    return 'Normal'

def extract_condition(variant_title):
   title = variant_title.lower()
   for pattern, condition in CONDITION_PATTERNS.items():
       if pattern.search(title):
           return condition
   return 'nm'

def check_prices_collection(product_ids):
    """
    Check if product IDs exist in the prices collection and are recent enough
    Returns a tuple of (cached_data, missing_ids)
    """
    cached_data = {}
    missing_ids = []
    
    # Define the maximum age of cached data (24 hours)
    max_age = timedelta(hours=24)
    current_time = datetime.now(timezone.utc)
    
    try:
        # Query the prices collection for all product IDs at once
        cursor = prices_collection.find(
            {
                "productId": {"$in": [int(pid) for pid in product_ids if pid.isdigit()]},
                "last_updated": {"$gte": current_time - max_age}
            }
        )
        
        # Process the results
        for doc in cursor:
            product_id = str(doc.get('productId'))
            if product_id and 'prices' in doc:
                cached_data[product_id] = []
                
                # Convert the prices structure to match the expected format
                for subtype_name, price_data in doc.get('prices', {}).items():
                    if price_data:
                        cached_data[product_id].append({
                            'productId': price_data.get('productId'),
                            'subTypeName': subtype_name,
                            'directLowPrice': price_data.get('directLowPrice'),
                            'lowPrice': price_data.get('lowPrice'),
                            'midPrice': price_data.get('midPrice'),
                            'highPrice': price_data.get('highPrice'),
                            'marketPrice': price_data.get('marketPrice')
                        })
        
        # Determine which product IDs are missing from the cache
        missing_ids = [pid for pid in product_ids if pid not in cached_data]
                
        logger.info(f"Found {len(cached_data)} products in cache, {len(missing_ids)} products need API calls")
        return cached_data, missing_ids
        
    except Exception as e:
        logger.error(f"Error checking prices collection: {str(e)}")
        # If there's an error, return empty cached data and all product IDs as missing
        return {}, product_ids

def fetch_pricing_data(product_ids, tcgplayer_api_key):
   if not product_ids:
       return {}

   # First check the in-memory cache
   cached_data, missing_ids = tcgplayer_cache.get_batch(product_ids)
   
   if not missing_ids:
       return cached_data
   
   # Then check the MongoDB prices collection
   db_cached_data, db_missing_ids = check_prices_collection(missing_ids)
   
   # Merge in-memory cache with MongoDB cache
   merged_cached_data = {**cached_data, **db_cached_data}
   
   # If all data was found in caches, return it
   if not db_missing_ids:
       return merged_cached_data
   
   # Otherwise, make API calls for the remaining missing IDs
   headers = {
       'Authorization': f'Bearer {tcgplayer_api_key}',
       'Accept': 'application/json',
   }
   
   logger.info(f"Making API calls for {len(db_missing_ids)} products")
   
   new_data = {}
   chunks = [missing_ids[i:i + 100] for i in range(0, len(missing_ids), 100)]
   
   for chunk in chunks:
       try:
           url = f"https://api.tcgplayer.com/pricing/product/{','.join(chunk)}"
           response = session.get(url, headers=headers, timeout=10)
           response.raise_for_status()
           
           raw_response = response.text
           logger.info(f"Raw TCGPlayer response: {raw_response}")
           
           response_data = response.json()
           logger.info(f"TCGPlayer response data: {response_data}")
           logger.info(f"TCGPlayer results: {response_data.get('results', [])}")
           
           # Log the structure of the response
           if 'results' in response_data:
               for result in response_data['results']:
                   logger.info(f"Processing result: {result}")
                   logger.info(f"  Product ID: {result.get('productId')}")
                   logger.info(f"  Subtype: {result.get('subTypeName')}")
                   logger.info(f"  Market Price: {result.get('marketPrice')}")
                   logger.info(f"  Low Price: {result.get('lowPrice')}")
                   logger.info(f"  Direct Low Price: {result.get('directLowPrice')}")
                   logger.info(f"  Mid Price: {result.get('midPrice')}")
                   logger.info(f"  High Price: {result.get('highPrice')}")
           
           for item in response_data.get('results', []):
               product_id = str(item.get('productId'))
               if product_id:
                   if product_id not in new_data:
                       new_data[product_id] = []
                   new_data[product_id].append(item)
                   logger.info(f"Added price data for product {product_id}: {item}")
                   
                   # Store this data in MongoDB for future use
                   try:
                       numeric_product_id = int(product_id) if product_id.isdigit() else None
                       subtype_name = item.get('subTypeName')
                       
                       if numeric_product_id and subtype_name:
                           # Update using the nested prices object format
                           prices_collection.update_one(
                               {'productId': numeric_product_id},
                               {
                                   '$set': {
                                       'productId': numeric_product_id,
                                       'last_updated': datetime.now(timezone.utc),
                                       'timestamp': datetime.now(timezone.utc),
                                       'lastUpdateStatus': 'success',
                                       f'prices.{subtype_name}': {
                                           'productId': numeric_product_id,
                                           'directLowPrice': item.get('directLowPrice'),
                                           'lowPrice': item.get('lowPrice'),
                                           'midPrice': item.get('midPrice'),
                                           'highPrice': item.get('highPrice'),
                                           'marketPrice': item.get('marketPrice')
                                       }
                                   }
                               },
                               upsert=True
                           )
                           logger.info(f"Stored price data in MongoDB for product {product_id}, subtype {subtype_name}")
                   except Exception as e:
                       logger.error(f"Error storing TCGPlayer API data in MongoDB: {str(e)}")
                   
       except Exception as e:
           logger.error(f"Error fetching TCGPlayer prices for chunk {chunk}: {str(e)}")
           logger.error(f"URL: {url}")
           continue
   
   tcgplayer_cache.update_batch(new_data)
   return {**cached_data, **new_data}

def calculate_variant_price(variant, pricing_data, product_pricing, pricing_rules, min_price_local, exchange_rate, is_test_user, username, product=None, user_profile=None, user_currency='USD'):
    from pricing_utils import PricingCalculator
    import logging
    import traceback
    logger = logging.getLogger(__name__)
    
    logger.info(f"\n=== Starting price calculation for variant {variant.get('title')} ===")
    logger.info(f"Initial parameters:")
    logger.info(f"  Min Price Local: {min_price_local}")
    logger.info(f"  Exchange Rate: {exchange_rate}")
    logger.info(f"  User Currency: {user_currency}")
    logger.info(f"  Pricing Data: {pricing_data}")
    
    if not pricing_data:
        logger.error("No pricing data provided")
        return None, True

    try:
        # Convert TCGPlayer prices to local currency upfront
        local_pricing_data = {}
        logger.info("\nConverting prices to local currency:")
        for key, value in pricing_data.items():
            if value is not None:
                # Convert USD price to local currency
                local_price = round(float(value) * exchange_rate, 2)
                local_pricing_data[key] = local_price
                logger.info(f"  {key}: ${value} USD -> {local_price} {user_currency}")

        from routes.warehouse.utils import prepare_shopify_settings
        
        # Get complete settings
        settings = prepare_shopify_settings(username)
        
        logger.info("\nPricing Settings:")
        logger.info(f"  Min Price: {settings.get('minPrice', 0)}")
        logger.info(f"  Custom Stepping: {settings.get('customStepping', {})}")
        logger.info(f"  Price Rounding Enabled: {settings.get('price_rounding_enabled', False)}")
        logger.info(f"  Price Rounding Thresholds: {settings.get('price_rounding_thresholds', [])}")
        logger.info(f"  Use Highest Price: {settings.get('use_highest_price', False)}")
        logger.info(f"  Price Comparison Pairs: {settings.get('price_comparison_pairs', [])}")
        logger.info(f"  Price Modifiers: {settings.get('price_modifiers', {})}")
        logger.info(f"  Price Preference Order: {settings.get('price_preference_order', [])}")
        logger.info(f"  Game Minimum Prices: {settings.get('game_minimum_prices', {})}")
        logger.info(f"  TCG Trend Increasing: {settings.get('tcg_trend_increasing', 0)}")
        logger.info(f"  TCG Trend Decreasing: {settings.get('tcg_trend_decreasing', 0)}")
        
        calculator = PricingCalculator(settings, user_currency)

        # Extract condition and printing type
        condition = extract_condition(variant.get('option1', ''))
        # Get valid subtypes from the product pricing data
        valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                         if p.get('subTypeName') and 
                         (p.get('marketPrice') is not None or 
                          p.get('lowPrice') is not None or 
                          p.get('directLowPrice') is not None)]
        printing_type = determine_printing_type(variant['title'], valid_subtypes)
        
        logger.info("\nVariant Details:")
        logger.info(f"  Condition: {condition}")
        logger.info(f"  Printing Type: {printing_type}")

        # Prepare complete sku_info with all necessary data
        sku_info = {
            'pricingInfo': local_pricing_data,
            'condName': condition,
            'printingName': printing_type,
            'skuId': variant.get('id'),
            'variantTitle': variant.get('title')
        }

        # Ensure product has all required fields for game rules
        if product:
            product.update({
                'gameName': product.get('gameName') or product.get('game_name'),
                'product_type': product.get('product_type'),
                'rarity': product.get('rarity'),
                'vendor': product.get('vendor'),
                'expansionName': product.get('expansionName')
            })

        logger.info("\nCalculating final price with all rules")
        final_price, is_missing, price_history = calculator.calculate_final_price(sku_info, product)
        
        # Log detailed price calculation steps
        logger.info("\nPrice Calculation Steps:")
        if price_history:
            for entry in price_history:
                logger.info(f"  {entry['step']}: ${entry['old_price']:.2f} -> ${entry['new_price']:.2f} {entry.get('details', '')}")
        else:
            logger.info("  No price history available")
        
        if is_missing:
            logger.error("Missing required pricing data")
            return None, True
        
        logger.info(f"\nFinal price after all rules applied: ${final_price}")
        return final_price, False

    except Exception as e:
        logger.error(f"Error calculating price: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, True

def process_user(config, tcgplayer_api_key):
    init_collections()  # Initialize collections using default connection
    start_time = datetime.now(timezone.utc)
    try:
        username = config['username']
        is_test_user = username.lower() == TEST_USERNAME.lower()
       
        selected_product_types = [pt.lower() for pt in config['selectedProductTypes']]
        
        # If no product types are selected, return early as there's nothing to process
        if not selected_product_types:
            logger.info(f"No product types selected for user {username}, skipping processing")
            return
        
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            return
            
        from models.shopify_settings_model import ShopifySettings
        shopify_settings = ShopifySettings.objects(username=username).first()
        if not shopify_settings:
            shopify_settings = ShopifySettings(username=username)
            shopify_settings.save()
        
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)

        # Calculate the timestamp for 6 hours ago
        six_hours_ago = datetime.now(timezone.utc) - timedelta(hours=6)
        
        query = {
            'username': username,
            'product_type': {
                '$regex': f'^({"|".join(map(re.escape, selected_product_types))})$',
                '$options': 'i',
                '$not': {'$regex': r'.*seal.*', '$options': 'i'}
            },
            'rarity': {'$not': {'$regex': r'.*seal.*', '$options': 'i'}},
            '$or': [
                {'last_repriced': {'$exists': False}},  # Never repriced
                {'last_repriced': {'$lt': six_hours_ago}}  # Repriced more than 6 hours ago
            ],
            'variants': {
                '$elemMatch': {
                    'inventory_quantity': {'$gt': 0}  # At least one variant has quantity > 0
                }
            }
        }

        total_products = shopify_collection.count_documents(query)
        if not total_products:
            return

        products = list(shopify_collection.find(query))
        bulk_updates = []
        total_updates = 0
        total_price_changes = 0
        
        with tqdm(total=len(products), desc=f"Processing {username}", leave=True) as pbar:
            filtered_products = [p for p in products if p.get('product_type', '').lower() in selected_product_types]
            print(f"\nProcessing {len(filtered_products)} products for {username}")
            product_ids = [str(p.get('productId')) for p in filtered_products if p.get('productId')]
            pricing_data = fetch_pricing_data(product_ids, tcgplayer_api_key)

            for product in filtered_products:
                pbar.update(1)
                try:
                    # Skip products with manual override
                    if product.get('manualOverride', False):
                        continue

                    product_id = str(product.get('productId'))
                    if not product_id or product_id not in pricing_data:
                        continue

                    product_pricing = pricing_data.get(product_id, [])
                    if not isinstance(product_pricing, list):
                        product_pricing = [product_pricing]
                    # Only include subtypes that have valid prices
                    valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                                    if p.get('subTypeName') and 
                                    (p.get('marketPrice') is not None or 
                                     p.get('lowPrice') is not None or 
                                     p.get('directLowPrice') is not None)]
                    logger.info(f"Valid subtypes: {valid_subtypes}")
                    pricing_rules = get_pricing_rules(user_profile, product)
                    
                    variants_changed = False
                    variants = product['variants']
                    
                    for variant in variants:
                        try:
                            old_price = float(variant.get('price', 0))
                            printing_type = determine_printing_type(variant['title'], valid_subtypes)
                            original_printing_type = printing_type  # Store original for logging
                            
                            # --- Start: Pokemon 1st Edition/Unlimited Edge Case ---
                            try:
                                game_name = product.get('gameName', '').lower()
                                
                                if game_name == 'pokemon' and valid_subtypes:
                                    # Normalize subtypes for the check
                                    normalized_subtypes = set()
                                    has_1st_ed = False
                                    has_unlimited_variation = False
                                    unlimited_subtype_name = None # Store the actual name corresponding to unlimited

                                    for subtype in valid_subtypes:
                                        st_lower = subtype.lower()
                                        # Check for 1st edition variants (including "1st Edition Holofoil")
                                        if '1st edition' in st_lower:
                                            normalized_subtypes.add('1st edition')
                                            has_1st_ed = True
                                        # Treat 'unlimited', 'holofoil', 'reverse holo' etc. as 'unlimited' *if* 1st ed is also present
                                        elif 'unlimited' in st_lower or 'holo' in st_lower or 'foil' in st_lower or 'normal' in st_lower:
                                            normalized_subtypes.add('unlimited')
                                            has_unlimited_variation = True
                                            
                                            # Store the actual subtype name for later use
                                            # Prioritize explicit "Unlimited Holofoil" or "Unlimited" variants
                                            if unlimited_subtype_name is None:
                                                unlimited_subtype_name = subtype
                                            if 'unlimited' in st_lower:
                                                unlimited_subtype_name = subtype

                                    # Check if the only normalized types are 1st ed and unlimited
                                    is_1st_ed_unlimited_only = (normalized_subtypes == {'1st edition', 'unlimited'})
                                    
                                    # Normalize the determined printing type
                                    normalized_determined_type = None
                                    pt_lower = printing_type.lower()
                                    if '1st edition' in pt_lower:
                                        normalized_determined_type = '1st edition'
                                    elif 'unlimited' in pt_lower or 'holo' in pt_lower or 'foil' in pt_lower or 'normal' in pt_lower:
                                         normalized_determined_type = 'unlimited'

                                    logger.info(f"Pokemon Edge Case Check (Product ID: {product_id}, Variant: {variant.get('title', 'N/A')}):")
                                    logger.info(f"  Game: {game_name}, Valid Subtypes: {valid_subtypes}")
                                    logger.info(f"  Normalized Subtypes: {normalized_subtypes}, Is 1st/Unl Only: {is_1st_ed_unlimited_only}")
                                    logger.info(f"  Original Determined Type: {original_printing_type}, Normalized: {normalized_determined_type}")
                                    logger.info(f"  Actual Unlimited Subtype Name Found: {unlimited_subtype_name}")

                                    # Apply override if conditions met
                                    if is_1st_ed_unlimited_only and normalized_determined_type != '1st edition' and unlimited_subtype_name:
                                        printing_type = unlimited_subtype_name # Use the actual unlimited subtype name
                                        logger.info(f"Pokemon edge case: Overriding to '{printing_type}' for variant '{variant.get('title', 'N/A')}' (Product ID: {product_id}). Original was '{original_printing_type}'.")
                                    
                                    # Special case: If product title contains "Unlimited" and we have an unlimited subtype, use it
                                    elif 'unlimited' in product.get('title', '').lower() and unlimited_subtype_name:
                                        printing_type = unlimited_subtype_name
                                        logger.info(f"Pokemon title edge case: Product title contains 'Unlimited', overriding to '{printing_type}' for variant '{variant.get('title', 'N/A')}' (Product ID: {product_id}). Original was '{original_printing_type}'.")
                                    
                            except Exception as edge_case_e:
                                logger.warning(f"Error during Pokemon edge case check for variant '{variant.get('title', 'N/A')}': {str(edge_case_e)}")
                            # --- End: Pokemon 1st Edition/Unlimited Edge Case ---
                        
                            logger.info(f"Product pricing data: {product_pricing}")
                            logger.info(f"Looking for printing type: {printing_type}")
                            
                            # First find the price data for this printing type
                            matched_price = None
                            
                            # First try exact match
                            for p in product_pricing:
                                subtype = p.get('subTypeName', '').lower()
                                logger.info(f"Checking for exact match - subtype: {subtype}")
                                if subtype == printing_type.lower() and has_valid_price(p):
                                    matched_price = p
                                    logger.info(f"Found exact match with valid price: {p}")
                                    break
                            
                            # If no exact match, try holofoil match
                            if not matched_price:
                                for p in product_pricing:
                                    subtype = p.get('subTypeName', '').lower()
                                    logger.info(f"Checking for holofoil match - subtype: {subtype}")
                                    if 'holofoil' in subtype and has_valid_price(p):
                                        matched_price = p
                                        logger.info(f"Found holofoil match with valid price: {p}")
                                        break
                            
                            # If still no match, try normal match
                            if not matched_price:
                                for p in product_pricing:
                                    subtype = p.get('subTypeName', '').lower()
                                    logger.info(f"Checking for normal match - subtype: {subtype}")
                                    if subtype == 'normal' and has_valid_price(p):
                                        matched_price = p
                                        logger.info(f"Found normal match with valid price: {p}")
                                        break
                            
                            # If no matches with valid prices, try to find any valid price
                            if not matched_price:
                                for p in product_pricing:
                                    if has_valid_price(p):
                                        matched_price = p
                                        logger.info(f"Using first available price with valid data: {p}")
                                        break

                            if matched_price:
                                # Extract pricing info for this printing type
                                pricing_info = {}
                                
                                # Get market price first as it's often the most reliable
                                market_price = matched_price.get('marketPrice')
                                if market_price is not None:
                                    pricing_info['marketPrice'] = float(market_price)
                                    logger.info(f"Market Price: ${market_price}")
                                
                                # Get low price (try directLowPrice first, then lowPrice)
                                low_price = matched_price.get('directLowPrice')
                                if low_price is None:
                                    low_price = matched_price.get('lowPrice')
                                if low_price is not None:
                                    pricing_info['lowPrice'] = float(low_price)
                                    logger.info(f"Low Price: ${low_price}")
                                
                                # Get mid price (fallback to market price if not available)
                                mid_price = matched_price.get('midPrice')
                                if mid_price is None and market_price is not None:
                                    mid_price = market_price
                                if mid_price is not None:
                                    pricing_info['midPrice'] = float(mid_price)
                                    logger.info(f"Mid Price: ${mid_price}")
                                
                                # Get high price (fallback to market price if not available)
                                high_price = matched_price.get('highPrice')
                                if high_price is None and market_price is not None:
                                    high_price = market_price
                                if high_price is not None:
                                    pricing_info['highPrice'] = float(high_price)
                                    logger.info(f"High Price: ${high_price}")
                                
                                # Log summary of extracted prices
                                logger.info(f"\nExtracted prices for {printing_type}:")
                                for price_type, price in pricing_info.items():
                                    logger.info(f"  {price_type}: ${price}")
                                    
                                # Only proceed if we have at least one valid price
                                if not any(price is not None for price in pricing_info.values()):
                                    logger.error("No valid prices found in matched price data")
                                    continue

                                # Update product info to include printing type and condition
                                product['printingType'] = printing_type
                                product['condition'] = extract_condition(variant.get('option1', ''))
                                
                                # Prepare settings with custom stepping rules
                                from routes.warehouse.utils import prepare_shopify_settings
                                settings = prepare_shopify_settings(username)
                                
                                # Override customStepping with the user's pricing rules
                                settings['customStepping'] = pricing_rules
                                min_price = settings.get('minPrice', 0)
                                
                                logger.info(f"Using custom stepping rules: {pricing_rules}")
                                
                                new_price, is_missing = calculate_variant_price(
                                    variant=variant,
                                    pricing_data=pricing_info,
                                    product_pricing=product_pricing,
                                    pricing_rules=pricing_rules,
                                    min_price_local=min_price,
                                    exchange_rate=exchange_rate,
                                    is_test_user=is_test_user,
                                    username=username,
                                    product=product,
                                    user_profile=user_profile,
                                    user_currency=user_currency
                                )
                                
                                if not is_missing and new_price is not None:
                                    if abs(old_price - new_price) > 0.01:
                                        variant['price'] = str(new_price)
                                        variants_changed = True
                                        total_price_changes += 1
                        except Exception:
                            continue

                    # Always update variants and mark for pushing to ensure sync
                    bulk_updates.append(
                        UpdateOne(
                            {'_id': product['_id']},
                            {
                                '$set': {
                                    'variants': variants,
                                    'needsPushing': True,
                                    'last_repriced': datetime.now(timezone.utc)
                                }
                            }
                        )
                    )
                    total_updates += 1

                except Exception:
                    continue

                if bulk_updates:
                    try:
                        # Use verified bulk write with retry logic
                        result = verified_bulk_write(shopify_collection, bulk_updates, ordered=False)
                        
                        # Log successful transactions
                        for update in bulk_updates:
                            if isinstance(update, UpdateOne) and update._filter.get('_id'):
                                product_id = update._filter.get('_id')
                                # Extract variant price changes from the update
                                if '$set' in update._doc and 'variants' in update._doc['$set']:
                                    variants = update._doc['$set']['variants']
                                    # Create expected prices dictionary for verification
                                    expected_prices = {}
                                    for variant in variants:
                                        if 'id' in variant and 'price' in variant:
                                            variant_id = variant['id']
                                            new_price = float(variant['price'])
                                            expected_prices[variant_id] = new_price
                                    
                                    # Schedule verification
                                    if expected_prices:
                                        schedule_verification(product_id, expected_prices)
                        
                        bulk_updates = []
                    except Exception as e:
                        logger.error(f"Error in bulk write: {str(e)}")
                        # Don't clear bulk_updates so we can retry on the next iteration
                        # But limit the size to avoid memory issues
                        if len(bulk_updates) > 1000:
                            bulk_updates = bulk_updates[-1000:]  # Keep only the last 1000 updates

        print(f"\nSummary for {username}:")
        print(f"Total products processed: {len(filtered_products)}")
        print(f"Products with price changes: {total_price_changes}")
        print(f"Records marked for pushing: {total_updates}")
        
        # Print cache statistics
        print(f"\nCache Statistics:")
        stats = tcgplayer_cache.get_stats()
        print(f"Cache Hit Rate: {stats['hit_rate']:.2f}%")
        print(f"Cache Hits: {stats['hits']}")
        print(f"Cache Misses: {stats['misses']}")
        print(f"Cache Size: {stats['cache_size']}")
        
        # Print execution time
        end_time = datetime.now(timezone.utc)
        duration = end_time - start_time
        print(f"\nExecution Time: {duration.total_seconds():.2f} seconds")

    except Exception:
        pass

def verify_pending_changes():
    """
    Process the verification queue to ensure changes were applied.
    """
    try:
        init_collections()  # Initialize collections
        
        pending_verifications = verification_queue_collection.find({
            "status": "pending",
            "attempts": {"$lt": 3},
            "created_at": {"$gt": datetime.now(timezone.utc) - timedelta(hours=24)}
        })
        
        for verification in pending_verifications:
            try:
                product = shopify_collection.find_one({"_id": verification["product_id"]})
                if not product:
                    continue
                    
                mismatches = []
                for variant in product["variants"]:
                    variant_id = variant["id"]
                    if variant_id in verification["expected_prices"]:
                        expected_price = verification["expected_prices"][variant_id]
                        actual_price = float(variant["price"])
                        
                        if abs(expected_price - actual_price) > 0.01:
                            mismatches.append({
                                "variant_id": variant_id,
                                "expected": expected_price,
                                "actual": actual_price
                            })
                
                if mismatches:
                    # Handle mismatches - could retry the update or alert
                    verification_queue_collection.update_one(
                        {"_id": verification["_id"]},
                        {"$inc": {"attempts": 1}, "$set": {"mismatches": mismatches}}
                    )
                    
                    # If this is the last attempt, try to fix the mismatches
                    if verification["attempts"] + 1 >= 3:
                        # Create update operations to fix mismatches
                        for variant in product["variants"]:
                            variant_id = variant["id"]
                            for mismatch in mismatches:
                                if mismatch["variant_id"] == variant_id:
                                    variant["price"] = str(mismatch["expected"])
                        
                        # Update the product with corrected prices
                        verified_update_one(
                            shopify_collection,
                            {"_id": verification["product_id"]},
                            {"$set": {"variants": product["variants"], "needsPushing": True}}
                        )
                        
                        # Mark as fixed
                        verification_queue_collection.update_one(
                            {"_id": verification["_id"]},
                            {"$set": {"status": "fixed"}}
                        )
                else:
                    # Mark as verified
                    verification_queue_collection.update_one(
                        {"_id": verification["_id"]},
                        {"$set": {"status": "verified"}}
                    )
                    
                    # Also update the pricing transactions
                    pricing_transactions_collection.update_many(
                        {"product_id": verification["product_id"]},
                        {"$set": {"verified": True}}
                    )
                    
            except Exception as e:
                logger.error(f"Error verifying changes: {str(e)}")
                verification_queue_collection.update_one(
                    {"_id": verification["_id"]},
                    {"$inc": {"attempts": 1}, "$set": {"last_error": str(e)}}
                )
    except Exception as e:
        logger.error(f"Error in verify_pending_changes: {str(e)}")

def process_single_product(product, username, tcgplayer_api_key):
    """Process a single product for repricing using the same logic as bulk repricing"""
    init_collections()  # Initialize collections using default connection
    try:
        # Get user profile and settings
        user_profile = user_collection.find_one({'username': username})
        if not user_profile:
            return None, []
            
        from models.shopify_settings_model import ShopifySettings
        shopify_settings = ShopifySettings.objects(username=username).first()
        if not shopify_settings:
            shopify_settings = ShopifySettings(username=username)
            shopify_settings.save()
        
        user_currency = user_profile.get('currency', 'USD')
        exchange_rate = get_exchange_rate(user_currency)

        # Skip products with manual override
        if product.get('manualOverride', False):
            return None, []

        # Get pricing data
        product_id = str(product.get('productId'))
        if not product_id:
            return None, []

        pricing_data = fetch_pricing_data([product_id], tcgplayer_api_key)
        if not product_id in pricing_data:
            return None, []

        product_pricing = pricing_data.get(product_id, [])
        if not isinstance(product_pricing, list):
            product_pricing = [product_pricing]
        # Only include subtypes that have valid prices
        valid_subtypes = [p.get('subTypeName') for p in product_pricing 
                         if p.get('subTypeName') and 
                         (p.get('marketPrice') is not None or 
                          p.get('lowPrice') is not None or 
                          p.get('directLowPrice') is not None)]
        logger.info(f"Valid subtypes: {valid_subtypes}")
        pricing_rules = get_pricing_rules(user_profile, product)
        
        variants_changed = False
        variants = product['variants']
        price_changes = []
        
        for variant in variants:
            try:
                old_price = float(variant.get('price', 0))
                printing_type = determine_printing_type(variant['title'], valid_subtypes)
            
                logger.info(f"Product pricing data: {product_pricing}")
                logger.info(f"Looking for printing type: {printing_type}")
                
                # First find the price data for this printing type
                matched_price = None
                
                # First try exact match
                for p in product_pricing:
                    subtype = p.get('subTypeName', '').lower()
                    logger.info(f"Checking for exact match - subtype: {subtype}")
                    if subtype == printing_type.lower() and has_valid_price(p):
                        matched_price = p
                        logger.info(f"Found exact match with valid price: {p}")
                        break
                
                # If no exact match, try holofoil match
                if not matched_price:
                    for p in product_pricing:
                        subtype = p.get('subTypeName', '').lower()
                        logger.info(f"Checking for holofoil match - subtype: {subtype}")
                        if 'holofoil' in subtype and has_valid_price(p):
                            matched_price = p
                            logger.info(f"Found holofoil match with valid price: {p}")
                            break
                
                # If still no match, try normal match
                if not matched_price:
                    for p in product_pricing:
                        subtype = p.get('subTypeName', '').lower()
                        logger.info(f"Checking for normal match - subtype: {subtype}")
                        if subtype == 'normal' and has_valid_price(p):
                            matched_price = p
                            logger.info(f"Found normal match with valid price: {p}")
                            break
                
                # If no matches with valid prices, try to find any valid price
                if not matched_price:
                    for p in product_pricing:
                        if has_valid_price(p):
                            matched_price = p
                            logger.info(f"Using first available price with valid data: {p}")
                            break

                if matched_price:
                    logger.info(f"Found matching price data for {printing_type}:")
                    logger.info(f"Raw price data: {matched_price}")
                    
                    # Extract pricing info for this printing type
                    pricing_info = {}
                    
                    # Get market price first as it's often the most reliable
                    market_price = matched_price.get('marketPrice')
                    if market_price is not None:
                        pricing_info['marketPrice'] = float(market_price)
                        logger.info(f"Market Price: ${market_price}")
                    
                    # Get low price (try directLowPrice first, then lowPrice)
                    low_price = matched_price.get('directLowPrice')
                    if low_price is None:
                        low_price = matched_price.get('lowPrice')
                    if low_price is not None:
                        pricing_info['lowPrice'] = float(low_price)
                        logger.info(f"Low Price: ${low_price}")
                    
                    # Get mid price (fallback to market price if not available)
                    mid_price = matched_price.get('midPrice')
                    if mid_price is None and market_price is not None:
                        mid_price = market_price
                    if mid_price is not None:
                        pricing_info['midPrice'] = float(mid_price)
                        logger.info(f"Mid Price: ${mid_price}")
                    
                    # Get high price (fallback to market price if not available)
                    high_price = matched_price.get('highPrice')
                    if high_price is None and market_price is not None:
                        high_price = market_price
                    if high_price is not None:
                        pricing_info['highPrice'] = float(high_price)
                        logger.info(f"High Price: ${high_price}")

                    # Update product info to include printing type and condition
                    product['printingType'] = printing_type
                    product['condition'] = extract_condition(variant.get('option1', ''))
                    
                    # Prepare settings with custom stepping rules
                    from routes.warehouse.utils import prepare_shopify_settings
                    settings = prepare_shopify_settings(username)
                    
                    # Override customStepping with the user's pricing rules
                    settings['customStepping'] = pricing_rules
                    min_price = settings.get('minPrice', 0)
                    
                    logger.info(f"Using custom stepping rules: {pricing_rules}")
                    
                    new_price, is_missing = calculate_variant_price(
                        variant=variant,
                        pricing_data=pricing_info,
                        product_pricing=product_pricing,
                        pricing_rules=pricing_rules,
                        min_price_local=min_price,
                        exchange_rate=exchange_rate,
                        is_test_user=username.lower() == TEST_USERNAME.lower(),
                        username=username,
                        product=product,
                        user_profile=user_profile,
                        user_currency=user_currency
                    )
                    
                    if not is_missing and new_price is not None:
                        if abs(old_price - new_price) > 0.01:
                            variant['price'] = str(new_price)
                            variants_changed = True
                            price_changes.append({
                                'variant_id': variant['id'],
                                'variant_title': variant['title'],
                                'old_price': old_price,
                                'new_price': new_price
                            })
            except Exception:
                continue

        # Always return variants to ensure sync with Shopify
        if variants_changed:
            # Log successful price changes
            for change in price_changes:
                log_pricing_transaction(
                    username=username,
                    product_id=product['_id'],
                    variant_id=change['variant_id'],
                    old_price=change['old_price'],
                    new_price=change['new_price'],
                    status='success'
                )
            
            # Create expected prices dictionary for verification
            expected_prices = {}
            for change in price_changes:
                expected_prices[change['variant_id']] = change['new_price']
            
            # Schedule verification
            if expected_prices:
                schedule_verification(product['_id'], expected_prices)
        
        return variants, price_changes

    except Exception as e:
        error_msg = f"Error processing single product: {str(e)}"
        logger.error(error_msg)
        
        # Log error in transaction
        try:
            log_pricing_transaction(
                username=username,
                product_id=product.get('_id', 'unknown'),
                variant_id='unknown',
                old_price=0,
                new_price=0,
                status='error',
                error=error_msg
            )
        except Exception as log_error:
            logger.error(f"Failed to log error transaction: {str(log_error)}")
            
        return None, []

def process_user_wrapper(args):
    """Wrapper function to unpack arguments for process_user when using ThreadPoolExecutor"""
    config, tcgplayer_api_key = args
    try:
        process_user(config, tcgplayer_api_key)
    except Exception as e:
        print(f"Error processing user {config.get('username')}: {str(e)}")

def bulk_reprice(max_workers=30):
    try:
        init_collections()  # Initialize collections using default connection
        currency_cache.clear_expired()
        
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            logger.error("No TCGPlayer API key found")
            return

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        
        # Get all user configs from autopricer collection
        user_configs = list(autopricer_collection.find({}))
        if not user_configs:
            logger.warning("No user configs found for autopricing")
            return
            
        print(f"\nStarting bulk reprice for {len(user_configs)} users")
        
        # Process users concurrently using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create list of (config, api_key) tuples for each user
            args = [(config, tcgplayer_api_key) for config in user_configs]
            
            # Submit all tasks and wait for completion
            list(executor.map(process_user_wrapper, args))
        
        # Run verification for pending changes
        verify_pending_changes()
                
        print("\nBulk reprice completed")

    except Exception as e:
        error_msg = f"Bulk reprice error: {str(e)}"
        logger.error(error_msg)
        print(error_msg)

def run_scheduled_task():
   try:
       bulk_reprice()
   except Exception:
       pass

def cleanup_resources():
   try:
       session.close()
   except Exception:
       pass

def handle_shutdown(signum, frame):
   cleanup_resources()
   sys.exit(0)

def main():
   try:
       signal.signal(signal.SIGTERM, handle_shutdown)
       signal.signal(signal.SIGINT, handle_shutdown)
       
       # Run in a loop, sleeping for 15 minutes between runs
       while True:
           start_time = datetime.now(timezone.utc)
           print(f"\n=== Starting bulk reprice at {start_time} ===")
           
           # Clear expired cache entries and run the bulk reprice
           currency_cache.clear_expired()
           bulk_reprice()
           
           end_time = datetime.now(timezone.utc)
           duration = end_time - start_time
           print(f"=== Completed bulk reprice in {duration.total_seconds():.2f} seconds ===")
           
           # Sleep for 15 minutes before the next run
           sleep_time = 15 * 60  # 15 minutes in seconds
           time.sleep(sleep_time)
       
   except KeyboardInterrupt:
       print("Received keyboard interrupt. Shutting down...")
   except Exception as e:
       print(f"Error running bulk reprice: {str(e)}")
       sys.exit(1)
   finally:
       cleanup_resources()
       sys.exit(0)

if __name__ == "__main__":
   main()
