@echo off
echo File Upload Script
echo =================

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is required but not installed.
    exit /b 1
)

REM Install dependencies if needed
echo Installing dependencies...
pip install -r deployment\requirements.txt

REM Get the path to upload
set /p LOCAL_PATH="Enter the path to the file or directory you want to upload: "

REM Run the uploader script
echo Running file uploader...
python deployment\upload_files.py "%LOCAL_PATH%"

echo Done!
pause
