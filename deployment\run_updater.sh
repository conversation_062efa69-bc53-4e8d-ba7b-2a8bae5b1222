#!/bin/bash
# <PERSON>ript to run the server updater

# Set up error handling
set -e

echo "Server Updater Script"
echo "====================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not installed."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip install -r deployment/requirements.txt

# Run the updater script
echo "Running server updater..."
python3 deployment/server_updater.py

echo "Done!"
