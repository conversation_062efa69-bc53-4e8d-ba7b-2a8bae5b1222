#!/usr/bin/env python3
"""
Shopify Webhook Monitoring Script

This script connects to a remote server via SSH using Paramiko and monitors
the Shopify webhook system, ensuring webhooks are being processed correctly
and analyzing logs for errors or issues.
"""

import paramiko
import time
import re
import sys
import socket
import datetime
import argparse
import json
from collections import defaultdict

class ShopifyWebhookMonitor:
    def __init__(self, hostname, username, password, port=22):
        """Initialize the monitor with server connection details."""
        self.hostname = hostname
        self.username = username
        self.password = password
        self.port = port
        self.ssh = None
        self.results = {}
        self.issues_found = []
        self.recommendations = []
        self.log_file_path = "/path/to/shopify_webhook.log"  # Default path, will be updated

    def connect(self):
        """Establish SSH connection to the server."""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            print(f"Connecting to {self.hostname}...")
            self.ssh.connect(
                hostname=self.hostname,
                username=self.username,
                password=self.password,
                port=self.port,
                timeout=10
            )
            print("Connection established successfully.")
            
            # Find the actual log file path
            self.find_log_file_path()
            
            return True
        except (paramiko.AuthenticationException, paramiko.SSHException, socket.error) as e:
            print(f"Error connecting to {self.hostname}: {str(e)}")
            return False

    def disconnect(self):
        """Close the SSH connection."""
        if self.ssh:
            self.ssh.close()
            print("Connection closed.")

    def execute_command(self, command, timeout=30):
        """Execute a command on the remote server and return the output."""
        if not self.ssh:
            print("Not connected to server. Please connect first.")
            return None

        try:
            print(f"Executing: {command}")
            stdin, stdout, stderr = self.ssh.exec_command(command, timeout=timeout)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if error:
                print(f"Command error: {error}")
            
            return output
        except Exception as e:
            print(f"Error executing command '{command}': {str(e)}")
            return None

    def find_log_file_path(self):
        """Find the actual path of the shopify webhook log file."""
        try:
            # Try to find the log file using find command
            find_result = self.execute_command("find / -name 'shopify_webhook.log' 2>/dev/null")
            
            if find_result and find_result.strip():
                # Use the first result
                self.log_file_path = find_result.strip().split('\n')[0]
                print(f"Found webhook log file at: {self.log_file_path}")
            else:
                # Try to find the log file by checking the Flask app directory
                app_dir_result = self.execute_command("find / -name 'shopify_webhooks.py' 2>/dev/null")
                if app_dir_result and app_dir_result.strip():
                    app_dir = '/'.join(app_dir_result.strip().split('\n')[0].split('/')[:-1])
                    log_check = self.execute_command(f"find {app_dir} -name '*.log' | grep -i webhook")
                    
                    if log_check and log_check.strip():
                        self.log_file_path = log_check.strip().split('\n')[0]
                        print(f"Found webhook log file at: {self.log_file_path}")
                    else:
                        print("Could not find webhook log file. Using default path.")
                else:
                    print("Could not find webhook log file. Using default path.")
        except Exception as e:
            print(f"Error finding log file path: {str(e)}")

    def check_webhook_service_status(self):
        """Check if the webhook service is running."""
        print("\n=== Checking Webhook Service Status ===")
        results = {}
        
        # Check if the Flask app is running
        flask_processes = self.execute_command("ps aux | grep -i 'python.*app.py\\|gunicorn' | grep -v grep")
        if flask_processes:
            results['flask_processes'] = flask_processes
            print("Flask application is running.")
        else:
            self.issues_found.append("Flask application does not appear to be running")
            self.recommendations.append(
                "Start the Flask application or check for errors in the application logs."
            )
            print("WARNING: Flask application does not appear to be running!")
        
        # Check if there are any webhook worker threads
        webhook_workers = self.execute_command("ps aux | grep -i 'webhook_worker' | grep -v grep")
        if webhook_workers:
            results['webhook_workers'] = webhook_workers
            print("Webhook worker threads are running.")
        else:
            # This might be a false negative since worker threads might not show up in ps output
            print("Note: Could not detect webhook worker threads. This might be normal depending on implementation.")
        
        # Check system load to see if the server is overloaded
        load_avg = self.execute_command("cat /proc/loadavg")
        if load_avg:
            parts = load_avg.strip().split()
            if len(parts) >= 3:
                results['load_1min'] = float(parts[0])
                results['load_5min'] = float(parts[1])
                results['load_15min'] = float(parts[2])
                
                # Get number of cores
                core_count = self.execute_command("nproc")
                if core_count:
                    cores = int(core_count.strip())
                    results['cpu_cores'] = cores
                    
                    # Check if load is high relative to number of cores
                    if float(parts[0]) > cores * 0.8:
                        self.issues_found.append(f"High system load: {parts[0]} (>80% of available cores)")
                        self.recommendations.append(
                            "System load is high which may affect webhook processing. Consider optimizing or scaling resources."
                        )
                        print(f"WARNING: High system load detected: {parts[0]} on {cores} cores")
                    else:
                        print(f"System load is normal: {parts[0]} on {cores} cores")
        
        # Check memory usage
        memory_info = self.execute_command("free -m")
        if memory_info:
            results['memory_info'] = memory_info
            
            # Parse memory info
            lines = memory_info.strip().splitlines()
            if len(lines) >= 2:
                mem_parts = lines[1].split()
                if len(mem_parts) >= 7:
                    total_memory = int(mem_parts[1])
                    used_memory = int(mem_parts[2])
                    memory_usage_pct = (used_memory / total_memory) * 100
                    
                    if memory_usage_pct > 90:
                        self.issues_found.append(f"Very high memory usage: {memory_usage_pct:.1f}%")
                        self.recommendations.append(
                            "Memory usage is extremely high. This may cause webhook processing issues."
                        )
                        print(f"WARNING: Very high memory usage: {memory_usage_pct:.1f}%")
                    elif memory_usage_pct > 80:
                        self.issues_found.append(f"High memory usage: {memory_usage_pct:.1f}%")
                        self.recommendations.append(
                            "Memory usage is high. Consider optimizing memory usage."
                        )
                        print(f"WARNING: High memory usage: {memory_usage_pct:.1f}%")
                    else:
                        print(f"Memory usage is normal: {memory_usage_pct:.1f}%")
        
        self.results['service_status'] = results
        return results

    def check_webhook_logs(self):
        """Check webhook logs for errors and issues."""
        print("\n=== Checking Webhook Logs ===")
        results = {}
        
        # Get the last 100 log entries
        log_entries = self.execute_command(f"tail -n 100 {self.log_file_path}")
        if not log_entries:
            self.issues_found.append(f"Could not read webhook log file at {self.log_file_path}")
            self.recommendations.append(
                "Check if the log file exists and has proper permissions."
            )
            print(f"ERROR: Could not read webhook log file at {self.log_file_path}")
            return {}
        
        results['recent_logs'] = log_entries
        
        # Count errors and warnings
        error_count = len(re.findall(r'ERROR', log_entries))
        warning_count = len(re.findall(r'WARNING', log_entries))
        
        results['error_count'] = error_count
        results['warning_count'] = warning_count
        
        print(f"Found {error_count} errors and {warning_count} warnings in recent logs")
        
        if error_count > 0:
            self.issues_found.append(f"Found {error_count} errors in recent webhook logs")
            
            # Extract error messages
            error_lines = re.findall(r'.*ERROR.*', log_entries)
            results['error_lines'] = error_lines
            
            # Group similar errors
            error_patterns = defaultdict(int)
            for line in error_lines:
                # Extract the error message without timestamps and specific IDs
                match = re.search(r'ERROR.*?:(.*)', line)
                if match:
                    error_msg = match.group(1).strip()
                    # Remove specific IDs and values
                    error_pattern = re.sub(r'[0-9a-f]{24}|[0-9]{5,}', 'ID', error_msg)
                    error_patterns[error_pattern] += 1
            
            results['error_patterns'] = dict(error_patterns)
            
            # Add recommendations based on common error patterns
            for pattern, count in error_patterns.items():
                if count > 1:  # If the error occurs multiple times
                    if "timeout" in pattern.lower():
                        self.recommendations.append(
                            f"Multiple timeout errors detected. Check database connection and performance."
                        )
                    elif "connection" in pattern.lower():
                        self.recommendations.append(
                            f"Connection errors detected. Check network connectivity to external services."
                        )
                    elif "database" in pattern.lower() or "mongo" in pattern.lower():
                        self.recommendations.append(
                            f"Database errors detected. Check MongoDB connection and performance."
                        )
                    else:
                        self.recommendations.append(
                            f"Recurring error pattern: {pattern} (occurred {count} times)"
                        )
        
        if warning_count > 10:
            self.issues_found.append(f"High number of warnings ({warning_count}) in recent webhook logs")
            self.recommendations.append(
                "Review warnings in the webhook logs and address recurring issues."
            )
        
        # Check for successful webhook processing
        success_count = len(re.findall(r'Successfully.*webhook', log_entries))
        results['success_count'] = success_count
        
        print(f"Found {success_count} successful webhook processing entries")
        
        if success_count == 0:
            self.issues_found.append("No successful webhook processing found in recent logs")
            self.recommendations.append(
                "Check if webhooks are being received and processed correctly."
            )
        
        # Check for recent activity
        last_activity = self.execute_command(f"tail -n 1 {self.log_file_path}")
        if last_activity:
            # Try to extract timestamp
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', last_activity)
            if timestamp_match:
                last_timestamp = timestamp_match.group(1)
                try:
                    last_time = datetime.datetime.strptime(last_timestamp, '%Y-%m-%d %H:%M:%S')
                    now = datetime.datetime.now()
                    time_diff = now - last_time
                    
                    results['last_activity'] = last_timestamp
                    results['minutes_since_last_activity'] = time_diff.total_seconds() / 60
                    
                    if time_diff.total_seconds() > 3600:  # More than an hour
                        self.issues_found.append(f"No webhook activity for {time_diff.total_seconds() / 3600:.1f} hours")
                        self.recommendations.append(
                            "Check if Shopify webhooks are properly configured and being triggered."
                        )
                        print(f"WARNING: No webhook activity for {time_diff.total_seconds() / 3600:.1f} hours")
                    else:
                        print(f"Last webhook activity: {time_diff.total_seconds() / 60:.1f} minutes ago")
                except Exception as e:
                    print(f"Error parsing timestamp: {str(e)}")
        
        self.results['webhook_logs'] = results
        return results

    def check_webhook_queue(self):
        """Check the webhook queue status."""
        print("\n=== Checking Webhook Queue Status ===")
        results = {}
        
        # Try to access the webhook status endpoint
        curl_command = "curl -s http://localhost:5000/webhook/status || curl -s http://127.0.0.1:5000/webhook/status"
        status_response = self.execute_command(curl_command)
        
        if not status_response:
            print("Could not access webhook status endpoint. Trying alternative methods...")
            
            # Try to check MongoDB directly for webhook queue status
            mongo_check = self.execute_command("which mongo")
            if mongo_check and "not found" not in mongo_check:
                # MongoDB connection string from the shopify_webhooks.py file
                mongo_connection_string = "mongodb://admin:Reggie2805!@*************:27017,***************:27017,***************:27017/admin?replicaSet=rs0&authSource=admin"
                mongo_cmd = f'mongo --quiet --eval "db.shopifyWebhooks.find({{status: \'pending\'}}).count()" "{mongo_connection_string}"'
                pending_count = self.execute_command(mongo_cmd)
                
                if pending_count and pending_count.strip().isdigit():
                    results['pending_webhooks'] = int(pending_count.strip())
                    print(f"Pending webhooks in queue: {pending_count.strip()}")
                    
                    if int(pending_count.strip()) > 100:
                        self.issues_found.append(f"Large number of pending webhooks: {pending_count.strip()}")
                        self.recommendations.append(
                            "Webhook queue has a backlog. Check processing speed and worker threads."
                        )
                
                mongo_cmd = f'mongo --quiet --eval "db.shopifyWebhooks.find({{status: \'error\'}}).count()" "{mongo_connection_string}"'
                error_count = self.execute_command(mongo_cmd)
                
                if error_count and error_count.strip().isdigit():
                    results['error_webhooks'] = int(error_count.strip())
                    print(f"Webhooks with errors: {error_count.strip()}")
                    
                    if int(error_count.strip()) > 0:
                        self.issues_found.append(f"Webhooks with errors: {error_count.strip()}")
                        self.recommendations.append(
                            "Some webhooks have failed processing. Check logs for details."
                        )
        else:
            try:
                # Parse JSON response
                status_data = json.loads(status_response)
                if 'metrics' in status_data:
                    metrics = status_data['metrics']
                    results['queue_metrics'] = metrics
                    
                    print(f"Queue size: {metrics.get('queue_size', 'N/A')}")
                    print(f"Processed count: {metrics.get('processed_count', 'N/A')}")
                    print(f"Error count: {metrics.get('error_count', 'N/A')}")
                    print(f"Average processing time: {metrics.get('average_processing_time', 'N/A')} seconds")
                    
                    # Check for issues
                    if metrics.get('queue_size', 0) > 100:
                        self.issues_found.append(f"Large webhook queue size: {metrics.get('queue_size')}")
                        self.recommendations.append(
                            "Webhook queue has a backlog. Check processing speed and worker threads."
                        )
                    
                    if metrics.get('error_count', 0) > metrics.get('processed_count', 1) * 0.1:  # More than 10% errors
                        self.issues_found.append(f"High webhook error rate: {metrics.get('error_count')} errors out of {metrics.get('processed_count')} processed")
                        self.recommendations.append(
                            "High error rate in webhook processing. Check logs for recurring errors."
                        )
                    
                    if metrics.get('average_processing_time', 0) > 5:  # More than 5 seconds per webhook
                        self.issues_found.append(f"Slow webhook processing: {metrics.get('average_processing_time')} seconds average")
                        self.recommendations.append(
                            "Webhook processing is slow. Optimize database operations or increase resources."
                        )
                    
                    # Check circuit breaker status
                    if metrics.get('circuit_breaker_state') == "OPEN":
                        self.issues_found.append("Database circuit breaker is OPEN")
                        self.recommendations.append(
                            "Database circuit breaker is open. Check database connectivity and performance."
                        )
                        print("WARNING: Database circuit breaker is OPEN")
                    
                    # Check recent webhooks
                    if 'recent_webhooks' in metrics:
                        recent_errors = [w for w in metrics['recent_webhooks'] if w.get('status') == 'error']
                        if recent_errors:
                            self.issues_found.append(f"Recent webhook errors: {len(recent_errors)}")
                            self.recommendations.append(
                                "Recent webhooks have failed. Check specific errors and retry if needed."
                            )
            except json.JSONDecodeError:
                print("Could not parse webhook status response as JSON")
            except Exception as e:
                print(f"Error processing webhook status: {str(e)}")
        
        self.results['webhook_queue'] = results
        return results

    def check_mongodb_status(self):
        """Check MongoDB status and performance."""
        print("\n=== Checking MongoDB Status ===")
        results = {}
        
        # MongoDB connection string from the shopify_webhooks.py file
        mongo_connection_string = "mongodb://admin:Reggie2805!@*************:27017,***************:27017,***************:27017/admin?replicaSet=rs0&authSource=admin"
        mongo_host = "*************"
        
        # Check if we can ping the MongoDB server
        ping_result = self.execute_command(f"ping -c 3 {mongo_host}")
        if ping_result:
            results['mongo_ping'] = ping_result
            
            # Check if ping was successful
            if "0% packet loss" in ping_result:
                print(f"Connectivity to MongoDB server ({mongo_host}) is good")
            else:
                packet_loss_match = re.search(r'(\d+)% packet loss', ping_result)
                if packet_loss_match:
                    loss_percentage = packet_loss_match.group(1)
                    if int(loss_percentage) > 0:
                        self.issues_found.append(f"Packet loss to MongoDB server: {loss_percentage}%")
                        self.recommendations.append(
                            "Network connectivity to MongoDB server is unstable. Check network configuration."
                        )
                        print(f"WARNING: Packet loss to MongoDB server: {loss_percentage}%")
        else:
            self.issues_found.append(f"Could not ping MongoDB server at {mongo_host}")
            self.recommendations.append(
                "Check network connectivity to the MongoDB server."
            )
            print(f"WARNING: Could not ping MongoDB server at {mongo_host}!")
        
        # Check MongoDB status using mongo command with connection string
        mongo_check = self.execute_command("which mongo")
        if mongo_check and "not found" not in mongo_check:
            # Check MongoDB server status
            mongo_cmd = f'mongo --quiet --eval "JSON.stringify(db.serverStatus())" "{mongo_connection_string}"'
            server_status = self.execute_command(mongo_cmd)
            
            if server_status:
                try:
                    status_data = json.loads(server_status)
                    
                    # Extract key metrics
                    connections = status_data.get('connections', {})
                    current_connections = connections.get('current', 0)
                    available_connections = connections.get('available', 0)
                    
                    results['mongo_connections'] = {
                        'current': current_connections,
                        'available': available_connections
                    }
                    
                    print(f"MongoDB connections: {current_connections} current, {available_connections} available")
                    
                    if current_connections > 0.8 * (current_connections + available_connections):
                        self.issues_found.append(f"High MongoDB connection usage: {current_connections} of {current_connections + available_connections}")
                        self.recommendations.append(
                            "MongoDB connection pool is nearly exhausted. Check for connection leaks or increase maxConnections."
                        )
                    
                    # Check for slow operations
                    mongo_cmd = f'mongo --quiet --eval "JSON.stringify(db.currentOp({{\'secs_running\': {{$gt: 5}}}}))" {mongo_connection_string}'
                    slow_ops = self.execute_command(mongo_cmd)
                    
                    if slow_ops:
                        try:
                            slow_ops_data = json.loads(slow_ops)
                            if 'inprog' in slow_ops_data and len(slow_ops_data['inprog']) > 0:
                                results['mongo_slow_ops'] = slow_ops_data['inprog']
                                self.issues_found.append(f"Slow MongoDB operations: {len(slow_ops_data['inprog'])}")
                                self.recommendations.append(
                                    "MongoDB has slow running operations. Check for missing indexes or optimize queries."
                                )
                                print(f"WARNING: Found {len(slow_ops_data['inprog'])} slow MongoDB operations")
                        except json.JSONDecodeError:
                            print("Could not parse MongoDB slow operations response")
                    
                    # Check shopifyWebhooks collection stats
                    mongo_cmd = f'mongo --quiet --eval "JSON.stringify(db.shopifyWebhooks.stats())" "{mongo_connection_string}"'
                    collection_stats = self.execute_command(mongo_cmd)
                    
                    if collection_stats:
                        try:
                            stats_data = json.loads(collection_stats)
                            results['webhook_collection_stats'] = {
                                'count': stats_data.get('count', 0),
                                'size': stats_data.get('size', 0),
                                'avgObjSize': stats_data.get('avgObjSize', 0)
                            }
                            
                            print(f"Webhook collection: {stats_data.get('count', 0)} documents, "
                                  f"{stats_data.get('size', 0) / (1024*1024):.2f} MB")
                            
                            if stats_data.get('count', 0) > 10000:
                                self.issues_found.append(f"Large webhook collection: {stats_data.get('count', 0)} documents")
                                self.recommendations.append(
                                    "Webhook collection has many documents. Consider implementing cleanup for old webhooks."
                                )
                        except json.JSONDecodeError:
                            print("Could not parse MongoDB collection stats response")
                    
                except json.JSONDecodeError:
                    print("Could not parse MongoDB server status response")
        
        self.results['mongodb_status'] = results
        return results

    def check_shopify_connectivity(self):
        """Check connectivity to Shopify API."""
        print("\n=== Checking Shopify Connectivity ===")
        results = {}
        
        # Check connectivity to Shopify
        ping_result = self.execute_command("ping -c 3 shopify.com")
        if ping_result:
            results['shopify_ping'] = ping_result
            
            # Check if ping was successful
            if "0% packet loss" in ping_result:
                print("Connectivity to Shopify.com is good")
            else:
                packet_loss_match = re.search(r'(\d+)% packet loss', ping_result)
                if packet_loss_match:
                    loss_percentage = packet_loss_match.group(1)
                    if int(loss_percentage) > 0:
                        self.issues_found.append(f"Packet loss to Shopify.com: {loss_percentage}%")
                        self.recommendations.append(
                            "Network connectivity to Shopify is unstable. Check network configuration."
                        )
                        print(f"WARNING: Packet loss to Shopify.com: {loss_percentage}%")
        else:
            self.issues_found.append("Could not ping Shopify.com")
            self.recommendations.append(
                "Check network connectivity and DNS resolution."
            )
            print("WARNING: Could not ping Shopify.com")
        
        # Check recent API calls in logs
        api_calls = self.execute_command(f"grep -i 'shopify.*api' {self.log_file_path} | tail -20")
        if api_calls:
            results['recent_api_calls'] = api_calls
            
            # Check for API errors
            api_errors = len(re.findall(r'error.*api|api.*error', api_calls, re.IGNORECASE))
            if api_errors > 0:
                self.issues_found.append(f"Shopify API errors detected: {api_errors}")
                self.recommendations.append(
                    "Check Shopify API credentials and rate limits."
                )
                print(f"WARNING: Found {api_errors} Shopify API errors in recent logs")
        
        self.results['shopify_connectivity'] = results
        return results

    def run_analysis(self):
        """Run all checks and compile results."""
        if not self.connect():
            return False
        
        try:
            # Run all checks
            self.check_webhook_service_status()
            self.check_webhook_logs()
            self.check_webhook_queue()
            self.check_mongodb_status()
            self.check_shopify_connectivity()
            
            # Generate summary
            self.generate_summary()
            
            return True
        except Exception as e:
            print(f"Error during analysis: {str(e)}")
            return False
        finally:
            self.disconnect()

    def generate_summary(self):
        """Generate a summary of findings and recommendations."""
        print("\n" + "="*80)
        print("SHOPIFY WEBHOOK MONITORING SUMMARY")
        print("="*80)
        
        # Print server info
        print(f"\nServer: {self.hostname}")
        print(f"Analysis Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Log File: {self.log_file_path}")
        
        # Print issues found
        if self.issues_found:
            print("\n" + "="*80)
            print("ISSUES DETECTED")
            print("="*80)
            
            for i, issue in enumerate(self.issues_found, 1):
                print(f"{i}. {issue}")
        else:
            print("\nNo significant issues detected. Webhook system appears to be functioning normally.")
        
        # Print recommendations
        if self.recommendations:
            print("\n" + "="*80)
            print("RECOMMENDATIONS")
            print("="*80)
            
            # Remove duplicate recommendations
            unique_recommendations = list(set(self.recommendations))
            
            for i, recommendation in enumerate(unique_recommendations, 1):
                print(f"{i}. {recommendation}")
        
        print("\n" + "="*80)
        print("END OF ANALYSIS")
        print("="*80)

def main():
    """Main function to run the webhook monitor."""
    # Server credentials
    hostname = "*************"
    username = "ubuntu"
    password = "Reggie2805"
    port = 22
    
    print("Shopify Webhook Monitoring")
    print("==========================")
    print(f"Target Server: {hostname}")
    print(f"Username: {username}")
    print("Starting analysis...")
    
    # Create monitor instance
    monitor = ShopifyWebhookMonitor(
        hostname=hostname,
        username=username,
        password=password,
        port=port
    )
    
    # Run the analysis
    success = monitor.run_analysis()
    
    if not success:
        print("Analysis failed. Please check the error messages above.")
        sys.exit(1)
    
    print("\nAnalysis completed successfully.")

if __name__ == "__main__":
    main()
