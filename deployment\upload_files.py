#!/usr/bin/env python3
"""
File Upload Script

This script uses paramiko's SFTP functionality to upload files to the remote server.
It's designed to help upload website files to the correct directory.
"""

import paramiko
import os
import sys
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("deployment/file_upload.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("FileUploader")

# Server connection details
SERVER_IP = "***************"
USERNAME = "ubuntu"
PASSWORD = "Reggie2805!"
PORT = 22
REMOTE_DIR = "/var/www/enterprise.tcgsync.com"

def upload_file(sftp, local_path, remote_path):
    """Upload a single file to the remote server."""
    # Ensure remote path uses forward slashes
    remote_path = remote_path.replace('\\', '/')
    
    logger.info(f"Uploading {local_path} to {remote_path}")
    try:
        # Ensure the directory exists
        dirname = os.path.dirname(remote_path)
        try:
            sftp.stat(dirname)
        except FileNotFoundError:
            logger.info(f"Creating directory: {dirname}")
            sftp.mkdir(dirname)
        
        # Upload the file directly
        sftp.put(local_path, remote_path)
        logger.info(f"Successfully uploaded to: {remote_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to upload {local_path}: {str(e)}")
        return False

def upload_directory(sftp, local_dir, remote_dir):
    """Upload a directory and its contents recursively to the remote server."""
    # Ensure remote path uses forward slashes
    remote_dir = remote_dir.replace('\\', '/')
    
    logger.info(f"Uploading directory {local_dir} to {remote_dir}")
    
    # Ensure the remote directory exists
    try:
        sftp.stat(remote_dir)
    except FileNotFoundError:
        logger.info(f"Creating directory: {remote_dir}")
        sftp.mkdir(remote_dir)
    
    # Upload all files and subdirectories
    for item in os.listdir(local_dir):
        local_path = os.path.join(local_dir, item)
        remote_path = os.path.join(remote_dir, item).replace('\\', '/')
        
        if os.path.isfile(local_path):
            upload_file(sftp, local_path, remote_path)
        elif os.path.isdir(local_path):
            upload_directory(sftp, local_path, remote_path)

# Remove the execute_command function as it's no longer needed

def main():
    """Main function to upload files to the server."""
    parser = argparse.ArgumentParser(description='Upload files to the remote server.')
    parser.add_argument('path', help='Local file or directory to upload')
    parser.add_argument('--remote-dir', default=REMOTE_DIR, 
                        help=f'Remote directory to upload to (default: {REMOTE_DIR})')
    args = parser.parse_args()
    
    local_path = args.path
    remote_dir = args.remote_dir.replace('\\', '/')
    
    if not os.path.exists(local_path):
        logger.error(f"Local path {local_path} does not exist")
        return 1
    
    start_time = datetime.now()
    logger.info(f"Upload started at {start_time}")
    logger.info(f"Connecting to {SERVER_IP}:{PORT} as {USERNAME}")
    
    # Create SSH client
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        # Connect to the server
        ssh.connect(SERVER_IP, port=PORT, username=USERNAME, password=PASSWORD)
        logger.info("Successfully connected to the server")
        
        # Open SFTP session
        sftp = ssh.open_sftp()
        
        # Upload files
        if os.path.isfile(local_path):
            remote_path = os.path.join(remote_dir, os.path.basename(local_path))
            upload_file(sftp, local_path, remote_path)
        elif os.path.isdir(local_path):
            if local_path.endswith('/') or local_path.endswith('\\'):
                local_path = local_path[:-1]
            dir_name = os.path.basename(local_path)
            if dir_name:
                remote_target = os.path.join(remote_dir, dir_name)
            else:
                remote_target = remote_dir
            upload_directory(sftp, local_path, remote_target)
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        return 1
    finally:
        # Close the connection
        if 'sftp' in locals():
            sftp.close()
        ssh.close()
        logger.info("SSH connection closed")
    
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"Upload completed at {end_time}")
    logger.info(f"Total duration: {duration}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
