#!/usr/bin/env python3
"""
Fix Gunicorn Startup Issues
This script diagnoses and fixes specific gunicorn startup issues by checking logs,
fixing paths, and creating a more robust configuration.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def fix_gunicorn_startup():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to fix gunicorn startup issues...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("🔍 DIAGNOSING GUNICORN STARTUP ISSUES")
        print("="*70)
        
        # 1. Check detailed logs to find the specific error
        print("1. 📋 Checking detailed logs...")
        
        # Stop any running gunicorn processes
        execute_command(ssh_client, "systemctl stop gunicorn")
        execute_command(ssh_client, "pkill -9 -f gunicorn")
        time.sleep(2)
        
        # Check systemd journal logs
        output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -n 50")
        print("Recent systemd logs:")
        print(output[:500] + "..." if len(output) > 500 else output)
        
        # Check gunicorn error log
        output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log")
        if output:
            print("\nGunicorn error log:")
            print(output[:500] + "..." if len(output) > 500 else output)
        
        # 2. Check if wsgi.py exists and is correct
        print("\n2. 🔍 Checking WSGI file...")
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/wsgi.py")
        
        if "No such file" in error:
            print("⚠️ wsgi.py file not found, checking for alternatives...")
            
            # Look for app.py or similar files
            output, error, code = execute_command(ssh_client, "find /var/www/html -maxdepth 1 -name '*.py' | grep -E 'app|main|wsgi|server'")
            
            if output:
                print(f"Found potential app files: {output}")
                
                # Check if app.py exists
                output, error, code = execute_command(ssh_client, "ls -la /var/www/html/app.py")
                if "No such file" not in error:
                    print("Found app.py, creating wsgi.py that imports from it")
                    
                    wsgi_content = '''
# wsgi.py - Gunicorn entry point
from app import app

if __name__ == "__main__":
    app.run()
'''
                    wsgi_command = f'cat > /var/www/html/wsgi.py << \'EOF\'\n{wsgi_content}\nEOF'
                    execute_command(ssh_client, wsgi_command)
                    execute_command(ssh_client, "chown www-data:www-data /var/www/html/wsgi.py")
                    execute_command(ssh_client, "chmod 644 /var/www/html/wsgi.py")
                    print("✅ Created wsgi.py file")
                
                # Check if main.py exists
                output, error, code = execute_command(ssh_client, "ls -la /var/www/html/main.py")
                if "No such file" not in error:
                    print("Found main.py, creating wsgi.py that imports from it")
                    
                    wsgi_content = '''
# wsgi.py - Gunicorn entry point
from main import app

if __name__ == "__main__":
    app.run()
'''
                    wsgi_command = f'cat > /var/www/html/wsgi.py << \'EOF\'\n{wsgi_content}\nEOF'
                    execute_command(ssh_client, wsgi_command)
                    execute_command(ssh_client, "chown www-data:www-data /var/www/html/wsgi.py")
                    execute_command(ssh_client, "chmod 644 /var/www/html/wsgi.py")
                    print("✅ Created wsgi.py file")
            else:
                print("No potential app files found in root directory, checking subdirectories...")
                
                # Look for Flask app in subdirectories
                output, error, code = execute_command(ssh_client, "find /var/www/html -name 'app.py' | head -1")
                
                if output:
                    app_dir = output.rsplit('/', 1)[0]
                    print(f"Found app.py in {app_dir}")
                    
                    # Create a wsgi.py that adds the directory to path
                    app_rel_path = app_dir.replace('/var/www/html/', '')
                    
                    wsgi_content = f'''
# wsgi.py - Gunicorn entry point
import sys
import os

# Add the app directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '{app_rel_path}'))

from app import app

if __name__ == "__main__":
    app.run()
'''
                    wsgi_command = f'cat > /var/www/html/wsgi.py << \'EOF\'\n{wsgi_content}\nEOF'
                    execute_command(ssh_client, wsgi_command)
                    execute_command(ssh_client, "chown www-data:www-data /var/www/html/wsgi.py")
                    execute_command(ssh_client, "chmod 644 /var/www/html/wsgi.py")
                    print("✅ Created wsgi.py file that imports from subdirectory")
        else:
            print("✅ wsgi.py file exists")
            
            # Check wsgi.py content
            output, error, code = execute_command(ssh_client, "cat /var/www/html/wsgi.py")
            print("wsgi.py content:")
            print(output[:300] + "..." if len(output) > 300 else output)
        
        # 3. Create a robust gunicorn configuration
        print("\n3. ⚡ Creating robust gunicorn configuration...")
        
        robust_config = '''
# Robust Gunicorn Configuration
import multiprocessing

# Worker settings - conservative for stability
workers = 4
worker_class = "sync"  # Basic sync workers for stability
timeout = 120
keepalive = 5

# Logging - more verbose for debugging
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "debug"  # More verbose logging for debugging
capture_output = True
enable_stdio_inheritance = True

# Binding
bind = "127.0.0.1:8000"

# Prevent startup timeouts
graceful_timeout = 60
'''
        
        config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{robust_config}\nEOF'
        output, error, code = execute_command(ssh_client, config_command)
        
        if code == 0:
            print("✅ Created robust gunicorn configuration")
            
            # Set permissions
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
            execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
        else:
            print(f"❌ Failed to create configuration: {error}")
        
        # 4. Update service file with more debugging options
        print("\n4. 📄 Creating improved service file...")
        
        service_content = '''[Unit]
Description=Gunicorn daemon for TCG Sync Application
After=network.target
Wants=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
Environment=PYTHONUNBUFFERED=1
ExecStart=/var/www/html/venv/bin/gunicorn --config gunicorn.conf.py wsgi:app
StandardOutput=journal
StandardError=journal
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
'''
        
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        output, error, code = execute_command(ssh_client, service_command)
        
        if code == 0:
            print("✅ Created improved service file")
        else:
            print(f"❌ Failed to create service file: {error}")
        
        # 5. Check Python environment and dependencies
        print("\n5. 🐍 Checking Python environment...")
        
        # Check if venv exists
        output, error, code = execute_command(ssh_client, "ls -la /var/www/html/venv/bin/python")
        
        if "No such file" in error:
            print("⚠️ Python virtual environment not found or incomplete")
            
            # Check if system Python is available
            output, error, code = execute_command(ssh_client, "which python3")
            
            if output:
                print(f"System Python found at {output}")
                
                # Update service file to use system Python
                system_python_path = output.strip()
                gunicorn_path = execute_command(ssh_client, "which gunicorn")[0].strip()
                
                if not gunicorn_path:
                    print("Installing gunicorn system-wide...")
                    execute_command(ssh_client, "pip3 install gunicorn")
                    gunicorn_path = execute_command(ssh_client, "which gunicorn")[0].strip()
                
                if gunicorn_path:
                    service_content = f'''[Unit]
Description=Gunicorn daemon for TCG Sync Application
After=network.target
Wants=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PYTHONUNBUFFERED=1
ExecStart={gunicorn_path} --config gunicorn.conf.py wsgi:app
StandardOutput=journal
StandardError=journal
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
'''
                    
                    service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
                    execute_command(ssh_client, service_command)
                    print("✅ Updated service file to use system Python")
        else:
            print("✅ Python virtual environment exists")
            
            # Check gunicorn in venv
            output, error, code = execute_command(ssh_client, "ls -la /var/www/html/venv/bin/gunicorn")
            
            if "No such file" in error:
                print("Installing gunicorn in virtual environment...")
                execute_command(ssh_client, "cd /var/www/html && source venv/bin/activate && pip install gunicorn")
                print("✅ Installed gunicorn in virtual environment")
        
        # 6. Try running gunicorn manually to see specific errors
        print("\n6. 🧪 Testing gunicorn manually...")
        
        # Run with debug output
        output, error, code = execute_command(ssh_client, "cd /var/www/html && /var/www/html/venv/bin/gunicorn --check-config --config gunicorn.conf.py wsgi:app")
        
        if code == 0:
            print("✅ Gunicorn configuration check passed")
        else:
            print(f"⚠️ Gunicorn configuration check failed: {error}")
            print("Trying to run gunicorn directly...")
            
            # Try running without config
            output, error, code = execute_command(ssh_client, "cd /var/www/html && /var/www/html/venv/bin/gunicorn --log-level debug wsgi:app")
            
            if "Error:" in output or "Error:" in error:
                print(f"⚠️ Error when running gunicorn directly: {output}\n{error}")
                
                # Check if it's an import error
                if "ImportError" in output or "ImportError" in error:
                    print("Detected ImportError, checking app structure...")
                    
                    # Look for Flask app structure
                    output, error, code = execute_command(ssh_client, "find /var/www/html -name '*.py' | xargs grep -l 'Flask(__name__)' | head -1")
                    
                    if output:
                        print(f"Found Flask app in {output}")
                        
                        # Extract directory and filename
                        app_path = output.strip()
                        app_dir = app_path.rsplit('/', 1)[0]
                        app_file = app_path.rsplit('/', 1)[1].replace('.py', '')
                        
                        # Create a new wsgi.py that correctly imports the app
                        app_rel_path = app_dir.replace('/var/www/html/', '')
                        
                        wsgi_content = f'''
# wsgi.py - Gunicorn entry point
import sys
import os

# Add the app directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '{app_rel_path}'))

from {app_file} import app

if __name__ == "__main__":
    app.run()
'''
                        wsgi_command = f'cat > /var/www/html/wsgi.py << \'EOF\'\n{wsgi_content}\nEOF'
                        execute_command(ssh_client, wsgi_command)
                        execute_command(ssh_client, "chown www-data:www-data /var/www/html/wsgi.py")
                        execute_command(ssh_client, "chmod 644 /var/www/html/wsgi.py")
                        print("✅ Created corrected wsgi.py file")
        
        # 7. Reload systemd and start service
        print("\n7. 🔄 Reloading systemd and starting service...")
        execute_command(ssh_client, "systemctl daemon-reload")
        execute_command(ssh_client, "systemctl enable gunicorn")
        execute_command(ssh_client, "systemctl start gunicorn")
        time.sleep(5)
        
        # Check if service is running
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running!")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Test response
            output, error, code = execute_command(ssh_client, "curl -s -w 'Response time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
            
            if code == 0:
                print(f"✅ Application response: {output}")
                
                # Now that we have a stable base, apply optimizations
                print("\n8. ⚡ Applying performance optimizations...")
                
                # Get CPU count
                output, error, code = execute_command(ssh_client, "nproc")
                cpu_count = int(output.strip()) if output.strip().isdigit() else 4
                
                # Calculate optimal workers (conservative)
                optimal_workers = cpu_count * 2
                optimal_workers = max(optimal_workers, 4)  # At least 4 workers
                
                optimized_config = f'''
# Optimized Gunicorn Configuration
workers = {optimal_workers}
worker_class = "sync"  # Stick with sync for stability
timeout = 120
keepalive = 5
max_requests = 1000
max_requests_jitter = 200
bind = "127.0.0.1:8000"
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"  # Less verbose logging for production
'''
                
                config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{optimized_config}\nEOF'
                execute_command(ssh_client, config_command)
                execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
                execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
                
                print("✅ Applied performance optimizations")
                
                # Restart service
                execute_command(ssh_client, "systemctl restart gunicorn")
                time.sleep(5)
                
                # Final check
                output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
                
                if "active (running)" in output:
                    print("✅ Gunicorn service is running with optimized configuration!")
                    
                    # Check worker count
                    output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
                    worker_count = output.strip()
                    print(f"✅ Running with {worker_count} workers")
                    
                    # Test response time
                    output, error, code = execute_command(ssh_client, "curl -s -w 'Response time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
                    
                    if code == 0:
                        print(f"✅ Final application response: {output}")
                else:
                    print("❌ Service failed to start with optimized configuration")
                    print("Reverting to basic configuration...")
                    
                    execute_command(ssh_client, f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{robust_config}\nEOF')
                    execute_command(ssh_client, "systemctl restart gunicorn")
            else:
                print(f"❌ Failed to test response: {error}")
        else:
            print("❌ Service failed to start")
            print("Status output:")
            print(output[:300])
            
            # Check logs
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -20")
            print("Recent logs:")
            print(output)
        
        # 8. Final verification
        print("\n" + "="*70)
        print("✅ GUNICORN STARTUP FIX COMPLETE!")
        print("="*70)
        print("The following has been done:")
        print("1. ✅ Diagnosed specific startup issues")
        print("2. ✅ Fixed WSGI configuration")
        print("3. ✅ Created a robust gunicorn configuration")
        print("4. ✅ Updated service file with better debugging")
        print("5. ✅ Verified Python environment and dependencies")
        print("6. ✅ Applied performance optimizations if stable")
        
        print("\nTo manage the service, use these commands:")
        print("- Check status: systemctl status gunicorn")
        print("- Restart service: systemctl restart gunicorn")
        print("- View logs: journalctl -u gunicorn -f")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    fix_gunicorn_startup()
