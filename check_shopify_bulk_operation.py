import requests
import json
import time
from datetime import datetime

# Shopify credentials
SHOPIFY_STORE_NAME = "n5kwj8-d8"
SHOPIFY_ACCESS_TOKEN = "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"
API_VERSION = "2023-10"

# Bulk operation ID to check
BULK_OPERATION_ID = "gid://shopify/BulkOperation/5172241662137"

def make_shopify_request(query, variables=None):
    """Make a GraphQL request to Shopify Admin API"""
    url = f"https://{SHOPIFY_STORE_NAME}.myshopify.com/admin/api/{API_VERSION}/graphql.json"
    
    headers = {
        "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    payload = {
        "query": query
    }
    
    if variables:
        payload["variables"] = variables
    
    response = requests.post(url, headers=headers, json=payload)
    
    print(f"Request URL: {url}")
    print(f"Response Status: {response.status_code}")
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_bulk_operation_status():
    """Get the status and details of the bulk operation"""
    query = """
    query getBulkOperation($id: ID!) {
        node(id: $id) {
            ... on BulkOperation {
                id
                status
                errorCode
                createdAt
                completedAt
                objectCount
                fileSize
                url
                partialDataUrl
                query
                type
            }
        }
    }
    """
    
    variables = {
        "id": BULK_OPERATION_ID
    }
    
    print("=== QUERYING BULK OPERATION STATUS ===")
    result = make_shopify_request(query, variables)
    
    if result and 'data' in result and 'node' in result['data']:
        bulk_op = result['data']['node']
        
        print("\n=== BULK OPERATION DETAILS ===")
        print(f"ID: {bulk_op.get('id')}")
        print(f"Status: {bulk_op.get('status')}")
        print(f"Error Code: {bulk_op.get('errorCode')}")
        print(f"Type: {bulk_op.get('type')}")
        print(f"Created At: {bulk_op.get('createdAt')}")
        print(f"Completed At: {bulk_op.get('completedAt')}")
        print(f"Object Count: {bulk_op.get('objectCount')}")
        print(f"File Size: {bulk_op.get('fileSize')}")
        print(f"Query: {bulk_op.get('query')}")
        
        # Check if there's a results URL
        if bulk_op.get('url'):
            print(f"Results URL: {bulk_op.get('url')}")
            return bulk_op.get('url')
        elif bulk_op.get('partialDataUrl'):
            print(f"Partial Data URL: {bulk_op.get('partialDataUrl')}")
            return bulk_op.get('partialDataUrl')
        else:
            print("No results URL available")
            return None
    else:
        print("Failed to get bulk operation details")
        if result:
            print(f"Full response: {json.dumps(result, indent=2)}")
        return None

def download_and_analyze_results(results_url):
    """Download and analyze the bulk operation results"""
    if not results_url:
        print("No results URL provided")
        return
    
    print(f"\n=== DOWNLOADING RESULTS FROM: {results_url} ===")
    
    try:
        response = requests.get(results_url)
        
        if response.status_code == 200:
            content = response.text
            print(f"Downloaded {len(content)} characters of data")
            
            # Split into lines and analyze
            lines = content.strip().split('\n')
            print(f"Total lines: {len(lines)}")
            
            print("\n=== ANALYZING RESULTS ===")
            
            # Look for errors in the results
            error_lines = []
            success_lines = []
            
            for i, line in enumerate(lines):
                try:
                    if line.strip():  # Skip empty lines
                        data = json.loads(line)
                        
                        # Check for errors in the line
                        if 'errors' in data and data['errors']:
                            error_lines.append((i + 1, data))
                            print(f"\nERROR on line {i + 1}:")
                            print(f"Data: {json.dumps(data, indent=2)}")
                        else:
                            success_lines.append((i + 1, data))
                            
                except json.JSONDecodeError as e:
                    print(f"JSON decode error on line {i + 1}: {e}")
                    print(f"Line content: {line}")
            
            print(f"\n=== SUMMARY ===")
            print(f"Total lines processed: {len(lines)}")
            print(f"Successful operations: {len(success_lines)}")
            print(f"Failed operations: {len(error_lines)}")
            
            if error_lines:
                print(f"\n=== DETAILED ERROR ANALYSIS ===")
                for line_num, data in error_lines:
                    print(f"\nLine {line_num} Error Details:")
                    print(f"Full data: {json.dumps(data, indent=2)}")
                    
                    if 'errors' in data:
                        for error in data['errors']:
                            print(f"  Error: {error}")
            
            # Show first few successful operations for reference
            if success_lines:
                print(f"\n=== SAMPLE SUCCESSFUL OPERATIONS (first 3) ===")
                for i, (line_num, data) in enumerate(success_lines[:3]):
                    print(f"\nLine {line_num} Success:")
                    print(f"Data: {json.dumps(data, indent=2)}")
            
            # Save full results to file for detailed analysis
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"bulk_operation_results_{timestamp}.jsonl"
            
            with open(filename, 'w') as f:
                f.write(content)
            
            print(f"\n=== FULL RESULTS SAVED TO: {filename} ===")
            
        else:
            print(f"Failed to download results: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error downloading/analyzing results: {e}")

def get_current_bulk_operations():
    """Get all current bulk operations to see what's running"""
    query = """
    query {
        currentBulkOperation {
            id
            status
            errorCode
            createdAt
            completedAt
            objectCount
            fileSize
            url
            partialDataUrl
            query
            type
        }
    }
    """
    
    print("\n=== CHECKING CURRENT BULK OPERATIONS ===")
    result = make_shopify_request(query)
    
    if result and 'data' in result:
        current_op = result['data'].get('currentBulkOperation')
        if current_op:
            print("Current bulk operation found:")
            print(json.dumps(current_op, indent=2))
        else:
            print("No current bulk operation running")
    else:
        print("Failed to get current bulk operations")
        if result:
            print(f"Response: {json.dumps(result, indent=2)}")

def main():
    print("=== SHOPIFY BULK OPERATION CHECKER ===")
    print(f"Store: {SHOPIFY_STORE_NAME}")
    print(f"Checking operation: {BULK_OPERATION_ID}")
    print(f"Timestamp: {datetime.now()}")
    
    # First check current operations
    get_current_bulk_operations()
    
    # Then check the specific operation
    results_url = get_bulk_operation_status()
    
    # Download and analyze results if available
    if results_url:
        download_and_analyze_results(results_url)
    else:
        print("\nNo results available to download. The operation may still be running or may have failed without producing results.")

if __name__ == "__main__":
    main()
