/* Loading overlay for repricing */
.repricing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.repricing-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
}

.repricing-overlay .loading-text {
    color: white;
    font-size: 1.2rem;
    margin-top: 1rem;
}

.variant-group {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}
.variant-group h5 {
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 8px;
    color: #17a2b8;
}
.variant-group table {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    overflow: hidden;
}
.variant-group th {
    background-color: rgba(0, 123, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
    font-weight: 500;
}
.variant-group td {
    vertical-align: middle;
    border-color: rgba(255, 255, 255, 0.05);
}
.variant-group tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}
.variant-group input[type="number"] {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}
.variant-group input[type="number"]:focus {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* SKU Info Styling - Improved */
.sku-info {
    background-color: rgba(33, 37, 41, 0.6);
    border-radius: 6px;
    padding: 8px 10px;
    margin: 5px 0;
    font-size: 0.85rem;
    border-left: 3px solid #007bff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.sku-info:hover {
    background-color: rgba(33, 37, 41, 0.8);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sku-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 5px;
    margin-bottom: 6px;
}

.sku-section-title {
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #adb5bd;
}

.sku-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sku-item {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.sku-label {
    font-weight: bold;
    margin-right: 5px;
    color: #adb5bd;
    min-width: 60px;
}

.sku-value {
    color: #17a2b8;
    font-family: monospace;
    font-size: 0.9rem;
    background-color: rgba(23, 162, 184, 0.1);
    padding: 1px 4px;
    border-radius: 3px;
}

.sku-value.matched {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 2px solid #28a745;
    padding-left: 3px;
}

.sku-catalog-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

.catalog-sku {
    margin-right: 3px;
}
/* TCGPlayer Prices Table Styling */
#tcgplayerPrices .table {
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
}

#tcgplayerPrices thead {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

#tcgplayerPrices th {
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    padding: 1rem;
}

#tcgplayerPrices td {
    padding: 0.75rem;
    border-color: rgba(255, 255, 255, 0.1);
    font-size: 0.95rem;
}

#tcgplayerPrices tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transition: background-color 0.2s ease;
}
