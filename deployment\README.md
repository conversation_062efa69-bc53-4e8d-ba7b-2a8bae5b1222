# Server Updater Script

This Python script uses Paramiko to connect to a remote Ubuntu server and perform system updates. It's designed for initial setup of a brand new server.

## Prerequisites

Before using this script, you need to have Python 3 installed along with the Paramiko library:

```bash
pip install paramiko
```

## Configuration

The script is pre-configured with the following connection details:

- Server IP: ***************
- Username: ubuntu
- Password: Reggie2805!
- SSH Port: 22 (default)

If you need to modify these details, edit the variables at the top of the `server_updater.py` file.

## What the Script Does

When executed, the script will:

1. Connect to the remote server using SSH
2. Update the package lists (`apt-get update`)
3. Upgrade all installed packages (`apt-get upgrade`)
4. Install some common useful packages (htop, net-tools, curl, wget, unzip)
5. Clean up unnecessary packages (`apt-get autoremove` and `apt-get autoclean`)
6. Check system status (disk space, memory usage, uptime)
7. Install Python 3.12:
   - Add the deadsnakes PPA repository
   - Install Python 3.12 and related packages (python3.12-venv, python3.12-dev)
   - Install pip for Python 3.12
   - Verify the installation
8. Set up SSL for the domain enterprise.tcgsync.com:
   - Install Nginx web server
   - Install Certbot (Let's Encrypt client) and its Nginx plugin
   - Create a website directory at `/var/www/enterprise.tcgsync.com`
   - Create a basic Nginx configuration for the domain
   - Obtain an SSL certificate using Let's Encrypt
   - Configure automatic certificate renewal

All actions are logged both to the console and to a log file (`deployment/server_update.log`).

## Website Files

The script creates a website directory at `/var/www/enterprise.tcgsync.com` on the server. This is where you should upload your website files. A sample index.html file is created as a placeholder.

### Uploading Files

To upload your website files to the server, you can use the included file upload script:

#### Using Helper Scripts

For convenience, helper scripts are provided:

- **On Windows**:
  ```
  deployment\upload_files.bat
  ```

- **On Linux/Mac**: 
  ```bash
  chmod +x deployment/upload_files.sh
  ./deployment/upload_files.sh
  ```

These scripts will prompt you for the path to the file or directory you want to upload.

#### Manual Execution

Alternatively, you can run the upload script manually:

```bash
python deployment/upload_files.py path/to/your/files
```

The script will:
1. Connect to the server using SSH
2. Upload the specified file or directory to `/var/www/enterprise.tcgsync.com`
3. Set proper permissions on the uploaded files

All upload actions are logged to `deployment/file_upload.log`.

## How to Run

### Using Helper Scripts

For convenience, helper scripts are provided:

- **On Linux/Mac**: 
  ```bash
  chmod +x deployment/run_updater.sh
  ./deployment/run_updater.sh
  ```

- **On Windows**:
  ```
  deployment\run_updater.bat
  ```

These scripts will automatically install the required dependencies and run the updater.

### Manual Execution

Alternatively, you can run the script manually:

1. Install dependencies:
   ```bash
   pip install -r deployment/requirements.txt
   ```

2. Run the script:
   ```bash
   python deployment/server_updater.py
   ```

## Security Considerations

This script contains hardcoded credentials, which is not recommended for production use. For better security:

1. Consider using SSH key-based authentication instead of passwords
2. Store sensitive information in environment variables or a secure configuration file
3. Limit SSH access to specific IP addresses using firewall rules
4. Change default passwords immediately after initial setup

## Troubleshooting

If you encounter connection issues:

1. Verify the server is reachable (try pinging the IP address)
2. Confirm the SSH service is running on the server
3. Check that the credentials are correct
4. Ensure your local network allows outbound connections on port 22

For other issues, check the log file for detailed error messages.
