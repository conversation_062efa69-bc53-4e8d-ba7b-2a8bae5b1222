from mongoengine import Document, StringField, FloatField, ListField, DictField, DateTimeField, BooleanField
from datetime import datetime, timezone

class TrollAusPurchase(Document):
    """
    Model for storing items purchased by TrollAus that need to be added to inventory.
    This collection stores items purchased through the POS Buy tab that will later
    be pushed to Shopify inventory.
    """
    # Purchase information
    purchase_id = StringField(required=True)  # Unique ID for this purchase
    username = StringField(required=True, default="TrollAus")  # Should always be TrollAus
    customer_id = StringField(required=True)  # Shopify customer ID
    customer_name = StringField(required=True)  # Customer name for reference
    customer_email = StringField()  # Customer email for reference

    # Item details
    items = ListField(DictField(), required=True)  # List of purchased items with product details

    # Financial information
    total_credit = FloatField(required=True)  # Total credit amount

    # Status tracking
    processed = BooleanField(default=False)  # Whether this has been processed and added to inventory
    processed_date = DateTimeField()  # When it was processed

    # Timestamps
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'trollAusPurchases',
        'indexes': [
            'username',
            'customer_id',
            'processed',
            'created_at'
        ]
    }

class TrollBuy(Document):
    """
    Model for storing all POS buy orders in the trollbuys collection.
    This records all products purchased including their Shopify IDs, customer information,
    and payment method details.
    """
    # Order information
    order_id = StringField(required=True, unique=True)  # Unique order ID
    username = StringField(required=True)  # User who processed the buy order

    # Customer information
    customer_id = StringField()  # Shopify customer ID (can be empty for cash purchases)
    customer_name = StringField()  # Customer name
    customer_email = StringField()  # Customer email
    customer_shopify_id = StringField()  # Shopify customer ID for reference

    # Products purchased
    products = ListField(DictField(), required=True)  # List of products with details

    # Payment information
    payment_method = StringField(required=True)  # 'cash', 'credit', or 'combined'
    cash_amount = FloatField(default=0.0)  # Cash portion of payment
    credit_amount = FloatField(default=0.0)  # Credit portion of payment
    total_amount = FloatField(required=True)  # Total purchase amount

    # Store credit processing status
    store_credit_processed = BooleanField(default=False)  # Whether store credit was applied
    store_credit_success = BooleanField(default=False)  # Whether store credit application succeeded
    store_credit_error = StringField()  # Error message if store credit failed
    customer_credit_before = FloatField(default=0.0)  # Customer's credit balance before
    customer_credit_after = FloatField(default=0.0)  # Customer's credit balance after
    store_credit_processed_at = DateTimeField()  # When store credit was processed
    store_credit_refunded = BooleanField(default=False)  # Whether store credit was refunded/undone
    store_credit_refunded_at = DateTimeField()  # When store credit was refunded

    # Inventory update status
    inventory_processed = BooleanField(default=False)  # Whether inventory was updated
    inventory_success = BooleanField(default=False)  # Whether inventory update succeeded
    inventory_error = StringField()  # Error message if inventory update failed
    inventory_processed_at = DateTimeField()  # When inventory was processed

    # Timestamps
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    # Status tracking
    status = StringField(default='completed')  # Order status
    notes = StringField()  # Any additional notes

    meta = {
        'collection': 'trollbuys',
        'indexes': [
            'username',
            'customer_id',
            'customer_shopify_id',
            'payment_method',
            'created_at',
            'status'
        ]
    }

    def to_dict(self):
        """Convert the document to a dictionary"""
        return {
            "id": str(self.id),
            "order_id": self.order_id,
            "username": self.username,
            "customer_id": self.customer_id,
            "customer_name": self.customer_name,
            "customer_email": self.customer_email,
            "customer_shopify_id": self.customer_shopify_id,
            "products": self.products,
            "payment_method": self.payment_method,
            "cash_amount": self.cash_amount,
            "credit_amount": self.credit_amount,
            "total_amount": self.total_amount,
            "store_credit_processed": self.store_credit_processed,
            "store_credit_success": self.store_credit_success,
            "store_credit_error": self.store_credit_error,
            "customer_credit_before": self.customer_credit_before,
            "customer_credit_after": self.customer_credit_after,
            "store_credit_processed_at": self.store_credit_processed_at.isoformat() if self.store_credit_processed_at else None,
            "store_credit_refunded": self.store_credit_refunded,
            "store_credit_refunded_at": self.store_credit_refunded_at.isoformat() if self.store_credit_refunded_at else None,
            "inventory_processed": self.inventory_processed,
            "inventory_success": self.inventory_success,
            "inventory_error": self.inventory_error,
            "inventory_processed_at": self.inventory_processed_at.isoformat() if self.inventory_processed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "status": self.status,
            "notes": self.notes
        }

    def to_dict_trollaus_purchase(self):
        """Convert the TrollAusPurchase document to a dictionary"""
        return {
            "id": str(self.id),
            "purchase_id": self.purchase_id,
            "username": self.username,
            "customer_id": self.customer_id,
            "customer_name": self.customer_name,
            "customer_email": self.customer_email,
            "items": self.items,
            "total_credit": self.total_credit,
            "processed": self.processed,
            "processed_date": self.processed_date.isoformat() if self.processed_date else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
