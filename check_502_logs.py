#!/usr/bin/env python3
"""
502 Error Log Checker
Checks various log files for 502 Bad Gateway errors
"""

import paramiko
import sys
import re
from datetime import datetime

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def check_502_logs():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔍 Connecting to {hostname} to check 502 error logs...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*60)
        print("🚨 502 BAD GATEWAY ERROR LOG ANALYSIS")
        print("="*60)
        
        # Common log locations to check
        log_locations = [
            "/var/log/nginx/error.log",
            "/var/log/nginx/access.log", 
            "/var/log/nginx/trollaus_error.log",
            "/var/log/nginx/trollaus_access.log",
            "/var/www/html/logs/app/gunicorn-error.log",
            "/var/www/html/logs/app/gunicorn-access.log",
            "/var/log/gunicorn/error.log",
            "/var/log/gunicorn/access.log",
            "/var/log/syslog",
            "/var/log/messages"
        ]
        
        print("📋 1. Checking Nginx Error Logs for 502 errors...")
        for log_path in ["/var/log/nginx/error.log", "/var/log/nginx/trollaus_error.log"]:
            output, error, code = execute_command(ssh_client, f"ls -la {log_path}")
            if code == 0:
                print(f"\n✅ Found: {log_path}")
                
                # Check for 502 errors in last 100 lines
                output, error, code = execute_command(ssh_client, f"tail -100 {log_path} | grep -i '502\\|bad gateway\\|upstream'")
                if output:
                    print(f"🚨 502 errors found in {log_path}:")
                    print("-" * 50)
                    for line in output.split('\n')[:10]:  # Show first 10 matches
                        print(line)
                    print("-" * 50)
                else:
                    print(f"✅ No recent 502 errors in {log_path}")
            else:
                print(f"❌ Not found: {log_path}")
        
        print("\n📋 2. Checking Nginx Access Logs for 502 responses...")
        for log_path in ["/var/log/nginx/access.log", "/var/log/nginx/trollaus_access.log"]:
            output, error, code = execute_command(ssh_client, f"ls -la {log_path}")
            if code == 0:
                print(f"\n✅ Found: {log_path}")
                
                # Check for 502 status codes
                output, error, code = execute_command(ssh_client, f"tail -1000 {log_path} | grep ' 502 '")
                if output:
                    print(f"🚨 502 responses found in {log_path}:")
                    print("-" * 50)
                    lines = output.split('\n')
                    print(f"Total 502 errors in last 1000 lines: {len(lines)}")
                    for line in lines[-5:]:  # Show last 5
                        print(line)
                    print("-" * 50)
                else:
                    print(f"✅ No 502 responses in recent access log")
        
        print("\n📋 3. Checking Gunicorn Logs...")
        gunicorn_logs = [
            "/var/www/html/logs/app/gunicorn-error.log",
            "/var/www/html/logs/app/gunicorn-access.log",
            "/var/log/gunicorn/error.log"
        ]
        
        for log_path in gunicorn_logs:
            output, error, code = execute_command(ssh_client, f"ls -la {log_path}")
            if code == 0:
                print(f"\n✅ Found: {log_path}")
                
                # Check for errors that might cause 502
                output, error, code = execute_command(ssh_client, f"tail -50 {log_path} | grep -i 'error\\|exception\\|failed\\|timeout'")
                if output:
                    print(f"⚠️ Recent errors in {log_path}:")
                    print("-" * 50)
                    for line in output.split('\n')[-10:]:  # Show last 10
                        if line.strip():
                            print(line)
                    print("-" * 50)
                else:
                    print(f"✅ No recent errors in {log_path}")
        
        print("\n📋 4. Checking System Logs for 502-related issues...")
        output, error, code = execute_command(ssh_client, "journalctl -u nginx --since '1 hour ago' | grep -i '502\\|bad gateway\\|upstream'")
        if output:
            print("🚨 Nginx service logs show 502 issues:")
            print("-" * 50)
            print(output)
            print("-" * 50)
        else:
            print("✅ No 502 issues in nginx service logs")
        
        print("\n📋 5. Checking Current Nginx and Gunicorn Status...")
        
        # Check nginx status
        output, error, code = execute_command(ssh_client, "systemctl status nginx")
        if "active (running)" in output:
            print("✅ Nginx is running")
        else:
            print("❌ Nginx is not running properly")
            print(output)
        
        # Check gunicorn processes
        output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep")
        if output:
            worker_count = len(output.split('\n'))
            print(f"✅ Gunicorn is running with {worker_count} workers")
        else:
            print("❌ Gunicorn is not running!")
        
        # Check if nginx can connect to gunicorn
        output, error, code = execute_command(ssh_client, "curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/")
        if output == "200":
            print("✅ Gunicorn responding on port 8000")
        else:
            print(f"❌ Gunicorn not responding properly (status: {output})")
        
        print("\n📋 6. Checking Nginx Configuration...")
        output, error, code = execute_command(ssh_client, "nginx -t")
        if code == 0:
            print("✅ Nginx configuration is valid")
        else:
            print(f"❌ Nginx configuration error: {error}")
        
        # Check upstream configuration
        output, error, code = execute_command(ssh_client, "grep -r 'upstream\\|proxy_pass' /etc/nginx/sites-enabled/")
        if output:
            print("🔧 Nginx upstream configuration:")
            print("-" * 50)
            print(output)
            print("-" * 50)
        
        print("\n📋 7. Real-time 502 Error Monitoring Commands...")
        print("Use these commands to monitor 502 errors in real-time:")
        print()
        print("# Monitor nginx error log:")
        print("tail -f /var/log/nginx/error.log | grep -i '502\\|bad gateway'")
        print()
        print("# Monitor nginx access log for 502 responses:")
        print("tail -f /var/log/nginx/access.log | grep ' 502 '")
        print()
        print("# Monitor gunicorn errors:")
        print("tail -f /var/www/html/logs/app/gunicorn-error.log")
        print()
        print("# Check if gunicorn is responding:")
        print("curl -I http://localhost:8000/")
        print()
        print("# Monitor system resources:")
        print("watch 'ps aux | grep gunicorn | grep -v grep'")
        
        print("\n📋 8. Quick 502 Troubleshooting Steps...")
        troubleshooting_steps = [
            "1. Check if gunicorn is running: ps aux | grep gunicorn",
            "2. Restart gunicorn if needed: systemctl restart gunicorn",
            "3. Check gunicorn logs: tail -50 /var/www/html/logs/app/gunicorn-error.log",
            "4. Test gunicorn directly: curl http://localhost:8000/",
            "5. Check nginx upstream config: grep -r proxy_pass /etc/nginx/sites-enabled/",
            "6. Restart nginx: systemctl restart nginx",
            "7. Check system resources: free -h && df -h",
            "8. Monitor in real-time: tail -f /var/log/nginx/error.log"
        ]
        
        for step in troubleshooting_steps:
            print(step)
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    check_502_logs()
