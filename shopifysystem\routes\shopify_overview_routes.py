from flask import Blueprint, jsonify, request, render_template
from pymongo import MongoClient
from flask_login import login_required, current_user
import logging
from datetime import datetime
import requests
from bson import ObjectId
from utils.json_encoder import JSONEncoder  # Ensure this import matches the location of JSONEncoder

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_dbname = 'test'
client = MongoClient(mongo_uri)
db = client[mongo_dbname]
shCustomers_collection = db['shCustomers']
shOrders_collection = db['shOrders']
shProducts_collection = db['shProducts']
shGiftCards_collection = db['shGiftCards']
users_collection = db['user']

logging.basicConfig(level=logging.DEBUG)

shopify_overview_bp = Blueprint('shopify_overview', __name__)

def calculate_outstanding_store_credit(username):
    gift_cards = shGiftCards_collection.find({'username': username})
    total_balance = sum(float(gift_card.get('balance', 0)) for gift_card in gift_cards)
    return total_balance

def convert_objectid(data):
    if isinstance(data, dict):
        return {key: str(value) if isinstance(value, ObjectId) else convert_objectid(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_objectid(item) for item in data]
    else:
        return data

@shopify_overview_bp.route('/shopify/overview', methods=['GET'])
@login_required
def shopify_overview():
    username = current_user.username
    num_customers = shCustomers_collection.count_documents({'username': username})
    num_orders = shOrders_collection.count_documents({'username': username})
    num_products = shProducts_collection.count_documents({'username': username})
    store_credit_outstanding = calculate_outstanding_store_credit(username)
    logging.debug(f"shopify_overview - username: {username}, num_customers: {num_customers}, num_orders: {num_orders}, num_products: {num_products}, store_credit_outstanding: {store_credit_outstanding}")
    return render_template('shopify_overview.html', num_customers=num_customers, num_orders=num_orders, num_products=num_products, store_credit_outstanding=store_credit_outstanding)

@shopify_overview_bp.route('/shopify/overview/search_customer', methods=['GET'])
@login_required
def search_customer():
    username = current_user.username
    search_email = request.args.get('email', '')

    query = {
        'username': username,
        'email': {'$regex': search_email, '$options': 'i'}
    }

    customer = shCustomers_collection.find_one(query, {'_id': 0, 'id': 1, 'first_name': 1, 'last_name': 1, 'email': 1})

    logging.debug(f"search_customer - query: {query}, customer: {customer}")

    if customer:
        customer = convert_objectid(customer)
        return jsonify(customer), 200
    else:
        return jsonify({"message": "Customer not found"}), 404

@shopify_overview_bp.route('/shopify/overview/check_gift_card', methods=['GET'])
@login_required
def check_gift_card():
    customer_id = request.args.get('customer_id')

    logging.debug(f"check_gift_card - customer_id: {customer_id}")

    # Check if a gift card already exists for the customer by both string and integer customer_id
    query = {
        '$or': [
            {'customer_id': str(customer_id)},
            {'customer_id': int(customer_id)}
        ]
    }

    existing_gift_card = shGiftCards_collection.find_one(query)

    logging.debug(f"check_gift_card - query: {query}, existing_gift_card: {existing_gift_card}")

    if existing_gift_card:
        # Convert ObjectId to string for JSON serialization
        existing_gift_card = convert_objectid(existing_gift_card)

        # Fetch the live balance from Shopify
        shopify_store_name, shopify_access_token, error = get_shopify_credentials()
        if error:
            return jsonify({"message": error}), 400

        shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{existing_gift_card['id']}.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        logging.debug(f"check_gift_card - Fetching live balance from Shopify: {shopify_api_url}")

        response = requests.get(shopify_api_url, headers=headers)

        logging.debug(f"check_gift_card - Shopify response status: {response.status_code}, data: {response.text}")

        if response.status_code == 200:
            live_gift_card = response.json()['gift_card']
            existing_gift_card['balance'] = live_gift_card['balance']
            existing_gift_card['last_updated'] = live_gift_card['updated_at']
        else:
            logging.debug(f"check_gift_card - Failed to fetch live balance, using stored balance")

        return jsonify({"message": "Gift card already exists for this customer", "gift_card": existing_gift_card, "live_balance": existing_gift_card['balance']}), 200
    else:
        return jsonify({"message": "No gift card found for this customer"}), 404

@shopify_overview_bp.route('/shopify/overview/create_gift_card', methods=['POST'])
@login_required
def create_gift_card():
    data = request.json
    initial_value = data.get('initialValue')
    note = data.get('note')
    customer_email = data.get('customerEmail')

    logging.debug(f"create_gift_card - customer_email: {customer_email}, initial_value: {initial_value}, note: {note}")

    if not initial_value or not customer_email:
        logging.debug(f"create_gift_card - Missing initial_value or customer_email")
        return jsonify({"message": "Initial value and customer email are required"}), 400

    # Find the customer by email within the logged-in user's records
    customer = shCustomers_collection.find_one({'email': customer_email, 'username': current_user.username})
    if not customer:
        logging.debug(f"create_gift_card - Customer not found for email: {customer_email}")
        return jsonify({"message": "Customer not found"}), 404

    logging.debug(f"create_gift_card - Customer found: {customer}")

    # Check if a gift card already exists for the customer by both string and integer customer_id
    customer_id = customer['id']
    query = {
        '$or': [
            {'customer_id': str(customer_id)},
            {'customer_id': int(customer_id)}
        ]
    }
    existing_gift_card = shGiftCards_collection.find_one(query)
    if existing_gift_card:
        logging.debug(f"create_gift_card - Gift card already exists for customer_id: {customer_id}")
        existing_gift_card = convert_objectid(existing_gift_card)
        return jsonify({"message": "Gift card already exists for this customer", "gift_card": existing_gift_card}), 409

    shopify_store_name, shopify_access_token, error = get_shopify_credentials()
    if error:
        return jsonify({"message": error}), 400
    
    user = users_collection.find_one({"username": current_user.username})
    shopify_store_currency = user.get('shopifyStoreCurrency', 'USD')  # Default to USD if not specified

    gift_card_data = {
        "gift_card": {
            "initial_value": initial_value,
            "currency": shopify_store_currency,
            "note": note
        }
    }

    shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    logging.debug(f"create_gift_card - Sending request to Shopify: {shopify_api_url} with data: {gift_card_data}")

    response = requests.post(shopify_api_url, headers=headers, json=gift_card_data)

    logging.debug(f"create_gift_card - Shopify response status: {response.status_code}, data: {response.text}")

    if response.status_code == 201:
        try:
            new_gift_card = response.json()['gift_card']
            new_gift_card['username'] = current_user.username
            new_gift_card['customer_id'] = str(customer_id)
            new_gift_card = convert_objectid(new_gift_card)
            shGiftCards_collection.insert_one(new_gift_card)

            # Update customer record with gift card details
            shCustomers_collection.update_one(
                {'id': customer_id},
                {'$set': {'gift_card_id': new_gift_card['id'], 'gift_card_initial_value': initial_value, 'gift_card_balance': new_gift_card['balance']}}
            )

            logging.debug(f"create_gift_card - Gift card created and associated with customer successfully, balance: {new_gift_card['balance']}")

            return jsonify({"message": "Gift card created and associated with customer successfully", "gift_card_balance": new_gift_card['balance']}), 201
        except Exception as e:
            logging.debug(f"create_gift_card - Error processing Shopify response: {str(e)}")
            return jsonify({"message": "Error processing Shopify response", "details": str(e)}), 500
    else:
        logging.debug(f"create_gift_card - Failed to create gift card, Shopify response: {response.text}")
        return jsonify({"message": "Failed to create gift card", "details": response.text}), 500

@shopify_overview_bp.route('/shopify/overview/update_gift_card_balance', methods=['POST'])
@login_required
def update_gift_card_balance():
    data = request.json
    customer_email = data.get('customerEmail')
    increase_balance = data.get('increaseBalance', 0)
    decrease_balance = data.get('decreaseBalance', 0)

    logging.debug(f"update_gift_card_balance - customer_email: {customer_email}, increase_balance: {increase_balance}, decrease_balance: {decrease_balance}")

    if not customer_email:
        logging.debug(f"update_gift_card_balance - Missing customer_email")
        return jsonify({"message": "Customer email is required"}), 400

    # Find the customer by email within the logged-in user's records
    customer = shCustomers_collection.find_one({'email': customer_email, 'username': current_user.username})
    if not customer:
        logging.debug(f"update_gift_card_balance - Customer not found for email: {customer_email}")
        return jsonify({"message": "Customer not found"}), 404

    logging.debug(f"update_gift_card_balance - Customer found: {customer}")

    # Check if a gift card exists for the customer by both string and integer customer_id
    customer_id = customer['id']
    query = {
        '$or': [
            {'customer_id': str(customer_id)},
            {'customer_id': int(customer_id)}
        ]
    }
    existing_gift_card = shGiftCards_collection.find_one(query)
    if not existing_gift_card:
        logging.debug(f"update_gift_card_balance - Gift card not found for customer_id: {customer_id}")
        return jsonify({"message": "Gift card not found for this customer"}), 404

    # Update the gift card balance on Shopify
    shopify_store_name, shopify_access_token, error = get_shopify_credentials()
    if error:
        return jsonify({"message": error}), 400

    adjustments = []
    if increase_balance:
        adjustments.append({
            "amount": float(increase_balance),
            "note": "Adding value to the gift card"
        })
    if decrease_balance:
        adjustments.append({
            "amount": -float(decrease_balance),
            "note": "Deducting value from the gift card"
        })

    for adjustment in adjustments:
        adjustment_data = {"adjustment": adjustment}

        shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{existing_gift_card['id']}/adjustments.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        logging.debug(f"update_gift_card_balance - Updating gift card balance on Shopify: {shopify_api_url} with data: {adjustment_data}")

        response = requests.post(shopify_api_url, headers=headers, json=adjustment_data)

        logging.debug(f"update_gift_card_balance - Shopify response status: {response.status_code}, data: {response.text}")

        if response.status_code != 201:
            return jsonify({"message": "Failed to update gift card balance", "details": response.text}), 500

    # Fetch the updated gift card balance from Shopify
    shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards/{existing_gift_card['id']}.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    response = requests.get(shopify_api_url, headers=headers)

    logging.debug(f"update_gift_card_balance - Fetching updated balance from Shopify: {shopify_api_url}")
    logging.debug(f"update_gift_card_balance - Shopify response status: {response.status_code}, data: {response.text}")

    if response.status_code == 200:
        updated_gift_card = response.json()['gift_card']
        # Update the gift card balance in MongoDB
        shGiftCards_collection.update_one(
            {'customer_id': customer_id},
            {'$set': {'balance': updated_gift_card['balance']}}
        )

        logging.debug(f"update_gift_card_balance - Gift card balance updated to: {updated_gift_card['balance']}")

        return jsonify({"message": "Gift card balance updated successfully", "updated_balance": updated_gift_card['balance']}), 200
    else:
        return jsonify({"message": "Failed to fetch updated gift card balance", "details": response.text}), 500

def get_shopify_credentials():
    try:
        user = users_collection.find_one({"username": current_user.username})
        if not user:
            logging.error(f"Database error: User document not found for {current_user.username}")
            return None, None, "Database error: Please try again"
            
        shopify_store_name = user.get('shopifyStoreName')
        shopify_access_token = user.get('shopifyAccessToken')
        
        missing = []
        if not shopify_store_name:
            missing.append("store name")
        if not shopify_access_token:
            missing.append("access token")
            
        if missing:
            missing_str = " and ".join(missing)
            logging.debug(f"Missing Shopify {missing_str} for user: {current_user.username}")
            return None, None, f"Please configure your Shopify {missing_str} in profile settings"
            
        return shopify_store_name, shopify_access_token, None
    except Exception as e:
        logging.error(f"Error getting Shopify credentials: {str(e)}")
        return None, None, "Error accessing Shopify credentials"

@shopify_overview_bp.route('/shopify/overview/create_customer', methods=['POST'])
@login_required
def create_customer():
    data = request.json
    customer_first_name = data.get('firstName')
    customer_last_name = data.get('lastName')
    customer_email = data.get('email')
    customer_notes = data.get('notes')

    logging.debug(f"create_customer - customer_first_name: {customer_first_name}, customer_last_name: {customer_last_name}, customer_email: {customer_email}, customer_notes: {customer_notes}")

    if not customer_first_name or not customer_email:
        logging.debug(f"create_customer - Missing first name or email")
        return jsonify({"message": "First name and email are required"}), 400

    shopify_store_name, shopify_access_token, error = get_shopify_credentials()
    if error:
        return jsonify({"message": error}), 400

    customer_data = {
        "customer": {
            "first_name": customer_first_name,
            "last_name": customer_last_name,
            "email": customer_email,
            "note": customer_notes,
            "verified_email": True,
            "accepts_marketing": False
        }
    }

    shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    logging.debug(f"create_customer - Sending request to Shopify: {shopify_api_url} with data: {customer_data}")

    response = requests.post(shopify_api_url, headers=headers, json=customer_data)

    logging.debug(f"create_customer - Shopify response status: {response.status_code}, data: {response.text}")

    try:
        response_data = response.json()
        if response.status_code == 201 and 'customer' in response_data:
            customer_data = response_data['customer']
            # Check if customer already exists
            existing_customer = shCustomers_collection.find_one({
                'username': current_user.username,
                '$or': [
                    {'id': str(customer_data['id'])},
                    {'id': customer_data['id']}
                ]
            })

            if existing_customer:
                # Update existing customer
                customer_doc = {
                    "username": current_user.username,
                    "id": str(customer_data['id']),  # Ensure ID is string
                    "first_name": customer_data.get('first_name', ''),
                    "last_name": customer_data.get('last_name', ''),
                    "email": customer_data.get('email', ''),
                    "note": customer_data.get('note', ''),
                    "lastUpdated": datetime.utcnow().isoformat(),
                    "addresses": customer_data.get('addresses', []),
                    "admin_graphql_api_id": customer_data.get('admin_graphql_api_id', ''),
                    "currency": customer_data.get('currency', 'GBP'),
                    "email_marketing_consent": customer_data.get('email_marketing_consent', {}),
                    "last_order_id": customer_data.get('last_order_id'),
                    "last_order_name": customer_data.get('last_order_name'),
                    "multipass_identifier": customer_data.get('multipass_identifier'),
                    "orders_count": customer_data.get('orders_count', 0),
                    "sms_marketing_consent": customer_data.get('sms_marketing_consent'),
                    "state": customer_data.get('state', 'disabled'),
                    "tags": customer_data.get('tags', ''),
                    "tax_exempt": customer_data.get('tax_exempt', False),
                    "tax_exemptions": customer_data.get('tax_exemptions', []),
                    "total_spent": customer_data.get('total_spent', '0.00'),
                    "updated_at": customer_data.get('updated_at'),
                    "verified_email": customer_data.get('verified_email', True)
                }
                
                customer_doc = convert_objectid(customer_doc)
                shCustomers_collection.update_one(
                    {'username': current_user.username, 'id': str(customer_data['id'])},
                    {'$set': customer_doc}
                )
            else:
                # Create new customer
                customer_doc = {
                    "username": current_user.username,
                    "id": str(customer_data['id']),  # Ensure ID is string
                    "first_name": customer_data.get('first_name', ''),
                    "last_name": customer_data.get('last_name', ''),
                    "email": customer_data.get('email', ''),
                    "note": customer_data.get('note', ''),
                    "created_at": datetime.utcnow(),
                    "lastUpdated": datetime.utcnow().isoformat(),
                    "addresses": customer_data.get('addresses', []),
                    "admin_graphql_api_id": customer_data.get('admin_graphql_api_id', ''),
                    "currency": customer_data.get('currency', 'GBP'),
                    "email_marketing_consent": customer_data.get('email_marketing_consent', {}),
                    "last_order_id": customer_data.get('last_order_id'),
                    "last_order_name": customer_data.get('last_order_name'),
                    "multipass_identifier": customer_data.get('multipass_identifier'),
                    "orders_count": customer_data.get('orders_count', 0),
                    "sms_marketing_consent": customer_data.get('sms_marketing_consent'),
                    "state": customer_data.get('state', 'disabled'),
                    "tags": customer_data.get('tags', ''),
                    "tax_exempt": customer_data.get('tax_exempt', False),
                    "tax_exemptions": customer_data.get('tax_exemptions', []),
                    "total_spent": customer_data.get('total_spent', '0.00'),
                    "updated_at": customer_data.get('updated_at'),
                    "verified_email": customer_data.get('verified_email', True)
                }
                
                customer_doc = convert_objectid(customer_doc)
                shCustomers_collection.insert_one(customer_doc)

            logging.debug(f"create_customer - Customer created successfully in Shopify and MongoDB: {customer_doc}")

            return jsonify({
                "message": "Customer created successfully", 
                "customer": customer_doc
            }), 201
        else:
            error_message = response_data.get('errors', {})
            if isinstance(error_message, dict):
                error_message = '; '.join(f"{k}: {v}" for k, v in error_message.items())
            elif isinstance(error_message, list):
                error_message = '; '.join(error_message)
            else:
                error_message = str(error_message)
                
            logging.debug(f"create_customer - Failed to create customer in Shopify, error: {error_message}")
            return jsonify({"message": f"Failed to create customer: {error_message}"}), 400
    except Exception as e:
        logging.error(f"create_customer - Error processing response: {str(e)}")
        return jsonify({"message": "Error processing response"}), 500

@shopify_overview_bp.route('/shopify/overview/fetch_customer_by_gid', methods=['GET'])
@login_required
def fetch_customer_by_gid():
    gid = request.args.get('gid')
    if not gid:
        logging.debug(f"fetch_customer_by_gid - Missing GID parameter")
        return jsonify({"message": "Customer ID is required"}), 400
    username = current_user.username

    logging.debug(f"fetch_customer_by_gid - Fetching customer with GID: {gid} for username: {username}")

    # First, check if we have this customer in our local database
    local_customer = shCustomers_collection.find_one({'username': username, 'id': gid}, {'_id': 0})

    if local_customer:
        logging.debug(f"fetch_customer_by_gid - Customer found in local database: {local_customer}")
        customer_data = convert_objectid(local_customer)
    else:
        # If not found locally, fetch from Shopify
        shopify_store_name, shopify_access_token, error = get_shopify_credentials()
        if error:
            return jsonify({"message": error}), 400

        shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/customers/{gid}.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        logging.debug(f"fetch_customer_by_gid - Fetching customer from Shopify: {shopify_api_url}")

        response = requests.get(shopify_api_url, headers=headers)

        logging.debug(f"fetch_customer_by_gid - Shopify response status: {response.status_code}, data: {response.text}")

        if response.status_code == 200:
            shopify_customer = response.json()['customer']
            
            # Add username and created_at fields
            shopify_customer['username'] = username
            shopify_customer['created_at'] = datetime.utcnow()
            
            # Insert or update the customer in our local database
            shCustomers_collection.update_one(
                {'username': username, 'id': gid},
                {'$set': shopify_customer},
                upsert=True
            )

            logging.debug(f"fetch_customer_by_gid - Customer fetched from Shopify and saved locally: {shopify_customer}")
            customer_data = convert_objectid(shopify_customer)
        else:
            logging.error(f"fetch_customer_by_gid - Failed to fetch customer from Shopify. Status: {response.status_code}, Response: {response.text}")
            return jsonify({"error": "Failed to fetch customer from Shopify"}), response.status_code

    # Check if the customer already has a store credit account
    existing_gift_card = shGiftCards_collection.find_one({'customer_id': str(gid)})
    
    if existing_gift_card:
        customer_data['has_store_credit'] = True
        customer_data['store_credit_balance'] = existing_gift_card['balance']
    else:
        customer_data['has_store_credit'] = False

    return jsonify({
        "customer": customer_data,
        "offer_store_credit": not customer_data['has_store_credit']
    }), 200

@shopify_overview_bp.route('/shopify/overview/create_store_credit', methods=['POST'])
@login_required
def create_store_credit():
    data = request.json
    customer_id = data.get('customer_id')
    initial_balance = data.get('initial_balance')

    if not customer_id or initial_balance is None:
        return jsonify({"error": "Customer ID and initial balance are required"}), 400

    shopify_store_name, shopify_access_token, error = get_shopify_credentials()
    if error:
        return jsonify({"message": error}), 400

    # Create gift card in Shopify
    shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/gift_cards.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }
    gift_card_data = {
        "gift_card": {
            "initial_value": initial_balance,
            "customer_id": customer_id
        }
    }

    response = requests.post(shopify_api_url, headers=headers, json=gift_card_data)

    if response.status_code == 201:
        new_gift_card = response.json()['gift_card']
        
        # Save gift card to local database
        username = current_user.username
        new_gift_card['username'] = username
        new_gift_card['customer_id'] = str(customer_id)
        new_gift_card = convert_objectid(new_gift_card)
        shGiftCards_collection.insert_one(new_gift_card)

        # Update customer record
        shCustomers_collection.update_one(
            {'username': username, 'id': customer_id},
            {'$set': {'has_store_credit': True, 'store_credit_balance': initial_balance}}
        )

        return jsonify({
            "message": "Store credit account created successfully",
            "gift_card": new_gift_card
        }), 201
    else:
        return jsonify({"error": "Failed to create store credit account", "details": response.text}), response.status_code
