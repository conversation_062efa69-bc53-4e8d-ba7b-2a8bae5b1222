# Script to pull marketplace listings from CardTrader API using a hybrid approach
# First tries expansion_id for bulk processing, then falls back to blueprint_id for individual cards
import asyncio
import aiohttp
import time
import random
import motor.motor_asyncio
from pymongo import MongoClient
from datetime import datetime
import logging
import sys
import argparse
import json
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# Async MongoDB client
motor_client = motor.motor_asyncio.AsyncIOMotorClient(mongo_uri)
motor_db = motor_client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}

# Rate limiting - CardTrader API is limited to 10 requests per second
class RateLimiter:
    def __init__(self, rate_limit=5):
        self.rate_limit = rate_limit  # requests per second
        self.tokens = rate_limit
        self.last_update = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        async with self.lock:
            now = time.time()
            time_passed = now - self.last_update
            self.tokens = min(self.rate_limit, self.tokens + time_passed * self.rate_limit)
            self.last_update = now
            
            if self.tokens < 1:
                # Not enough tokens, calculate sleep time
                sleep_time = (1 - self.tokens) / self.rate_limit
                await asyncio.sleep(sleep_time)
                self.tokens = 0
                self.last_update = time.time()
            else:
                self.tokens -= 1

# Global rate limiter
rate_limiter = None  # Will be initialized in main()

def get_blueprints_by_game(game_id=None, count=10, random_sample=True):
    """Get blueprints from the database, optionally filtered by game_id"""
    logger.info(f"Fetching blueprints from database...")
    
    # Check if blueprints collection exists
    if 'blueprints' not in cardtrader_db.list_collection_names():
        logger.error("blueprints collection not found in cardtrader database")
        return []
    
    # Build the query
    query = {"id": {"$exists": True}}  # Ensure id field exists
    if game_id is not None:
        query["game_id"] = game_id
        logger.info(f"Filtering blueprints by game_id: {game_id}")
    
    # Count matching blueprints
    total_blueprints = cardtrader_db.blueprints.count_documents(query)
    logger.info(f"Found {total_blueprints} matching blueprints in database")
    
    if total_blueprints == 0:
        return []
    
    # Limit the number of blueprints to process if specified
    if count > 0 and count < total_blueprints:
        if random_sample:
            # Get random sample
            pipeline = [
                {"$match": query},
                {"$sample": {"size": count}}
            ]
            blueprints = list(cardtrader_db.blueprints.aggregate(pipeline))
            logger.info(f"Selected {len(blueprints)} random blueprints")
        else:
            # Get first N blueprints
            blueprints = list(cardtrader_db.blueprints.find(query).limit(count))
            logger.info(f"Selected first {len(blueprints)} blueprints")
    else:
        # Get all matching blueprints
        blueprints = list(cardtrader_db.blueprints.find(query))
        logger.info(f"Selected all {len(blueprints)} matching blueprints")
    
    return blueprints

def group_blueprints_by_expansion(blueprints):
    """Group blueprints by expansion_id"""
    expansion_groups = defaultdict(list)
    for blueprint in blueprints:
        expansion_id = blueprint.get('expansion_id')
        if expansion_id:
            expansion_groups[expansion_id].append(blueprint)
    
    logger.info(f"Grouped blueprints into {len(expansion_groups)} expansions")
    return expansion_groups

def extract_market_summary(listings):
    """Extract summary market data from listings"""
    if not listings:
        return {
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None
            }
        }
    
    # Calculate total quantity
    total_quantity = sum(listing.get("quantity", 0) for listing in listings)
    
    # Extract prices (in cents)
    prices = []
    for listing in listings:
        price_info = listing.get("price", {})
        if isinstance(price_info, dict) and "cents" in price_info:
            # Convert cents to dollars/euros for easier reading
            price = float(price_info["cents"]) / 100
            prices.append(price)
    
    # Calculate price statistics
    price_data = {
        "min": min(prices) if prices else None,
        "max": max(prices) if prices else None,
        "avg": sum(prices) / len(prices) if prices else None,
        "currency": listings[0].get("price", {}).get("currency", "EUR") if listings else "EUR"
    }
    
    return {
        "total_quantity": total_quantity,
        "price_data": price_data
    }

async def fetch_marketplace_listings_by_expansion(session, expansion_id):
    """Fetch marketplace listings for an entire expansion using expansion_id"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"expansion_id": expansion_id}
    
    # Acquire token from rate limiter
    await rate_limiter.acquire()
    
    try:
        logger.info(f"Fetching marketplace listings for expansion ID: {expansion_id}")
        
        # Make the API request
        async with session.get(url, params=params, headers=HEADERS, timeout=30) as response:
            if response.status == 200:
                data = await response.json()
                # The response is an object with blueprint IDs as keys and arrays of products as values
                logger.info(f"Successfully fetched marketplace listings for expansion ID: {expansion_id}")
                return data
            elif response.status == 429:
                # Rate limited, wait and retry
                error_text = await response.text()
                logger.warning(f"Rate limited: {error_text}")
                await asyncio.sleep(5)  # Wait longer before retry
                
                # Retry the request
                await rate_limiter.acquire()
                async with session.get(url, params=params, headers=HEADERS, timeout=30) as retry_response:
                    if retry_response.status == 200:
                        data = await retry_response.json()
                        logger.info(f"Retry successful for expansion ID: {expansion_id}")
                        return data
                    else:
                        error_text = await retry_response.text()
                        logger.error(f"Retry failed with status code {retry_response.status}: {error_text}")
                        return {}
            else:
                error_text = await response.text()
                logger.error(f"API request failed with status code {response.status}: {error_text}")
                return {}
    except aiohttp.ClientError as e:
        logger.error(f"Client error fetching marketplace listings for expansion ID {expansion_id}: {str(e)}")
        # Retry once after a short delay
        await asyncio.sleep(5)
        try:
            logger.info(f"Retrying after error for expansion ID: {expansion_id}")
            await rate_limiter.acquire()
            async with session.get(url, params=params, headers=HEADERS, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Retry successful for expansion ID: {expansion_id}")
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"Retry failed with status code {response.status}: {error_text}")
                    return {}
        except Exception as retry_e:
            logger.error(f"Retry also failed for expansion ID {expansion_id}: {str(retry_e)}")
            return {}
    except Exception as e:
        logger.error(f"Error fetching marketplace listings for expansion ID {expansion_id}: {str(e)}")
        return {}

async def fetch_marketplace_listings_by_blueprint(session, blueprint_id):
    """Fetch marketplace listings for a specific blueprint ID"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"blueprint_id": blueprint_id}
    
    # Acquire token from rate limiter
    await rate_limiter.acquire()
    
    try:
        logger.info(f"Fetching marketplace listings for blueprint ID: {blueprint_id}")
        
        # Make the API request
        async with session.get(url, params=params, headers=HEADERS, timeout=10) as response:
            if response.status == 200:
                data = await response.json()
                # The response is an object with blueprint IDs as keys and arrays of products as values
                listings = data.get(str(blueprint_id), [])
                logger.info(f"Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                return listings
            elif response.status == 429:
                # Rate limited, wait and retry
                error_text = await response.text()
                logger.warning(f"Rate limited: {error_text}")
                await asyncio.sleep(2)  # Wait a bit longer before retry
                
                # Retry the request
                await rate_limiter.acquire()
                async with session.get(url, params=params, headers=HEADERS, timeout=10) as retry_response:
                    if retry_response.status == 200:
                        data = await retry_response.json()
                        listings = data.get(str(blueprint_id), [])
                        logger.info(f"Retry successful. Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                        return listings
                    else:
                        error_text = await retry_response.text()
                        logger.error(f"Retry failed with status code {retry_response.status}: {error_text}")
                        return []
            else:
                error_text = await response.text()
                logger.error(f"API request failed with status code {response.status}: {error_text}")
                return []
    except aiohttp.ClientError as e:
        logger.error(f"Client error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        # Retry once after a short delay
        await asyncio.sleep(1)
        try:
            logger.info(f"Retrying after error for blueprint ID: {blueprint_id}")
            await rate_limiter.acquire()
            async with session.get(url, params=params, headers=HEADERS, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    listings = data.get(str(blueprint_id), [])
                    logger.info(f"Retry successful. Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                    return listings
                else:
                    error_text = await response.text()
                    logger.error(f"Retry failed with status code {response.status}: {error_text}")
                    return []
        except Exception as retry_e:
            logger.error(f"Retry also failed for blueprint ID {blueprint_id}: {str(retry_e)}")
            return []
    except Exception as e:
        logger.error(f"Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
        return []

async def save_to_marketprices(blueprint, listings):
    """Save the marketplace listings summary to the marketprices collection using motor"""
    if not listings:
        logger.info(f"No listings to save for blueprint ID: {blueprint.get('id')}")
        return
    
    # Extract market summary data
    market_summary = extract_market_summary(listings)
    
    # Create a document to insert
    document = {
        "blueprint_id": blueprint.get("id"),
        "blueprint_name": blueprint.get("name"),
        "expansion_id": blueprint.get("expansion_id"),
        "game_id": blueprint.get("game_id"),
        "total_quantity": market_summary["total_quantity"],
        "price_data": market_summary["price_data"],
        "listings_count": len(listings),  # Store the count of listings for reference
        "fetched_at": datetime.now()
    }
    
    try:
        # Insert or update the document in the marketprices collection
        result = await motor_db.marketprices.update_one(
            {"blueprint_id": blueprint.get("id")},
            {"$set": document},
            upsert=True
        )
        
        if result.upserted_id:
            logger.info(f"Inserted new document for blueprint ID: {blueprint.get('id')}")
        else:
            logger.info(f"Updated existing document for blueprint ID: {blueprint.get('id')}")
    except Exception as e:
        logger.error(f"Error saving to marketprices collection: {str(e)}")

async def process_expansion(session, expansion_id, blueprints, results, failed_blueprints):
    """Process all blueprints for an expansion"""
    try:
        # Fetch marketplace listings for the expansion
        expansion_data = await fetch_marketplace_listings_by_expansion(session, expansion_id)
        
        if not expansion_data:
            logger.warning(f"No data returned for expansion ID: {expansion_id}")
            # Add all blueprints to failed_blueprints for individual processing
            for blueprint in blueprints:
                failed_blueprints.append(blueprint)
            return 0
        
        # Process each blueprint in this expansion
        processed_count = 0
        for blueprint in blueprints:
            blueprint_id = blueprint.get('id')
            blueprint_name = blueprint.get('name', 'Unknown')
            
            # Get listings for this blueprint from the expansion data
            blueprint_listings = expansion_data.get(str(blueprint_id), [])
            
            if blueprint_listings:
                # Add result
                results.append((blueprint, blueprint_listings))
                logger.info(f"Processed: {blueprint_name} (ID: {blueprint_id}) - Found {len(blueprint_listings)} listings")
                processed_count += 1
            else:
                # No listings found in expansion data, add to failed_blueprints for individual processing
                logger.info(f"No listings found for {blueprint_name} (ID: {blueprint_id}) in expansion data, will try individual fetch")
                failed_blueprints.append(blueprint)
        
        return processed_count
    except Exception as e:
        logger.error(f"Error processing expansion {expansion_id}: {str(e)}")
        # Add all blueprints to failed_blueprints for individual processing
        for blueprint in blueprints:
            failed_blueprints.append(blueprint)
        return 0

async def process_blueprint_individually(session, blueprint, results):
    """Process a single blueprint by fetching its listings directly"""
    blueprint_id = blueprint.get('id')
    blueprint_name = blueprint.get('name', 'Unknown')
    
    try:
        # Fetch marketplace listings for the blueprint
        listings = await fetch_marketplace_listings_by_blueprint(session, blueprint_id)
        
        if listings:
            # Add result
            results.append((blueprint, listings))
            logger.info(f"Individually processed: {blueprint_name} (ID: {blueprint_id}) - Found {len(listings)} listings")
            return 1
        else:
            logger.info(f"No listings found for {blueprint_name} (ID: {blueprint_id})")
            return 0
    except Exception as e:
        logger.error(f"Error processing blueprint {blueprint_id}: {str(e)}")
        return 0

async def process_batch(session, expansion_groups, batch_size=100):
    """Process a batch of expansions and save results"""
    results = []
    failed_blueprints = []
    expansion_tasks = []
    
    # Create tasks for all expansions
    for expansion_id, blueprints in expansion_groups.items():
        task = asyncio.create_task(process_expansion(session, expansion_id, blueprints, results, failed_blueprints))
        expansion_tasks.append(task)
    
    # Wait for all expansion tasks to complete
    await asyncio.gather(*expansion_tasks)
    
    # Process failed blueprints individually
    logger.info(f"{len(failed_blueprints)} blueprints need individual processing")
    
    if failed_blueprints:
        # Create a semaphore to limit concurrent individual requests
        semaphore = asyncio.Semaphore(10)  # Limit to 10 concurrent individual requests
        
        # Process failed blueprints individually
        individual_tasks = []
        for blueprint in failed_blueprints:
            async def process_with_semaphore(blueprint):
                async with semaphore:
                    return await process_blueprint_individually(session, blueprint, results)
            
            task = asyncio.create_task(process_with_semaphore(blueprint))
            individual_tasks.append(task)
        
        # Wait for all individual tasks to complete
        await asyncio.gather(*individual_tasks)
    
    # Process and save results
    save_tasks = []
    for blueprint, listings in results:
        save_task = asyncio.create_task(save_to_marketprices(blueprint, listings))
        save_tasks.append(save_task)
    
    # Wait for all save tasks to complete
    await asyncio.gather(*save_tasks)
    
    return len(results)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Pull marketplace listings from CardTrader API using a hybrid approach')
    parser.add_argument('--blueprints', type=int, default=10,
                        help='Number of blueprints to process (default: 10, use 0 for all matching blueprints)')
    parser.add_argument('--concurrency', type=int, default=5,
                        help='Maximum number of concurrent requests (default: 5)')
    parser.add_argument('--rate-limit', type=float, default=5.0,
                        help='API request rate limit in requests per second (default: 5.0)')
    parser.add_argument('--game', type=int, default=None,
                        help='Filter blueprints by game_id (default: None)')
    parser.add_argument('--random', action='store_true', default=True,
                        help='Select random blueprints (default: True)')
    return parser.parse_args()

async def main_async():
    # Parse command line arguments
    args = parse_arguments()
    
    # Initialize rate limiter
    global rate_limiter
    rate_limiter = RateLimiter(args.rate_limit)
    
    start_time = time.time()
    logger.info(f"Starting CardTrader marketplace data collection using hybrid approach")
    logger.info(f"Configuration: {args.blueprints} blueprints, {args.concurrency} concurrency, {args.rate_limit} req/sec, game_id: {args.game}")
    
    # Get blueprints
    blueprints = get_blueprints_by_game(args.game, args.blueprints, args.random)
    
    if not blueprints:
        logger.error("No blueprints found. Exiting.")
        return
    
    # Group blueprints by expansion_id
    expansion_groups = group_blueprints_by_expansion(blueprints)
    
    # Create marketprices collection if it doesn't exist
    if 'marketprices' not in cardtrader_db.list_collection_names():
        logger.info("Creating marketprices collection")
        cardtrader_db.create_collection('marketprices')
    
    # Process expansions
    total_processed = 0
    total_expansions = len(expansion_groups)
    
    # Create a ClientSession that will be used for all requests
    connector = aiohttp.TCPConnector(limit=args.concurrency, ssl=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        # Process all expansions
        logger.info(f"Processing {total_expansions} expansions")
        
        batch_start_time = time.time()
        processed = await process_batch(session, expansion_groups)
        batch_end_time = time.time()
        
        total_processed += processed
        
        # Log progress
        logger.info(f"Batch completed in {batch_end_time - batch_start_time:.2f} seconds")
        logger.info(f"Processed {total_processed}/{len(blueprints)} blueprints")
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Count documents and total listings in marketprices collection
    marketprices_count = cardtrader_db.marketprices.count_documents({})
    
    # Calculate listings by summing listings_count field
    pipeline = [
        {"$group": {"_id": None, "total_listings": {"$sum": "$listings_count"}}}
    ]
    result = list(cardtrader_db.marketprices.aggregate(pipeline))
    total_listings = result[0]["total_listings"] if result else 0
    
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {total_processed} blueprints from {total_expansions} expansions")
    logger.info(f"Found {total_listings} total marketplace listings")
    logger.info(f"Average processing time: {duration/total_processed:.2f} seconds per blueprint")
    logger.info(f"Processing rate: {total_processed/duration:.2f} blueprints per second")
    logger.info(f"Estimated time for all 101,711 blueprints: {101711/(total_processed/duration)/3600:.2f} hours")
    logger.info(f"Total documents in marketprices collection: {marketprices_count}")

def main():
    """Entry point for the script"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
