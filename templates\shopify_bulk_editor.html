{% extends "base.html" %}

{% block title %}Shopify Bulk Product Type Editor{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1>Shopify Bulk Product Type Editor</h1>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="productType">Product Type:</label>
                <input type="text" id="productType" class="form-control" placeholder="Enter product type to load">
            </div>
            <button id="loadProducts" class="btn btn-primary">Load Products</button>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="newProductType">New Product Type:</label>
                <input type="text" id="newProductType" class="form-control" placeholder="Enter new product type">
            </div>
            <button id="updateProductType" class="btn btn-success" disabled>Update Product Type</button>
        </div>
    </div>
    <div class="mt-4">
        <table id="productTable" class="table table-striped">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAll"></th>
                    <th>Product Title</th>
                    <th>Current Product Type</th>
                </tr>
            </thead>
            <tbody>
                <!-- Products will be loaded here -->
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/shopify_bulk_editor.js') }}"></script>
{% endblock %}
