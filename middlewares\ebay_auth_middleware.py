"""Middleware for eBay authentication."""
from functools import wraps
from flask import redirect, url_for, flash, request, current_app
from flask_login import current_user
from routes.ebay_auth_routes import refresh_ebay_token_if_needed
from models.ebay_auth_model import EbayAuthModel
from optimized_mongo_connection import OptimizedMongoConnection
from config import Config

def get_ebay_auth_model():
    mongo = OptimizedMongoConnection.get_instance()
    db = mongo.get_connection().get_database(Config.MONGO_DBNAME)
    return EbayAuthModel(db)

def check_ebay_token(marketplace_id: str = None):
    """Decorator to check if eBay token is valid and refresh if needed."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('index'))
            
            # Get marketplace ID from route or query params if not provided
            route_marketplace_id = marketplace_id or request.args.get('marketplace_id')
            
            # Get auth model
            ebay_auth_model = get_ebay_auth_model()
            
            # Check if user has any eBay connections
            auth_data = ebay_auth_model.get_by_user_id(str(current_user.id), route_marketplace_id)
            if not auth_data:
                flash('Please connect your eBay account first.', 'warning')
                return redirect(url_for('ebay_settings.view_ebay_settings'))
            
            # If multiple marketplaces and none specified, check if any are valid
            if isinstance(auth_data, list):
                any_valid = False
                for auth in auth_data:
                    # Skip if auth data is invalid
                    if not isinstance(auth, dict) or 'marketplace_id' not in auth:
                        continue
                        
                    if ebay_auth_model.is_token_valid(str(current_user.id), auth.get('marketplace_id')):
                        any_valid = True
                        break
                
                if not any_valid:
                    # Try to refresh tokens
                    if not refresh_ebay_token_if_needed(str(current_user.id)):
                        flash('Your eBay token has expired. Please reconnect your account.', 'warning')
                        return redirect(url_for('ebay_settings.view_ebay_settings'))
            else:
                # Check single marketplace token
                if not route_marketplace_id:
                    flash('No marketplace specified.', 'warning')
                    return redirect(url_for('ebay_settings.view_ebay_settings'))
                    
                if not ebay_auth_model.is_token_valid(str(current_user.id), route_marketplace_id):
                    # Try to refresh token
                    if not refresh_ebay_token_if_needed(str(current_user.id), route_marketplace_id):
                        flash('Your eBay token has expired. Please reconnect your account.', 'warning')
                        return redirect(url_for('ebay_settings.view_ebay_settings'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
