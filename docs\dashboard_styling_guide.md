# TCGSync Enterprise Dashboard Styling Guide

This document provides a comprehensive reference for the styling used throughout the TCGSync Enterprise dashboard. Use this guide to maintain consistent styling when developing new features or modifying existing ones.

## Table of Contents

1. [Color Palette](#color-palette)
2. [Typography](#typography)
3. [Layout & Spacing](#layout--spacing)
4. [Components](#components)
   - [Cards](#cards)
   - [Buttons](#buttons)
   - [Quick Access Section](#quick-access-section)
   - [Tables](#tables)
   - [Badges](#badges)
   - [Modals](#modals)
5. [Animations & Effects](#animations--effects)
6. [Responsive Design](#responsive-design)

## Color Palette

### Primary Colors

```css
/* Main background color - Purple */
--background-color: #6b21a8;

/* Card backgrounds */
--card-bg: #1e293b;
--card-bg-hover: #2d3748;

/* Text colors */
--text-color: #ffffff;
--text-muted: #94a3b8;
```

### Accent Colors

```css
/* Primary accent */
--primary-color: #e91e63; /* Magenta */
--primary-color-dark: #c2185b;
--secondary-color: #ff4081;

/* Status colors */
--danger-color: #e74c3c;
--success-color: #2ecc71;
--warning-color: #f1c40f;
--info-color: #3498db;
```

### Section-specific Colors

```css
--dashboard-color: #ff4081;
--advanced-color: #9b59b6;
--shopify-color: #2ecc71;
--buylist-color: #e67e22;
--pos-color: #e74c3c;
--cardmarket-color: #1abc9c;
--automations-color: #f1c40f;
--admin-color: #34495e;
```

### Gradients

```css
/* Quick access section background */
background: linear-gradient(135deg, #1a1a2e, #16213e);

/* Card header gradient */
background: linear-gradient(135deg, #4a90e2, #357abd);

/* Premium badge gradient */
background: linear-gradient(135deg, #FFD700, #FFA500);

/* Feature request button gradient */
background: linear-gradient(135deg, #6b21a8, #4c1d95);
```

## Typography

### Font Families

```css
/* Primary fonts */
font-family: 'Poppins', sans-serif; /* For headings and emphasis */
font-family: 'Roboto', sans-serif; /* For body text */
```

### Font Sizes

```css
/* Headings */
--heading-large: 24px;
--heading-medium: 20px;
--heading-small: 18px;

/* Body text */
--text-large: 16px;
--text-normal: 14px;
--text-small: 13px;
--text-xs: 12px;
```

### Font Weights

```css
--font-light: 300;
--font-regular: 400;
--font-semibold: 600;
--font-bold: 700;
```

## Layout & Spacing

### Container Spacing

```css
/* Main content padding */
.main-content {
    padding: 2rem;
    padding-top: 80px !important; /* Accounts for top bar */
}

/* Dashboard container */
.dashboard-container {
    padding: 10px;
}

/* Card padding */
.dashboard-card-header {
    padding: 15px 20px;
}

.dashboard-card-body {
    padding: 20px;
}
```

### Grid System

```css
/* Quick access grid */
.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0;
    padding: 15px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .quick-access-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}
```

## Components

### Cards

#### Dashboard Card

```css
.dashboard-card {
    background-color: #1e293b;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.dashboard-card-header {
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.dashboard-card-body {
    padding: 20px;
}
```

#### Quick Access Card

```css
.quick-access-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.03);
    padding: 15px;
    cursor: pointer;
    height: 100%;
    border-radius: 10px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    margin: 4px;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    transform: scale(0.75);
}

.quick-access-card:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.07);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
```

#### Enterprise Feature Card

```css
.enterprise-feature-item {
    display: flex;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.enterprise-feature-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
```

### Buttons

```css
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Feature request button */
.btn-feature-request {
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(107, 33, 168, 0.3);
    align-self: flex-start;
    margin-top: 5px !important;
}

.btn-feature-request:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(107, 33, 168, 0.4);
    color: white;
    background: linear-gradient(135deg, #7c3adb, #5a24b5);
}
```

### Quick Access Section

```css
.quick-access-section {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: none;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 20px;
    margin-top: 5px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    position: relative;
}

.quick-access-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, #ff4081, #7e57c2, #2196f3);
    z-index: 2;
}
```

### Tables

```css
.table {
    color: white;
    background-color: #1e293b;
}

.table thead th {
    background-color: #1a202c;
    color: white;
    border-bottom: 2px solid #4a5568;
    padding: 15px;
    font-weight: 600;
}

.table tbody tr {
    background-color: #1e293b;
}

.table tbody tr:hover {
    background-color: #3a4a5c;
}

.table td, .table th {
    border-color: #4a5568;
    padding: 15px;
    vertical-align: middle;
}
```

> **Important**: All tables must have a dark background (#1e293b) with white text to maintain consistent styling across the application.

### Badges

```css
.badge {
    padding: 6px 12px;
    font-weight: 500;
    border-radius: 20px;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

.bg-gradient-premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}
```

### Form Controls & Dropdowns

```css
/* Form controls styling */
.form-control, .form-select, select {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 8px;
}

.form-control:focus, .form-select:focus, select:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
}

/* Dropdown styling */
.form-select option, select option {
    background-color: #1e293b !important;
    color: white !important;
}

.form-select, select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
```

> **Important**: All form controls and dropdowns must have a dark background with white text. Dropdown menus must always have a dark background (#1e293b) to ensure text is readable.

### Modals

```css
.modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}
```

## Animations & Effects

### Transitions

```css
/* Standard transition */
transition: all 0.3s ease;

/* Smooth transition for cards */
transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);

/* Bouncy animation for icons */
transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
```

### Hover Effects

```css
/* Card hover effect */
transform: translateY(-5px);
box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);

/* Button hover effect */
transform: translateY(-2px);
box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

/* Icon hover effect */
transform: scale(1.15);
```

### Shadows

```css
/* Light shadow */
box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);

/* Medium shadow */
box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

/* Heavy shadow */
box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);

/* Text shadow */
text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
```

## Responsive Design

### Breakpoints

```css
/* Large screens */
@media (max-width: 1200px) {
    .quick-access-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium screens */
@media (max-width: 768px) {
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .topbar {
        padding: 15px;
    }

    .container-fluid {
        padding: 15px;
    }
}

/* Small screens */
@media (max-width: 576px) {
    /* Add specific small screen adjustments here */
}
```

### Mobile-specific Styles

```css
/* Mobile-specific styles are loaded conditionally */
<link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}" media="screen and (max-width: 768px)">
```

---

## Usage Guidelines

1. **Consistency**: Always use the variables and classes defined in this guide to maintain a consistent look and feel across the application.

2. **Extending Styles**: When creating new components, extend existing styles rather than creating entirely new ones.

3. **Background Color**: All pages should use the purple background color (#6b21a8) for consistent styling.

4. **Card Design**: Use the dashboard card component for all content sections to maintain visual consistency.

5. **Table Styling**: All tables must have a dark background (#1e293b) with white text to maintain consistent styling across the application.

6. **Dropdown Styling**: All dropdown menus must have a dark background (#1e293b) with white text to ensure readability and consistent styling.

7. **Icons**: Use Font Awesome icons consistently throughout the application, with the appropriate colors for each section.

8. **Responsive Design**: Ensure all new components are responsive and work well on mobile devices.

9. **Accessibility**: Maintain good color contrast for text readability and include appropriate ARIA attributes for screen readers.
