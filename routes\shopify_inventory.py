from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required
import requests
import logging
from utils.shopify_api import get_shopify_headers
from models.database import mongo
from pymongo.collection import Collection

# Set up logging
logger = logging.getLogger(__name__)

# Create blueprint
shopify_inventory_bp = Blueprint('shopify_inventory', __name__)

@shopify_inventory_bp.route('/shopify/api/variant/<variant_id>/inventory-item-id', methods=['GET'])
@login_required
def get_inventory_item_id(variant_id):
    """
    Get the inventory item ID for a variant.
    This endpoint is called from the frontend when processing a buy transaction.
    """
    try:
        # First check if we have this information in our database
        shProducts_collection = mongo.db.shProducts
        variant = shProducts_collection.find_one(
            {"variant.id": int(variant_id)},
            {"variant.inventory_item_id": 1}
        )

        if variant and variant.get('variant', {}).get('inventory_item_id'):
            return jsonify({
                "success": True,
                "inventory_item_id": variant['variant']['inventory_item_id']
            })

        # If not in database, fetch from Shopify API
        store_name = current_user.shopifyStoreName
        access_token = current_user.shopifyAccessToken

        if not store_name or not access_token:
            return jsonify({"success": False, "message": "Shopify credentials not found"}), 400

        # Get headers for Shopify API
        headers = get_shopify_headers(access_token)

        # Get the variant from Shopify
        variant_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/variants/{variant_id}.json"
        response = requests.get(variant_url, headers=headers)
        response.raise_for_status()

        variant_data = response.json().get('variant', {})
        inventory_item_id = variant_data.get('inventory_item_id')

        if not inventory_item_id:
            return jsonify({"success": False, "message": "Inventory item ID not found"}), 404

        return jsonify({
            "success": True,
            "inventory_item_id": inventory_item_id
        })
    except Exception as e:
        logger.error(f"Error getting inventory item ID for variant {variant_id}: {str(e)}")
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

@shopify_inventory_bp.route('/shopify/api/inventory/adjust-buy', methods=['POST'])
@login_required
def adjust_buy_inventory():
    """
    Adjust Shopify inventory when buying items from customers.
    This endpoint is called from the frontend when processing a buy transaction.
    """
    try:
        data = request.json
        items = data.get('items', [])

        if not items:
            return jsonify({"success": False, "message": "No items provided"}), 400

        # Get Shopify credentials
        store_name = current_user.shopifyStoreName
        access_token = current_user.shopifyAccessToken

        if not store_name or not access_token:
            return jsonify({"success": False, "message": "Shopify credentials not found"}), 400

        # Get headers for Shopify API
        headers = get_shopify_headers(access_token)

        # Process each item
        results = []
        for item in items:
            inventory_item_id = item.get('inventory_item_id')
            quantity = item.get('quantity', 1)
            variant_id = item.get('variant_id')
            title = item.get('title', 'Unknown item')

            if not inventory_item_id:
                results.append({
                    "variant_id": variant_id,
                    "title": title,
                    "success": False,
                    "message": "Missing inventory_item_id"
                })
                continue

            # Get the location ID
            location_id = None
            try:
                location_response = requests.get(
                    f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json",
                    headers=headers
                )
                location_response.raise_for_status()
                locations = location_response.json().get('locations', [])

                if not locations:
                    results.append({
                        "variant_id": variant_id,
                        "title": title,
                        "success": False,
                        "message": "No locations found"
                    })
                    continue

                # Use the first active location
                for location in locations:
                    if location.get('active', False):
                        location_id = location.get('id')
                        break

                if not location_id:
                    location_id = locations[0].get('id')  # Fallback to first location
            except Exception as e:
                logger.error(f"Error getting locations: {str(e)}")
                results.append({
                    "variant_id": variant_id,
                    "title": title,
                    "success": False,
                    "message": f"Error getting locations: {str(e)}"
                })
                continue

            # Adjust inventory (positive adjustment for buying items)
            try:
                adjust_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
                inventory_data = {
                    "inventory_item_id": inventory_item_id,
                    "location_id": location_id,
                    "available_adjustment": quantity  # Positive adjustment for buying items
                }

                logger.info(f"Adjusting inventory for {title} (variant {variant_id}): {inventory_data}")

                # Add retry logic
                max_retries = 3
                retry_delay = 1  # seconds
                success = False

                for retry in range(max_retries):
                    try:
                        inventory_response = requests.post(adjust_url, json=inventory_data, headers=headers)
                        inventory_response.raise_for_status()

                        # If we get here, the request was successful
                        success = True
                        results.append({
                            "variant_id": variant_id,
                            "title": title,
                            "success": True,
                            "message": "Inventory adjusted successfully"
                        })
                        break
                    except requests.exceptions.RequestException as e:
                        logger.warning(f"Retry {retry + 1}/{max_retries} failed for {title}: {str(e)}")
                        if retry < max_retries - 1:
                            import time
                            time.sleep(retry_delay)

                if not success:
                    results.append({
                        "variant_id": variant_id,
                        "title": title,
                        "success": False,
                        "message": "Failed to adjust inventory after multiple retries"
                    })
            except Exception as e:
                logger.error(f"Error adjusting inventory for {title}: {str(e)}")
                results.append({
                    "variant_id": variant_id,
                    "title": title,
                    "success": False,
                    "message": f"Error adjusting inventory: {str(e)}"
                })

        # Check if all items were processed successfully
        all_success = all(result.get('success', False) for result in results)

        return jsonify({
            "success": all_success,
            "results": results,
            "message": "All items processed successfully" if all_success else "Some items failed to process"
        })
    except Exception as e:
        logger.error(f"Error in adjust_buy_inventory: {str(e)}")
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500
