// Sales Management JavaScript

let currentPage = 1;
let currentFilters = {};
let transactionsData = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    loadTransactions();
    
    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
});

// Load transactions from the server
async function loadTransactions(page = 1) {
    try {
        showLoading();
        
        const params = new URLSearchParams({
            page: page,
            per_page: 50,
            ...currentFilters
        });
        
        const response = await fetch(`/sales/api/transactions?${params}`);
        const data = await response.json();
        
        if (data.success) {
            transactionsData = data.transactions;
            renderTransactionsTable(data.transactions);
            renderPagination(data.pagination);
            updateSummaryCards(data.transactions);
        } else {
            showError('Failed to load transactions: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        showError('Error loading transactions: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Render the transactions table
function renderTransactionsTable(transactions) {
    const tbody = document.getElementById('transactionsTableBody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i><br>
                        <h6 class="text-muted">No transactions found</h6>
                        <p class="mb-0 small">Try adjusting your filters or date range</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => {
        const statusBadge = getStatusBadge(transaction.status);
        const paymentMethodBadge = getPaymentMethodBadge(transaction);
        const formattedDate = new Date(transaction.timestamp).toLocaleString();
        
        return `
            <tr class="transaction-row" data-status="${transaction.status}">
                <td>
                    <div class="text-white">${formattedDate}</div>
                    <small class="text-muted">${new Date(transaction.timestamp).toLocaleDateString()}</small>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="fw-bold text-white">${transaction.customer_name || 'Walk-in Customer'}</div>
                    ${transaction.customer_email ? `<small class="text-muted"><i class="fas fa-envelope me-1"></i>${transaction.customer_email}</small>` : ''}
                </td>
                <td>
                    <div class="fw-bold text-white fs-6">$${transaction.total_amount.toFixed(2)}</div>
                </td>
                <td>
                    <div class="payment-methods-cell">
                        ${paymentMethodBadge}
                        ${transaction.detailed_payments && transaction.detailed_payments.length > 1 ?
                            `<br><small class="text-muted"><i class="fas fa-info-circle me-1"></i>${transaction.detailed_payments.length} payment methods</small>` :
                            ''
                        }
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">
                        <i class="fas fa-shopping-cart me-1"></i>${transaction.items_count} items
                    </span>
                </td>
                <td>
                    <div class="text-white">${transaction.employee_name}</div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewTransactionDetails('${transaction.id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${transaction.can_revert ? `
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-warning btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="Revert Options">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    ${!transaction.store_credit_reverted ? `
                                        <li><a class="dropdown-item" href="#" onclick="revertStoreCredit('${transaction.id}')">
                                            <i class="fas fa-credit-card me-2"></i>Revert Store Credit
                                        </a></li>
                                    ` : ''}
                                    ${!transaction.inventory_reverted ? `
                                        <li><a class="dropdown-item" href="#" onclick="revertInventory('${transaction.id}')">
                                            <i class="fas fa-boxes me-2"></i>Revert Inventory
                                        </a></li>
                                    ` : ''}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="revertFullTransaction('${transaction.id}')">
                                        <i class="fas fa-undo-alt me-2"></i>Full Revert
                                    </a></li>
                                </ul>
                            </div>
                        ` : `
                            <button class="btn btn-outline-secondary btn-sm" disabled title="No revert options available">
                                <i class="fas fa-lock"></i>
                            </button>
                        `}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get status badge HTML
function getStatusBadge(status) {
    switch (status) {
        case 'completed':
            return '<span class="badge bg-success">Completed</span>';
        case 'failed':
            return '<span class="badge bg-danger">Failed</span>';
        case 'pending':
            return '<span class="badge bg-warning">Pending</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

// Get payment method badge HTML with detailed breakdown
function getPaymentMethodBadge(transaction) {
    // Use detailed payments if available, otherwise fall back to basic breakdown
    if (transaction.detailed_payments && transaction.detailed_payments.length > 0) {
        return getDetailedPaymentBadges(transaction.detailed_payments);
    }

    // Fallback to basic breakdown
    const breakdown = transaction.payment_breakdown;
    const badges = [];

    if (breakdown.store_credit > 0) {
        badges.push(`<span class="badge bg-info mb-1">
            <i class="fas fa-credit-card me-1"></i>Store Credit $${breakdown.store_credit.toFixed(2)}
        </span>`);
    }
    if (breakdown.cash > 0) {
        badges.push(`<span class="badge bg-success mb-1">
            <i class="fas fa-money-bill-wave me-1"></i>Cash $${breakdown.cash.toFixed(2)}
        </span>`);
    }
    if (breakdown.card > 0) {
        badges.push(`<span class="badge bg-primary mb-1">
            <i class="fas fa-credit-card me-1"></i>Card $${breakdown.card.toFixed(2)}
        </span>`);
    }

    if (badges.length === 0) {
        const methodIcon = getPaymentMethodIcon(transaction.payment_method);
        return `<span class="badge bg-secondary">
            <i class="${methodIcon} me-1"></i>${transaction.payment_method}
        </span>`;
    }

    return badges.join('<br>');
}

// Get detailed payment badges with customer information
function getDetailedPaymentBadges(detailedPayments) {
    const badges = [];

    // Group payments by type and customer
    const paymentGroups = {};

    detailedPayments.forEach(payment => {
        const key = payment.customer_name ?
            `${payment.type}-${payment.customer_name}` :
            payment.type;

        if (!paymentGroups[key]) {
            paymentGroups[key] = {
                type: payment.type,
                amount: 0,
                customer_name: payment.customer_name,
                display_type: payment.display_type || payment.type
            };
        }

        paymentGroups[key].amount += payment.amount;
    });

    // Create badges for each payment group
    Object.values(paymentGroups).forEach(group => {
        const icon = getPaymentMethodIcon(group.type);
        const badgeClass = getPaymentMethodBadgeClass(group.type);

        let badgeText = `<i class="${icon} me-1"></i>${group.display_type} $${group.amount.toFixed(2)}`;

        // Add customer name for store credit payments
        if (group.customer_name && group.type === 'storecredit') {
            badgeText = `<i class="${icon} me-1"></i>Store Credit<br><small>${group.customer_name}</small><br>$${group.amount.toFixed(2)}`;
        }

        badges.push(`<span class="badge ${badgeClass} mb-1 d-inline-block" style="max-width: 150px; white-space: normal; line-height: 1.2;">
            ${badgeText}
        </span>`);
    });

    return badges.join('<br>');
}

// Get badge class for payment method
function getPaymentMethodBadgeClass(method) {
    switch (method?.toLowerCase()) {
        case 'cash':
            return 'bg-success';
        case 'card':
            return 'bg-primary';
        case 'storecredit':
        case 'store_credit':
            return 'bg-info';
        case 'giftcard':
        case 'gift_card':
            return 'bg-warning';
        default:
            return 'bg-secondary';
    }
}

// Get payment method icon
function getPaymentMethodIcon(method) {
    switch (method?.toLowerCase()) {
        case 'cash':
            return 'fas fa-money-bill-wave';
        case 'card':
            return 'fas fa-credit-card';
        case 'store_credit':
        case 'storecredit':
            return 'fas fa-wallet';
        case 'giftcard':
        case 'gift_card':
            return 'fas fa-gift';
        case 'split':
            return 'fas fa-layer-group';
        default:
            return 'fas fa-dollar-sign';
    }
}

// Format detailed payment methods for modal display
function formatDetailedPaymentMethods(detailedPayments) {
    if (!detailedPayments || detailedPayments.length === 0) {
        return '<div class="text-muted">No payment information available</div>';
    }

    // Group payments by customer for store credit, keep others separate
    const paymentGroups = [];
    const customerGroups = {};

    detailedPayments.forEach(payment => {
        if (payment.type === 'storecredit' && payment.customer_name) {
            // Group store credit by customer
            if (!customerGroups[payment.customer_name]) {
                customerGroups[payment.customer_name] = {
                    customer_name: payment.customer_name,
                    customer_id: payment.customer_id,
                    total_amount: 0,
                    payments: []
                };
            }
            customerGroups[payment.customer_name].total_amount += payment.amount;
            customerGroups[payment.customer_name].payments.push(payment);
        } else {
            // Keep other payment types separate
            paymentGroups.push(payment);
        }
    });

    // Add customer groups to payment groups
    Object.values(customerGroups).forEach(group => {
        paymentGroups.push({
            type: 'storecredit',
            amount: group.total_amount,
            customer_name: group.customer_name,
            customer_id: group.customer_id,
            display_type: 'Store Credit',
            is_customer_group: true
        });
    });

    // Sort payments by amount (largest first)
    paymentGroups.sort((a, b) => b.amount - a.amount);

    return paymentGroups.map(payment => {
        const icon = getPaymentMethodIcon(payment.type);
        const badgeClass = getPaymentMethodBadgeClass(payment.type);

        let paymentHTML = `
            <div class="payment-method-item p-3 mb-2 border rounded ${badgeClass.replace('bg-', 'border-')}">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="${icon} me-2 text-${badgeClass.replace('bg-', '')}"></i>
                        <div>
                            <strong>${payment.display_type || payment.type}</strong>
        `;

        // Add customer information for store credit
        if (payment.customer_name && payment.type === 'storecredit') {
            paymentHTML += `
                            <br><small class="text-muted">
                                <i class="fas fa-user me-1"></i>${payment.customer_name}
                            </small>
            `;
        }

        paymentHTML += `
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fw-bold fs-5 text-${badgeClass.replace('bg-', '')}">$${payment.amount.toFixed(2)}</span>
                        <br><small class="text-muted">${payment.status || 'completed'}</small>
                    </div>
                </div>
            </div>
        `;

        return paymentHTML;
    }).join('');
}

// Render pagination
function renderPagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    
    if (pagination.pages <= 1) {
        paginationEl.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    if (pagination.page > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadTransactions(${pagination.page - 1})">Previous</a>
            </li>
        `;
    }
    
    // Page numbers
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadTransactions(${i})">${i}</a>
            </li>
        `;
    }
    
    // Next button
    if (pagination.page < pagination.pages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadTransactions(${pagination.page + 1})">Next</a>
            </li>
        `;
    }
    
    paginationEl.innerHTML = paginationHTML;
    currentPage = pagination.page;
}

// Update summary cards
function updateSummaryCards(transactions) {
    const completed = transactions.filter(t => t.status === 'completed').length;
    const failed = transactions.filter(t => t.status === 'failed').length;
    const pending = transactions.filter(t => t.status === 'pending').length;
    const totalRevenue = transactions
        .filter(t => t.status === 'completed')
        .reduce((sum, t) => sum + t.total_amount, 0);
    
    document.getElementById('completedCount').textContent = completed;
    document.getElementById('failedCount').textContent = failed;
    document.getElementById('pendingCount').textContent = pending;
    document.getElementById('totalRevenue').textContent = `$${totalRevenue.toFixed(2)}`;
}

// Apply filters
function applyFilters() {
    currentFilters = {
        status: document.getElementById('statusFilter').value,
        payment_method: document.getElementById('paymentMethodFilter').value,
        date_from: document.getElementById('dateFrom').value,
        date_to: document.getElementById('dateTo').value,
        customer_search: document.getElementById('customerSearch').value
    };
    
    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key] || currentFilters[key] === 'all') {
            delete currentFilters[key];
        }
    });
    
    loadTransactions(1);
}

// Clear filters
function clearFilters() {
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('paymentMethodFilter').value = 'all';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    document.getElementById('customerSearch').value = '';
    
    currentFilters = {};
    loadTransactions(1);
}

// Refresh transactions
function refreshTransactions() {
    loadTransactions(currentPage);
}

// Export transactions
function exportTransactions() {
    // TODO: Implement export functionality
    alert('Export functionality coming soon!');
}

// Show loading state
function showLoading() {
    const tbody = document.getElementById('transactionsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-search me-2"></i>Loading transactions...
                    </div>
                </div>
            </td>
        </tr>
    `;
}

// Hide loading state
function hideLoading() {
    // Loading is hidden when table is rendered
}

// Show error message
function showError(message) {
    const tbody = document.getElementById('transactionsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <div class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle fa-3x"></i>
                    </div>
                    <div class="text-danger">
                        <h6 class="text-danger">Error Loading Transactions</h6>
                        <p class="mb-0 text-muted">${message}</p>
                    </div>
                    <button class="btn btn-outline-primary btn-sm mt-3" onclick="refreshTransactions()">
                        <i class="fas fa-sync-alt me-1"></i>Try Again
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// View transaction details
async function viewTransactionDetails(transactionId) {
    try {
        const response = await fetch(`/sales/api/transaction/${transactionId}`);
        const data = await response.json();

        if (data.success) {
            renderTransactionDetails(data.transaction);
            const modal = new bootstrap.Modal(document.getElementById('transactionDetailsModal'));
            modal.show();
        } else {
            alert('Failed to load transaction details: ' + data.message);
        }
    } catch (error) {
        console.error('Error loading transaction details:', error);
        alert('Error loading transaction details: ' + error.message);
    }
}

// Render transaction details in modal
function renderTransactionDetails(transaction) {
    const modalBody = document.getElementById('transactionDetailsBody');
    const modalActions = document.getElementById('transactionActions');

    // Format payment methods with detailed breakdown
    let paymentMethodsHTML = '';

    // Check if we have detailed payments from the transaction list
    const listTransaction = transactionsData.find(t => t.id === transactionId);

    if (listTransaction && listTransaction.detailed_payments && listTransaction.detailed_payments.length > 0) {
        // Use detailed payments from the list data
        paymentMethodsHTML = formatDetailedPaymentMethods(listTransaction.detailed_payments);
    } else if (transaction.payment_info.methods && transaction.payment_info.methods.length > 0) {
        // Use payment methods from transaction details
        paymentMethodsHTML = transaction.payment_info.methods.map(method => {
            let methodHTML = `
                <div class="payment-method-item p-2 mb-2 border rounded">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="${getPaymentMethodIcon(method.type)} me-2"></i>
                            <strong>${method.displayType || method.type}</strong>
                        </div>
                        <span class="fw-bold text-success">$${method.amount.toFixed(2)}</span>
                    </div>
            `;

            // Add customer info for store credit
            if (method.customerName) {
                methodHTML += `
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>Customer: ${method.customerName}
                        </small>
                    </div>
                `;
            }

            methodHTML += `</div>`;
            return methodHTML;
        }).join('');
    } else {
        // Fallback to basic payment method
        paymentMethodsHTML = `
            <div class="payment-method-item p-2 border rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="${getPaymentMethodIcon(transaction.payment_info.method)} me-2"></i>
                        <strong>${transaction.payment_info.method}</strong>
                    </div>
                    <span class="fw-bold text-success">$${transaction.total_amount.toFixed(2)}</span>
                </div>
            </div>
        `;
    }

    // Format items
    const itemsHTML = transaction.items.map(item => {
        return `
            <tr>
                <td>${item.title}</td>
                <td>${item.quantity}</td>
                <td>$${item.price.toFixed(2)}</td>
                <td>$${(item.quantity * item.price).toFixed(2)}</td>
            </tr>
        `;
    }).join('');

    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Transaction Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${transaction.id}</td></tr>
                    <tr><td><strong>Date:</strong></td><td>${new Date(transaction.timestamp).toLocaleString()}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>${getStatusBadge(transaction.status)}</td></tr>
                    <tr><td><strong>Total:</strong></td><td class="fw-bold">$${transaction.total_amount.toFixed(2)}</td></tr>
                    <tr><td><strong>Employee:</strong></td><td>${transaction.employee_name}</td></tr>
                    <tr><td><strong>Till:</strong></td><td>${transaction.till_info.location}</td></tr>
                </table>

                <h6>Customer Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${transaction.customer_info.name}</td></tr>
                    <tr><td><strong>Email:</strong></td><td>${transaction.customer_info.email || 'N/A'}</td></tr>
                    <tr><td><strong>ID:</strong></td><td>${transaction.customer_info.id || 'N/A'}</td></tr>
                </table>
            </div>

            <div class="col-md-6">
                <h6 class="text-white mb-3">
                    <i class="fas fa-credit-card me-2"></i>Payment Information
                </h6>
                <div class="payment-summary">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted">Total Amount:</span>
                        <span class="h5 text-success mb-0">$${transaction.total_amount.toFixed(2)}</span>
                    </div>
                    <hr class="my-3" style="border-color: rgba(255, 255, 255, 0.2);">
                    <div class="payment-methods">
                        ${paymentMethodsHTML}
                    </div>
                </div>

                ${transaction.shopify_info.order_id ? `
                    <h6 class="mt-3">Shopify Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Order ID:</strong></td><td>${transaction.shopify_info.order_id}</td></tr>
                        <tr><td><strong>Order URL:</strong></td><td>
                            <a href="${transaction.shopify_info.order_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                View in Shopify <i class="fas fa-external-link-alt"></i>
                            </a>
                        </td></tr>
                    </table>
                ` : ''}

                ${transaction.revert_info.store_credit_reverted || transaction.revert_info.inventory_reverted ? `
                    <h6 class="mt-3">Revert History</h6>
                    <table class="table table-sm">
                        ${transaction.revert_info.store_credit_reverted ? `
                            <tr><td><strong>Store Credit:</strong></td><td>
                                <span class="badge bg-warning">Reverted</span>
                                <small class="text-muted d-block">${new Date(transaction.revert_info.store_credit_revert_date).toLocaleString()}</small>
                            </td></tr>
                        ` : ''}
                        ${transaction.revert_info.inventory_reverted ? `
                            <tr><td><strong>Inventory:</strong></td><td>
                                <span class="badge bg-warning">Reverted</span>
                                <small class="text-muted d-block">${new Date(transaction.revert_info.inventory_revert_date).toLocaleString()}</small>
                            </td></tr>
                        ` : ''}
                    </table>
                ` : ''}
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>Items</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHTML}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        ${transaction.error_message ? `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${transaction.error_message}
                    </div>
                </div>
            </div>
        ` : ''}
    `;

    // Set up action buttons
    let actionsHTML = '';
    if (transaction.status === 'completed' && transaction.shopify_info.order_id) {
        actionsHTML = `
            ${!transaction.revert_info.store_credit_reverted ? `
                <button class="btn btn-warning me-2" onclick="revertStoreCredit('${transaction.id}')">
                    <i class="fas fa-credit-card"></i> Revert Store Credit
                </button>
            ` : ''}
            ${!transaction.revert_info.inventory_reverted ? `
                <button class="btn btn-warning me-2" onclick="revertInventory('${transaction.id}')">
                    <i class="fas fa-boxes"></i> Revert Inventory
                </button>
            ` : ''}
            <button class="btn btn-danger" onclick="revertFullTransaction('${transaction.id}')">
                <i class="fas fa-undo-alt"></i> Full Revert
            </button>
        `;
    }

    modalActions.innerHTML = actionsHTML;
}

// Revert store credit
async function revertStoreCredit(transactionId) {
    if (!confirm('Are you sure you want to revert the store credit for this transaction? This will refund the store credit to the customer.')) {
        return;
    }

    try {
        const response = await fetch(`/sales/api/transaction/${transactionId}/revert-store-credit`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            alert('Store credit reverted successfully!');
            refreshTransactions();
            // Close modal and refresh details if still open
            const modal = bootstrap.Modal.getInstance(document.getElementById('transactionDetailsModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            alert('Failed to revert store credit: ' + data.message);
        }
    } catch (error) {
        console.error('Error reverting store credit:', error);
        alert('Error reverting store credit: ' + error.message);
    }
}

// Revert inventory
async function revertInventory(transactionId) {
    if (!confirm('Are you sure you want to revert the inventory for this transaction? This will restore the inventory quantities.')) {
        return;
    }

    try {
        const response = await fetch(`/sales/api/transaction/${transactionId}/revert-inventory`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            alert('Inventory reverted successfully!');
            refreshTransactions();
            // Close modal and refresh details if still open
            const modal = bootstrap.Modal.getInstance(document.getElementById('transactionDetailsModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            alert('Failed to revert inventory: ' + data.message);
        }
    } catch (error) {
        console.error('Error reverting inventory:', error);
        alert('Error reverting inventory: ' + error.message);
    }
}

// Revert full transaction
async function revertFullTransaction(transactionId) {
    if (!confirm('Are you sure you want to fully revert this transaction? This will revert both store credit and inventory.')) {
        return;
    }

    try {
        const response = await fetch(`/sales/api/transaction/${transactionId}/revert-full`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            alert('Transaction fully reverted successfully!');
            refreshTransactions();
            // Close modal and refresh details if still open
            const modal = bootstrap.Modal.getInstance(document.getElementById('transactionDetailsModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            alert('Failed to revert transaction: ' + data.message);
        }
    } catch (error) {
        console.error('Error reverting transaction:', error);
        alert('Error reverting transaction: ' + error.message);
    }
}
