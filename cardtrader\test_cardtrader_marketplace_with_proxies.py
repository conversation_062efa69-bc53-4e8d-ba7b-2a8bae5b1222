# Optimized script to pull marketplace listings from CardTrader API with proxy rotation
import asyncio
import aiohttp
import time
import random
import motor.motor_asyncio
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
import sys
import argparse
import json
import os
from tqdm import tqdm
import signal
import math
import requests
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"cardtrader_marketplace_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Connect to MongoDB
mongo_uri = '*******************************************************************'
client = MongoClient(mongo_uri)
cardtrader_db = client['cardtrader']

# Async MongoDB client
motor_client = motor.motor_asyncio.AsyncIOMotorClient(mongo_uri)
motor_db = motor_client['cardtrader']

# CardTrader API configuration
API_BASE_URL = "https://api.cardtrader.com/api/v2"
# CardTrader API token
API_TOKEN = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# Proxy configuration
PROXY_URL = "https://proxy.webshare.io/api/v2/proxy/list/download/mppaungsmfcdtflcalmqswojioauylnrbmfjimqd/-/any/username/direct/-/"
REQUESTS_PER_PROXY = 350
RATE_LIMIT_DURATION = 120  # 2 minutes cooldown for rate limited proxies
USE_PROXIES = True  # Set to False to disable proxy usage

# Global variables for statistics and control
stats = {
    "total_processed": 0,
    "successful_requests": 0,
    "failed_requests": 0,
    "rate_limited_requests": 0,
    "cloudflare_blocks": 0,
    "total_listings_found": 0,
    "start_time": None,
    "last_status_time": None,
    "processed_ids": set(),  # Keep track of processed IDs to avoid duplicates
    "running": True  # Flag to control graceful shutdown
}

# Proxy management
proxies = []
proxy_lock = threading.Lock()
proxy_status = {}

class ProxyStatus:
    def __init__(self):
        self.requests = 0
        self.rate_limited_until = 0
        self.last_used = 0
        self.errors = 0
        self.cloudflare_blocks = 0

def load_proxies():
    """Load proxies from the proxy provider URL"""
    try:
        logger.info("Loading proxies from provider...")
        response = requests.get(PROXY_URL)
        response.raise_for_status()
        proxy_list = []
        for line in response.text.splitlines():
            if not line.strip():
                continue
            try:
                parts = line.strip().split(':')
                if len(parts) == 4:
                    ip, port, username, password = parts
                    proxy = {
                        'http': f"http://{username}:{password}@{ip}:{port}",
                        'https': f"http://{username}:{password}@{ip}:{port}"
                    }
                    proxy_list.append(proxy)
                    # Initialize proxy status
                    proxy_status[proxy['http']] = ProxyStatus()
            except Exception as e:
                logger.debug(f"Error parsing proxy: {e}")
                continue
        logger.info(f"Loaded {len(proxy_list)} proxies")
        return proxy_list
    except Exception as e:
        logger.error(f"Error loading proxies: {e}")
        return []

def get_available_proxy():
    """Get an available proxy from the pool"""
    if not USE_PROXIES or not proxies:
        return None
        
    current_time = time.time()
    with proxy_lock:
        # Filter available proxies
        available = [
            p for p in proxies
            if (proxy_status[p['http']].requests < REQUESTS_PER_PROXY and
                proxy_status[p['http']].rate_limited_until <= current_time and
                proxy_status[p['http']].errors < 5 and
                proxy_status[p['http']].cloudflare_blocks < 3)
        ]
        
        if not available:
            # Reset all proxies that have cooled down
            for proxy in proxies:
                status = proxy_status[proxy['http']]
                if current_time - status.last_used >= RATE_LIMIT_DURATION:
                    status.requests = 0
                    status.rate_limited_until = 0
                    
                    # Only reset errors and cloudflare blocks if it's been a while
                    if current_time - status.last_used >= RATE_LIMIT_DURATION * 5:
                        status.errors = max(0, status.errors - 1)
                        status.cloudflare_blocks = max(0, status.cloudflare_blocks - 1)
            
            # Try again
            available = [
                p for p in proxies
                if (proxy_status[p['http']].requests < REQUESTS_PER_PROXY and
                    proxy_status[p['http']].rate_limited_until <= current_time and
                    proxy_status[p['http']].errors < 5 and
                    proxy_status[p['http']].cloudflare_blocks < 3)
            ]
            
            if not available:
                # If still no proxies, use any that aren't completely banned
                available = [
                    p for p in proxies
                    if proxy_status[p['http']].cloudflare_blocks < 5
                ]
                
                if not available:
                    logger.warning("No available proxies! Using direct connection.")
                    return None
        
        # Sort by least recently used and least used
        proxy = min(available, key=lambda p: (
            proxy_status[p['http']].last_used,
            proxy_status[p['http']].requests
        ))
        
        # Update proxy status
        status = proxy_status[proxy['http']]
        status.requests += 1
        status.last_used = current_time
        
        return proxy

def mark_proxy_rate_limited(proxy):
    """Mark a proxy as rate limited"""
    if not proxy:
        return
        
    with proxy_lock:
        status = proxy_status[proxy['http']]
        status.rate_limited_until = time.time() + RATE_LIMIT_DURATION
        status.requests = REQUESTS_PER_PROXY

def mark_proxy_cloudflare_blocked(proxy):
    """Mark a proxy as blocked by Cloudflare"""
    if not proxy:
        return
        
    with proxy_lock:
        status = proxy_status[proxy['http']]
        status.cloudflare_blocks += 1
        status.rate_limited_until = time.time() + RATE_LIMIT_DURATION * 2  # Longer cooldown
        status.requests = REQUESTS_PER_PROXY
        stats["cloudflare_blocks"] += 1

def mark_proxy_error(proxy):
    """Mark a proxy as having an error"""
    if not proxy:
        return
        
    with proxy_lock:
        proxy_status[proxy['http']].errors += 1

def is_cloudflare_block(response_text):
    """Check if the response is a Cloudflare block"""
    return (
        "cloudflare" in response_text.lower() and 
        ("ray id:" in response_text.lower() or "you are being rate limited" in response_text.lower())
    )

# Adaptive rate limiting with exponential backoff and Cloudflare protection
class AdaptiveRateLimiter:
    def __init__(self, initial_rate=3.0, min_rate=0.5, max_rate=8.0):
        self.rate_limit = initial_rate  # requests per second
        self.min_rate = min_rate
        self.max_rate = max_rate
        self.tokens = initial_rate
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        self.consecutive_successes = 0
        self.consecutive_failures = 0
        self.cloudflare_blocks = 0
        self.backoff_until = 0  # Timestamp until which we should back off
        self.global_backoff_factor = 1.0  # Factor to multiply all backoff times by
    
    async def acquire(self):
        async with self.lock:
            now = time.time()
            
            # Check if we're in a backoff period
            if now < self.backoff_until:
                wait_time = self.backoff_until - now
                logger.debug(f"In backoff period, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                now = time.time()
            
            # Refill tokens based on time passed
            time_passed = now - self.last_update
            self.tokens = min(self.rate_limit, self.tokens + time_passed * self.rate_limit)
            self.last_update = now
            
            if self.tokens < 1:
                # Not enough tokens, calculate sleep time
                sleep_time = (1 - self.tokens) / self.rate_limit
                logger.debug(f"Rate limit reached, waiting {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
                self.tokens = 0
                self.last_update = time.time()
            else:
                self.tokens -= 1
                
            # Add a small random delay to avoid synchronized requests
            jitter = random.uniform(0, 0.5)
            await asyncio.sleep(jitter)
    
    def success(self):
        """Record a successful request"""
        self.consecutive_successes += 1
        self.consecutive_failures = 0
        
        # Increase rate limit after 20 consecutive successes (more conservative)
        if self.consecutive_successes >= 20:
            old_rate = self.rate_limit
            # Increase by smaller increments
            self.rate_limit = min(self.rate_limit + 0.2, self.max_rate)
            if old_rate != self.rate_limit:
                logger.info(f"Increasing rate limit to {self.rate_limit:.1f} req/sec after {self.consecutive_successes} consecutive successes")
            self.consecutive_successes = 0
            
            # If we've been successful for a while, gradually reduce the global backoff factor
            if self.global_backoff_factor > 1.0:
                self.global_backoff_factor = max(1.0, self.global_backoff_factor * 0.9)
    
    def failure(self, retry_after=None, is_cloudflare=False):
        """Record a failed request due to rate limiting"""
        self.consecutive_failures += 1
        self.consecutive_successes = 0
        
        # If this is a Cloudflare block, handle it specially
        if is_cloudflare:
            self.cloudflare_blocks += 1
            logger.warning(f"Detected Cloudflare rate limit block ({self.cloudflare_blocks} total)")
            
            # Increase the global backoff factor
            self.global_backoff_factor = min(10.0, self.global_backoff_factor * 1.5)
            
            # Implement a longer backoff for Cloudflare blocks
            backoff_time = min(30 * self.cloudflare_blocks, 600)  # Cap at 10 minutes
            self.backoff_until = time.time() + backoff_time
            logger.warning(f"Cloudflare block detected! Backing off for {backoff_time:.2f} seconds")
            
            # Drastically reduce the rate limit
            old_rate = self.rate_limit
            self.rate_limit = max(self.rate_limit * 0.5, self.min_rate)
            logger.warning(f"Reducing rate limit to {self.rate_limit:.1f} req/sec due to Cloudflare block")
            
            return
        
        # If we have a Retry-After header, use that
        if retry_after:
            # Apply the global backoff factor to the retry-after time
            adjusted_retry = retry_after * self.global_backoff_factor
            self.backoff_until = time.time() + adjusted_retry
            logger.info(f"Backing off for {adjusted_retry:.2f} seconds based on Retry-After header (factor: {self.global_backoff_factor:.1f})")
            return
        
        # Decrease rate limit after 3 consecutive failures
        if self.consecutive_failures >= 3:
            old_rate = self.rate_limit
            self.rate_limit = max(self.rate_limit - 0.5, self.min_rate)
            if old_rate != self.rate_limit:
                logger.info(f"Decreasing rate limit to {self.rate_limit:.1f} req/sec after {self.consecutive_failures} consecutive failures")
            
            # Implement exponential backoff with global factor
            backoff_time = min(2 ** (self.consecutive_failures - 2), 120) * self.global_backoff_factor
            self.backoff_until = time.time() + backoff_time
            logger.info(f"Backing off for {backoff_time:.2f} seconds (factor: {self.global_backoff_factor:.1f})")
            
            # Reset consecutive failures after implementing backoff
            if self.consecutive_failures >= 5:
                self.consecutive_failures = 0

# Global rate limiter
rate_limiter = None  # Will be initialized in main()

async def get_matched_blueprints_with_tcgplayer_ids(limit=0, random_sample=False, min_age_days=None):
    """Get blueprints from the matchedIds collection with valid TCGPlayer IDs"""
    logger.info(f"Fetching matched blueprints with valid TCGPlayer IDs...")
    
    # Build the query for matchedIds collection
    query = {
        "tcg_player_id": {"$exists": True, "$ne": None},
        "blueprint_id": {"$exists": True, "$ne": None}
    }
    
    # Add age filter if specified
    if min_age_days:
        cutoff_date = datetime.now() - timedelta(days=min_age_days)
        # If we have a last_updated field, use it
        if await motor_db.marketprices.count_documents({"last_updated": {"$exists": True}}) > 0:
            query["$or"] = [
                {"_id": {"$nin": await motor_db.marketprices.distinct("_id", {"last_updated": {"$gte": cutoff_date}})}},
                {"_id": {"$nin": await motor_db.marketprices.distinct("_id")}}
            ]
    
    # Count total matching records
    total_records = await motor_db.matchedIds.count_documents(query)
    logger.info(f"Found {total_records} matched blueprints with valid TCGPlayer IDs")
    
    if total_records == 0:
        return []
    
    # Get the records
    if limit > 0 and limit < total_records:
        if random_sample:
            # Get random sample using aggregation
            pipeline = [
                {"$match": query},
                {"$sample": {"size": limit}}
            ]
            cursor = motor_db.matchedIds.aggregate(pipeline)
            matched_records = await cursor.to_list(length=limit)
            logger.info(f"Selected {len(matched_records)} random matched blueprints")
        else:
            # Get first N records
            cursor = motor_db.matchedIds.find(query).limit(limit)
            matched_records = await cursor.to_list(length=limit)
            logger.info(f"Selected first {len(matched_records)} matched blueprints")
    else:
        # Get all matching records
        cursor = motor_db.matchedIds.find(query)
        matched_records = await cursor.to_list(length=total_records)
        logger.info(f"Selected all {len(matched_records)} matching blueprints")
    
    return matched_records

def extract_market_summary(listings):
    """Extract summary market data from listings"""
    if not listings:
        return {
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None,
                "currency": "EUR"
            }
        }
    
    # Calculate total quantity
    total_quantity = sum(listing.get("quantity", 0) for listing in listings)
    
    # Extract prices (in cents)
    prices = []
    for listing in listings:
        price_info = listing.get("price", {})
        if isinstance(price_info, dict) and "cents" in price_info:
            # Convert cents to dollars/euros for easier reading
            price = float(price_info["cents"]) / 100
            prices.append(price)
    
    # Calculate price statistics
    price_data = {
        "min": min(prices) if prices else None,
        "max": max(prices) if prices else None,
        "avg": sum(prices) / len(prices) if prices else None,
        "currency": listings[0].get("price", {}).get("currency", "EUR") if listings else "EUR"
    }
    
    return {
        "total_quantity": total_quantity,
        "price_data": price_data
    }

async def fetch_marketplace_listings(session, blueprint_id, max_retries=3):
    """Fetch marketplace listings for a specific blueprint ID using aiohttp with proxy rotation"""
    url = f"{API_BASE_URL}/marketplace/products"
    params = {"blueprint_id": blueprint_id}
    
    for retry in range(max_retries):
        # Get a proxy from the pool
        proxy = get_available_proxy()
        proxy_url = None
        
        if proxy:
            # Convert proxy dict to aiohttp proxy format
            proxy_url = proxy['http']
            logger.debug(f"Using proxy: {proxy_url.split('@')[-1]}")
        
        try:
            # Acquire token from rate limiter
            await rate_limiter.acquire()
            
            # Make the API request
            request_kwargs = {
                'params': params,
                'headers': HEADERS,
                'timeout': aiohttp.ClientTimeout(total=15)
            }
            
            if proxy_url:
                request_kwargs['proxy'] = proxy_url
            
            async with session.get(url, **request_kwargs) as response:
                if response.status == 200:
                    data = await response.json()
                    # The response is an object with blueprint IDs as keys and arrays of products as values
                    listings = data.get(str(blueprint_id), [])
                    logger.debug(f"Found {len(listings)} marketplace listings for blueprint ID: {blueprint_id}")
                    rate_limiter.success()
                    stats["successful_requests"] += 1
                    stats["total_listings_found"] += len(listings)
                    return listings
                elif response.status == 429:
                    # Rate limited
                    stats["rate_limited_requests"] += 1
                    error_text = await response.text()
                    logger.warning(f"Rate limited: {error_text[:200]}...")
                    
                    # Check if this is a Cloudflare block
                    cloudflare_blocked = is_cloudflare_block(error_text)
                    
                    # Mark the proxy as rate limited or cloudflare blocked
                    if cloudflare_blocked:
                        mark_proxy_cloudflare_blocked(proxy)
                        rate_limiter.failure(is_cloudflare=True)
                    else:
                        mark_proxy_rate_limited(proxy)
                        
                        # Check for Retry-After header
                        retry_after = response.headers.get('Retry-After')
                        if retry_after:
                            try:
                                retry_after = float(retry_after)
                            except (ValueError, TypeError):
                                retry_after = None
                        
                        rate_limiter.failure(retry_after)
                    
                    # If this is the last retry, give up
                    if retry == max_retries - 1:
                        logger.error(f"Max retries reached for blueprint ID: {blueprint_id}")
                        stats["failed_requests"] += 1
                        return []
                    
                    # Otherwise, retry after a delay
                    backoff_time = 2 ** retry
                    logger.info(f"Retry {retry+1}/{max_retries} for blueprint ID: {blueprint_id} after {backoff_time}s")
                    await asyncio.sleep(backoff_time)
                else:
                    error_text = await response.text()
                    logger.error(f"API request failed with status code {response.status}: {error_text[:200]}...")
                    mark_proxy_error(proxy)
                    stats["failed_requests"] += 1
                    return []
        except aiohttp.ClientProxyConnectionError:
            logger.warning(f"Proxy connection error for blueprint ID {blueprint_id}")
            mark_proxy_error(proxy)
            # If this is the last retry, give up
            if retry == max_retries - 1:
                logger.error(f"Max retries reached for blueprint ID: {blueprint_id}")
                stats["failed_requests"] += 1
                return []
        except asyncio.TimeoutError:
            logger.warning(f"Timeout fetching marketplace listings for blueprint ID {blueprint_id}")
            mark_proxy_error(proxy)
            # If this is the last retry, give up
            if retry == max_retries - 1:
                logger.error(f"Max retries reached for blueprint ID: {blueprint_id}")
                stats["failed_requests"] += 1
                return []
            
            # Otherwise, retry after a delay
            backoff_time = 2 ** retry
            logger.info(f"Retry {retry+1}/{max_retries} for blueprint ID: {blueprint_id} after {backoff_time}s")
            await asyncio.sleep(backoff_time)
        except Exception as e:
            logger.error(f"Error fetching marketplace listings for blueprint ID {blueprint_id}: {str(e)}")
            mark_proxy_error(proxy)
            stats["failed_requests"] += 1
            return []
    
    logger.error(f"All retries failed for blueprint ID {blueprint_id}")
    return []

async def save_to_marketprices(matched_record, listings):
    """Save the marketplace listings summary to the marketprices collection using motor"""
    blueprint_id = matched_record.get("blueprint_id")
    tcg_player_id = matched_record.get("tcg_player_id")
    
    if not listings:
        logger.debug(f"No listings to save for blueprint ID: {blueprint_id}")
        # Still save a record with empty listings
        document = {
            "blueprint_id": blueprint_id,
            "tcg_player_id": tcg_player_id,
            "total_quantity": 0,
            "price_data": {
                "min": None,
                "max": None,
                "avg": None,
                "currency": "EUR"
            },
            "listings_count": 0,
            "listings": [],
            "last_updated": datetime.now()
        }
    else:
        # Extract market summary data
        market_summary = extract_market_summary(listings)
        
        # Create a document to insert
        document = {
            "blueprint_id": blueprint_id,
            "tcg_player_id": tcg_player_id,
            "total_quantity": market_summary["total_quantity"],
            "price_data": market_summary["price_data"],
            "listings_count": len(listings),
            "listings": listings,
            "last_updated": datetime.now()
        }
    
    try:
        # Insert or update the document in the marketprices collection
        result = await motor_db.marketprices.update_one(
            {"blueprint_id": blueprint_id},
            {"$set": document},
            upsert=True
        )
        
        if result.upserted_id:
            logger.debug(f"Inserted new document for blueprint ID: {blueprint_id}")
        else:
            logger.debug(f"Updated existing document for blueprint ID: {blueprint_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error saving to marketprices collection: {str(e)}")
        return False

async def process_blueprint(session, matched_record, semaphore):
    """Process a single blueprint with rate limiting and retries"""
    blueprint_id = matched_record.get("blueprint_id")
    tcg_player_id = matched_record.get("tcg_player_id")
    
    # Skip if we've already processed this ID
    if blueprint_id in stats["processed_ids"]:
        logger.debug(f"Skipping already processed blueprint ID: {blueprint_id}")
        return False
    
    # Add to processed IDs
    stats["processed_ids"].add(blueprint_id)
    
    try:
        async with semaphore:
            # Check if we should continue running
            if not stats["running"]:
                return False
            
            # Fetch marketplace listings
            listings = await fetch_marketplace_listings(session, blueprint_id)
            
            # Save to marketprices collection
            success = await save_to_marketprices(matched_record, listings)
            
            # Update stats
            stats["total_processed"] += 1
            
            # Print progress every 10 seconds
            now = time.time()
            if stats["last_status_time"] is None or now - stats["last_status_time"] >= 10:
                elapsed = now - stats["start_time"]
                processed = stats["total_processed"]
                rate = processed / elapsed if elapsed > 0 else 0
                
                logger.info(f"Progress: {processed} blueprints processed ({rate:.2f} blueprints/sec)")
                logger.info(f"Success: {stats['successful_requests']}, Failed: {stats['failed_requests']}, Rate Limited: {stats['rate_limited_requests']}, Cloudflare Blocks: {stats['cloudflare_blocks']}")
                logger.info(f"Total listings found: {stats['total_listings_found']}")
                
                if USE_PROXIES and proxies:
                    active_proxies = sum(1 for p in proxies if proxy_status[p['http']].cloudflare_blocks < 3)
                    logger.info(f"Active proxies: {active_proxies}/{len(proxies)}")
                
                stats["last_status_time"] = now
            
            return success
    except Exception as e:
        logger.error(f"Error processing blueprint ID {blueprint_id}: {str(e)}")
        stats["failed_requests"] += 1
        return False

async def process_batch(session, matched_records, concurrency=10, batch_delay=0):
    """Process a batch of blueprints with controlled concurrency"""
    semaphore = asyncio.Semaphore(concurrency)
    tasks = []
    
    # Create tasks for all blueprints in the batch
    for matched_record in matched_records:
        if not stats["running"]:
            break
        
        task = asyncio.create_task(process_blueprint(session, matched_record, semaphore))
        tasks.append(task)
    
    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Count successful tasks
    successful = sum(1 for r in results if r is True)
    
    # Wait between batches if specified
    if batch_delay > 0:
        logger.info(f"Waiting {batch_delay} seconds between batches")
        await asyncio.sleep(batch_delay)
    
    return successful

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    logger.info("Received interrupt signal, shutting down gracefully...")
    stats["running"] = False

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Optimized script to pull marketplace listings from CardTrader API with proxy rotation')
    parser.add_argument('--blueprints', type=int, default=0,
                        help='Number of blueprints to process (default: 0 for all)')
    parser.add_argument('--concurrency', type=int, default=10,
                        help='Maximum number of concurrent requests (default: 10)')
    parser.add_argument('--rate-limit', type=float, default=3.0,
                        help='Initial API rate limit in requests per second (default: 3.0)')
    parser.add_argument('--batch-size', type=int, default=100,
                        help='Batch size for processing blueprints (default: 100)')
    parser.add_argument('--batch-delay', type=int, default=2,
                        help='Delay in seconds between batches (default: 2)')
    parser.add_argument('--random', action='store_true',
                        help='Select random blueprints instead of sequential')
    parser.add_argument('--min-age-days', type=int, default=None,
                        help='Only process records not updated in the last N days')
    parser.add_argument('--threads', type=int, default=4,
                        help='Number of threads for MongoDB operations (default: 4)')
    parser.add_argument('--no-proxies', action='store_true',
                        help='Disable proxy usage')
    parser.add_argument('--max-retries', type=int, default=3,
                        help='Maximum number of retries for failed requests (default: 3)')
    return parser.parse_args()

async def main_async():
    # Parse command line arguments
    args = parse_arguments()
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Initialize rate limiter
    global rate_limiter
    rate_limiter = AdaptiveRateLimiter(
        initial_rate=args.rate_limit,
        min_rate=0.5,
        max_rate=8.0
    )
    
    # Initialize stats
    stats["start_time"] = time.time()
    stats["last_status_time"] = None
    stats["running"] = True
    
    # Set proxy usage based on arguments
    global USE_PROXIES
    USE_PROXIES = not args.no_proxies
    
    logger.info(f"Starting CardTrader marketplace data collection for matched IDs with valid TCGPlayer IDs")
    logger.info(f"Configuration: {args.blueprints} blueprints, {args.concurrency} concurrency, {args.batch_size} batch size, {args.rate_limit} req/sec, {args.threads} threads")
    logger.info(f"Proxy usage: {'Enabled' if USE_PROXIES else 'Disabled'}")
    
    # Load proxies if enabled
    global proxies
    if USE_PROXIES:
        proxies = load_proxies()
        if not proxies:
            logger.warning("No proxies loaded. Continuing without proxies.")
            USE_PROXIES = False
    
    # Create marketprices collection if it doesn't exist
    if 'marketprices' not in cardtrader_db.list_collection_names():
        logger.info("Creating marketprices collection")
        cardtrader_db.create_collection('marketprices')
    
    # Get matched blueprints with valid TCGPlayer IDs
    matched_records = await get_matched_blueprints_with_tcgplayer_ids(
        limit=args.blueprints,
        random_sample=args.random,
        min_age_days=args.min_age_days
    )
    
    if not matched_records:
        logger.error("No matched blueprints found with valid TCGPlayer IDs. Exiting.")
        return
    
    total_blueprints = len(matched_records)
    total_batches = math.ceil(total_blueprints / args.batch_size)
    
    # Process blueprints in batches
    total_processed = 0
    
    # Create a ClientSession that will be used for all requests
    connector = aiohttp.TCPConnector(limit=args.concurrency, ssl=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        # Process blueprints in batches to avoid memory issues
        for i in range(0, total_blueprints, args.batch_size):
            if not stats["running"]:
                logger.info("Graceful shutdown initiated, stopping processing")
                break
            
            batch = matched_records[i:i+args.batch_size]
            logger.info(f"Processing batch {i//args.batch_size + 1}/{total_batches} ({len(batch)} blueprints)")
            
            batch_start_time = time.time()
            processed = await process_batch(
                session,
                batch,
                concurrency=args.concurrency,
                batch_delay=args.batch_delay
            )
            batch_end_time = time.time()
            
            total_processed += processed
            
            # Log progress
            batch_duration = batch_end_time - batch_start_time
            logger.info(f"Batch completed in {batch_duration:.2f} seconds")
            logger.info(f"Progress: {total_processed}/{total_blueprints} blueprints processed ({total_processed/total_blueprints*100:.1f}%)")
            
            # Reload proxies periodically if enabled
            if USE_PROXIES and i > 0 and i % (args.batch_size * 5) == 0:
                new_proxies = load_proxies()
                if new_proxies:
                    with proxy_lock:
                        # Keep existing proxy status for proxies that are still in the list
                        for proxy in new_proxies:
                            if proxy['http'] not in proxy_status:
                                proxy_status[proxy['http']] = ProxyStatus()
                        proxies = new_proxies
                        logger.info(f"Reloaded proxies, now have {len(proxies)} proxies")
    
    end_time = time.time()
    duration = end_time - stats["start_time"]
    
    # Calculate final statistics
    success_rate = stats["successful_requests"] / (stats["total_processed"] or 1) * 100
    failure_rate = stats["failed_requests"] / (stats["total_processed"] or 1) * 100
    rate_limited_rate = stats["rate_limited_requests"] / (stats["total_processed"] or 1) * 100
    
    logger.info(f"Completed in {duration:.2f} seconds")
    logger.info(f"Processed {stats['total_processed']} blueprints")
    logger.info(f"Found {stats['total_listings_found']} total marketplace listings")
    logger.info(f"Success rate: {success_rate:.1f}%, Failure rate: {failure_rate:.1f}%, Rate limited: {rate_limited_rate:.1f}%")
    logger.info(f"Cloudflare blocks: {stats['cloudflare_blocks']}")
    
    if stats["total_processed"] > 0:
        logger.info(f"Average processing time: {duration/stats['total_processed']:.2f} seconds per blueprint")
        logger.info(f"Processing rate: {stats['total_processed']/duration:.2f} blueprints per second")
        
        # Estimate time for all blueprints
        total_matched_blueprints = await motor_db.matchedIds.count_documents({
            "tcg_player_id": {"$exists": True, "$ne": None},
            "blueprint_id": {"$exists": True, "$ne": None}
        })
        
        if total_matched_blueprints > stats["total_processed"]:
            estimated_hours = (total_matched_blueprints - stats["total_processed"]) / (stats["total_processed"]/duration) / 3600
            logger.info(f"Estimated time for remaining {total_matched_blueprints - stats['total_processed']} blueprints: {estimated_hours:.2f} hours")
    
    # Count documents in marketprices collection
    marketprices_count = cardtrader_db.marketprices.count_documents({})
    logger.info(f"Total documents in marketprices collection: {marketprices_count}")

def main():
    """Entry point for the script"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unhandled exception: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()
