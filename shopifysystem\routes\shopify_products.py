# Previous imports remain unchanged...
import logging
from flask import Blueprint, render_template, request, jsonify, make_response, current_app, Response, session
from flask_login import login_required, current_user
from pymongo import MongoClient, UpdateOne
import concurrent.futures
import time
from bson import ObjectId
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import json
import io
import csv
from datetime import datetime, timedelta
import asyncio
import aiohttp
from utils.pricing_utils import determine_printing_type, calculate_variant_price
from services.currency_service import currency_service

logger = logging.getLogger(__name__)

# MongoDB Configuration
import os
mongo_uri = os.environ.get('MONGO_URI', 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin')

mongo_client = MongoClient(mongo_uri, maxPoolSize=50)
db = mongo_client[os.environ.get('MONGO_DBNAME', 'test')]
shopify_collection = db['shProducts']
catalog_collection = db['catalog']
user_collection = db['user']
shOrders_collection = db['shOrders']
shCustomers_collection = db['shCustomers']
tcgplayer_key_collection = db['tcgplayerKey']
prices_collection = db['prices']  # Add prices collection
staged_inventory_collection = db['staged_inventory']  # Add staged inventory collection

products_bp = Blueprint('products', __name__, url_prefix='/shopify/products')

# Add condition mapping dictionary
CONDITION_MAPPING = {
    'nm': 'near mint',
    'lp': 'lightly played',
    'mp': 'moderately played',
    'hp': 'heavily played',
    'dm': 'damaged'
}

# Add reverse condition mapping for more robust matching
REVERSE_CONDITION_MAPPING = {
    'near mint': 'nm',
    'near-mint': 'nm',
    'nearmint': 'nm',
    'nm': 'nm',
    'lightly played': 'lp',
    'lightly-played': 'lp',
    'lightlyplayed': 'lp',
    'lp': 'lp',
    'moderately played': 'mp',
    'moderately-played': 'mp',
    'moderatelyplayed': 'mp',
    'mp': 'mp',
    'heavily played': 'hp',
    'heavily-played': 'hp',
    'heavilyplayed': 'hp',
    'hp': 'hp',
    'damaged': 'dm',
    'dm': 'dm'
}

def normalize_condition(condition):
    """Normalize condition string for consistent matching"""
    condition = condition.lower().strip()
    # First try to match against reverse mapping
    normalized = REVERSE_CONDITION_MAPPING.get(condition)
    if normalized:
        return CONDITION_MAPPING[normalized]
    # If not found, return original condition
    return condition

def get_price_from_collection(product_id, subtype_name, price_preference_order):
    """Get price from collection based on preference order"""
    collection_price = prices_collection.find_one({
        'productId': product_id,
        'subTypeName': subtype_name
    })
    if collection_price:
        # Convert collection price to same format as TCGPlayer pricing data
        pricing_data = {
            'lowPrice': collection_price.get('lowPrice'),
            'marketPrice': collection_price.get('marketPrice'),
            'midPrice': collection_price.get('midPrice'),
            'highPrice': collection_price.get('highPrice'),
            'subTypeName': subtype_name
        }
        return pricing_data
    return None

def calculate_new_price(product, variant, custom_stepping, min_price, user_currency, printing_type=None):
    """Calculate new price for a variant using the unified pricing calculator"""
    try:
        username = product.get('username')
        if not username:
            logger.error("No username found in product document")
            return float(variant.get('price', 9999))

        # Get Shopify settings
        from models.shopify_settings_model import ShopifySettings
        shopify_settings = ShopifySettings.objects(username=username).first()
        if not shopify_settings:
            # Use update_one with upsert instead of save to handle race conditions
            ShopifySettings.objects(username=username).update_one(
                upsert=True,
                set__username=username
            )
            shopify_settings = ShopifySettings.objects(username=username).first()

        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return float(variant.get('price', 9999))

        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        headers = {
            'Authorization': f'Bearer {tcgplayer_api_key}',
            'Accept': 'application/json',
        }

        product_id = product.get('productId')
        if not product_id:
            return float(variant.get('price', 9999))

        try:
            # Get TCGPlayer pricing data
            pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_id}"
            pricing_response = requests.get(pricing_url, headers=headers)
            pricing_response.raise_for_status()
            pricing_data = pricing_response.json().get('results', [])

            if not pricing_data:
                return float(variant.get('price', 9999))

            valid_subtypes = [p.get('subTypeName') for p in pricing_data if p.get('subTypeName')]

            # Use specified printing type if provided, otherwise determine automatically
            if printing_type and printing_type in valid_subtypes:
                selected_printing_type = printing_type
            else:
                selected_printing_type = determine_printing_type(variant['title'], valid_subtypes)

            # Find matching price info
            matched_price = next(
                (p for p in pricing_data if p.get('subTypeName', '').lower() == selected_printing_type.lower()),
                next(
                    (p for p in pricing_data if 'holofoil' in p.get('subTypeName', '').lower()),
                    next(
                        (p for p in pricing_data if p.get('subTypeName', '').lower() == 'normal'),
                        None
                    )
                ) if 'cold foil' in selected_printing_type.lower() else None
            )

            if matched_price:
                # Check if all prices are null
                all_null = all(matched_price.get(price_type) is None
                             for price_type in shopify_settings.price_preference_order)
                if all_null:
                    # Try to get price from collection
                    collection_pricing_data = get_price_from_collection(
                        product_id,
                        matched_price['subTypeName'],
                        shopify_settings.price_preference_order
                    )
                    if collection_pricing_data:
                        matched_price = collection_pricing_data

            if matched_price:
                # Extract condition from variant title
                variant_title = variant.get('title', '').lower()

                # Try to match condition from the title
                condition_code = None
                # First try exact matches
                for code, full_name in CONDITION_MAPPING.items():
                    if code in variant_title or full_name in variant_title:
                        condition_code = code
                        break

                # If no match found, try partial matches
                if not condition_code:
                    if any(word in variant_title for word in ['near mint', 'nearmint', 'nm']):
                        condition_code = 'nm'
                    elif any(word in variant_title for word in ['lightly played', 'lightlyplayed', 'lp']):
                        condition_code = 'lp'
                    elif any(word in variant_title for word in ['moderately played', 'moderatelyplayed', 'mp']):
                        condition_code = 'mp'
                    elif any(word in variant_title for word in ['heavily played', 'heavilyplayed', 'hp']):
                        condition_code = 'hp'
                    elif any(word in variant_title for word in ['damaged', 'dm']):
                        condition_code = 'dm'
                    else:
                        condition_code = 'nm'  # Default to NM if no condition found

                logger.info(f"Processing variant '{variant_title}' with condition code '{condition_code}'")

                # Get standardized settings using the warehouse utils function
                from routes.warehouse.utils import prepare_shopify_settings
                settings_dict = prepare_shopify_settings(username)

                # Only override custom stepping and currency, keep original min price
                settings_dict['customStepping'] = custom_stepping
                settings_dict['currency'] = user_currency  # Pass the user's currency to the calculator

                # Prepare catalog item info for game rules
                catalog_item = {
                    'gameName': product.get('product_type'),  # Use product_type as game name
                    'product_type': product.get('product_type'),
                    'rarity': product.get('rarity'),
                    'vendor': product.get('vendor'),
                    'expansionName': product.get('expansionName')
                }

                # Prepare SKU info with all pricing data
                sku_info = {
                    'pricingInfo': matched_price,
                    'condName': condition_code,
                    'printingName': selected_printing_type
                }

                # Calculate price using unified calculator with all settings
                from pricing_utils import PricingCalculator
                calculator = PricingCalculator(settings_dict)
                final_price, is_missing, price_history = calculator.calculate_final_price(sku_info, catalog_item)

                # Log price calculation steps
                for entry in price_history:
                    logger.info(f"{entry['step']}: ${entry['old_price']:.2f} -> ${entry['new_price']:.2f} {entry.get('details', '')}")

                if not is_missing and final_price is not None:
                    return round(final_price, 2)

            return float(variant.get('price', 9999))

        except requests.RequestException:
            return float(variant.get('price', 9999))

    except Exception as e:
        logger.error(f"Error in price calculation: {str(e)}")
        return float(variant.get('price', 9999))  # Keep existing price on error

@products_bp.route('/')
@login_required
def products():
    return render_template('shopify_products.html')

@products_bp.route('/api/vendors', methods=['GET'])
@login_required
def get_vendors():
    item_type = request.args.get('itemType')

    query = {'username': current_user.username}
    if item_type == 'tcg':
        query['tcgItem'] = True

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$vendor'}},
        {'$sort': {'_id': 1}},
        {'$project': {'_id': 0, 'vendor': '$_id'}},
        {'$limit': 1000}  # Limit the number of vendors to prevent exceeding memory limits
    ]

    try:
        vendors = [doc['vendor'] for doc in shopify_collection.aggregate(pipeline) if doc['vendor']]
        return jsonify({'vendors': vendors})
    except Exception as e:
        logger.error(f"Error in get_vendors: {str(e)}")
        return jsonify({'error': 'An error occurred while fetching vendors'}), 500

@products_bp.route('/api/product-types', methods=['GET'])
@login_required
def get_product_types():
    vendor = request.args.get('vendor')
    query = {'username': current_user.username}
    if vendor:
        query['vendor'] = vendor

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$product_type'}},
        {'$sort': {'_id': 1}}
    ]

    product_types = [doc['_id'] for doc in shopify_collection.aggregate(pipeline) if doc['_id']]
    return jsonify({'product_types': product_types})

@products_bp.route('/api/expansion-names', methods=['GET'])
@login_required
def get_expansion_names():
    product_type = request.args.get('productType')
    if not product_type:
        return jsonify({'error': 'Product Type is required'}), 400

    query = {'product_type': product_type, 'username': current_user.username}

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$expansionName'}},
        {'$sort': {'_id': 1}}
    ]

    expansion_names = [doc['_id'] for doc in shopify_collection.aggregate(pipeline)]
    return jsonify(expansion_names)

@products_bp.route('/api/products', methods=['GET'])
@login_required
def get_products():
    item_type = request.args.get('itemType')
    vendor = request.args.get('vendor')
    product_type = request.args.get('productType')
    expansion_name = request.args.get('expansionName')
    search_term = request.args.get('searchTerm', '')
    in_stock_only = request.args.get('inStockOnly', 'false').lower() == 'true'
    needs_pushing = request.args.get('needsPushing', 'false').lower() == 'true'
    sold_only = request.args.get('soldOnly', 'false').lower() == 'true'
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 25))
    # For infinite scrolling, we'll use a cursor-based approach with skip/limit
    skip = (page - 1) * per_page

    match_stage = {'username': current_user.username}
    if search_term:
        match_stage['title'] = {'$regex': search_term, '$options': 'i'}
    if item_type == 'tcg':
        match_stage['tcgItem'] = True
    if vendor:
        match_stage['vendor'] = vendor
    if product_type:
        match_stage['product_type'] = product_type
    if expansion_name:
        match_stage['expansionName'] = expansion_name
    if in_stock_only:
        match_stage['variants.inventory_quantity'] = {'$gt': 0}
    if sold_only:
        match_stage['last_sold_date'] = {'$exists': True}
    if needs_pushing:
        match_stage['needsPushing'] = True
    manual_attention = request.args.get('manualAttention', 'false').lower() == 'true'
    if manual_attention:
        match_stage['needsManual'] = True
        
    # Handle quantity filter
    quantity_filter_type = request.args.get('quantityFilterType')
    quantity_filter_value = request.args.get('quantityFilterValue')
    if quantity_filter_type and quantity_filter_value and quantity_filter_value.isdigit():
        quantity_value = int(quantity_filter_value)
        if quantity_filter_type == 'above':
            match_stage['variants.inventory_quantity'] = {'$gte': quantity_value}
        elif quantity_filter_type == 'below':
            match_stage['variants.inventory_quantity'] = {'$lte': quantity_value}

    skip = (page - 1) * per_page

    # Base pipeline for filtering and computing fields
    base_pipeline = [
        {'$match': match_stage},
        {'$lookup': {
            'from': 'shProducts',
            'let': {'productId': {'$toInt': '$productId'}, 'username': '$username'},
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$productId', '$$productId']},
                            {'$eq': ['$username', '$$username']}
                        ]
                    }
                }},
                {'$project': {'variants.title': 1}}
            ],
            'as': 'shProduct'
        }},
    {'$project': {
        'shopifyId': '$id',
        'title': 1,
        'image': 1,  # Include the full image object with src property
        'images': 1,  # Include full images array for debugging
        'expansionName': 1,
        'number': 1,  # Include card number field
        'variants': 1,
        'last_sold_date': 1,
        'last_repriced': 1,  # Include last_repriced field for the frontend
        'shProductVariants': {'$arrayElemAt': ['$shProduct.variants', 0]},
        'manualOverride': 1,
        'needsPushing': 1  # Include needsPushing field for the frontend
    }},
        {'$addFields': {
            'lowest_price': {'$min': '$variants.price'},
            'highest_price': {'$max': '$variants.price'},
            'total_quantity': {'$sum': '$variants.inventory_quantity'},
            'average_sell_price': {'$avg': '$variants.price'}
        }}
    ]

    # Add sorting if specified
    sort_price = request.args.get('sortPrice')
    if sort_price == 'low-to-high':
        base_pipeline.append({'$sort': {'lowest_price': 1, 'title': 1}})
    elif sort_price == 'high-to-low':
        base_pipeline.append({'$sort': {'highest_price': -1, 'title': 1}})
    elif sort_price == 'number-asc' or sort_price == 'number-desc':
        # Implement natural sort for card numbers using MongoDB's $function
        # This splits the number field into chunks of digits and non-digits
        # and compares them appropriately for natural sorting
        base_pipeline.append({
            '$addFields': {
                'sortNumber': {'$ifNull': ['$number', '']},  # Use empty string if number is null
                'naturalSortKey': {
                    '$function': {
                        'body': """
                        function(number) {
                            if (!number) return [];
                            
                            // Split the string into chunks of digits and non-digits
                            const parts = number.toString().split(/(\d+)/);
                            
                            // Create a key array for sorting
                            const key = [];
                            for (let i = 0; i < parts.length; i++) {
                                const part = parts[i];
                                if (part.match(/^\d+$/)) {
                                    // If it's a number, convert to integer for numeric comparison
                                    key.push(parseInt(part));
                                } else {
                                    // If it's a string, keep as is for string comparison
                                    key.push(part);
                                }
                            }
                            return key;
                        }
                        """,
                        'args': ['$number'],
                        'lang': 'js'
                    }
                }
            }
        })
        
        # Sort by the natural sort key
        if sort_price == 'number-asc':
            base_pipeline.append({'$sort': {'naturalSortKey': 1, 'title': 1}})
        else:  # number-desc
            base_pipeline.append({'$sort': {'naturalSortKey': -1, 'title': 1}})
    else:
        base_pipeline.append({'$sort': {'title': 1}})

    # For card number sorting, we need to apply the natural sort before pagination
    if sort_price == 'number-asc' or sort_price == 'number-desc':
        # First apply the natural sort to all matching documents
        sorted_pipeline = base_pipeline.copy()
        
        # Then apply pagination after sorting
        pipeline = sorted_pipeline + [
            {'$facet': {
                'paginatedResults': [{'$skip': skip}, {'$limit': per_page}],
                'totalCount': [{'$count': 'count'}]
            }}
        ]
    else:
        # For other sorts, use the standard pipeline with pagination
        pipeline = base_pipeline + [
            {'$facet': {
                'paginatedResults': [{'$skip': skip}, {'$limit': per_page}],
                'totalCount': [{'$count': 'count'}]
            }}
        ]

    result = list(shopify_collection.aggregate(pipeline))

    if result and result[0]['paginatedResults']:
        products = result[0]['paginatedResults']
        total_count = result[0]['totalCount'][0]['count'] if result[0]['totalCount'] else 0

        # Add debug logging for image URLs
        for product in products:
            if '_id' in product:
                product['_id'] = str(product['_id'])

            # Format last_repriced date if it exists
            if 'last_repriced' in product and product['last_repriced']:
                try:
                    # Handle MongoDB $date format
                    if isinstance(product['last_repriced'], dict) and '$date' in product['last_repriced']:
                        last_repriced = product['last_repriced']['$date']
                    else:
                        last_repriced = str(product['last_repriced'])

                    # Convert to formatted datetime - use a more concise format for the table
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_repriced.replace('Z', '+00:00'))
                    product['last_repriced'] = dt.strftime('%d/%m/%Y')  # Simplified format for table view
                except Exception as e:
                    logger.error(f"Error formatting last_repriced date: {e}")
                    product['last_repriced'] = None

            # Log image data for debugging
            logger.info(f"Product {product.get('title')}: image={product.get('image')}, has_images_array={bool(product.get('images'))}")

            # Ensure image is in the correct format for the frontend
            if isinstance(product.get('image'), str):
                # If image is a direct string URL, convert it to an object with src property
                image_url = product['image']
                product['image'] = {'src': image_url}
                logger.info(f"Converted string image URL to object: {product['image']}")
            elif product.get('image') and isinstance(product['image'], dict) and 'src' in product['image']:
                # Image is already in correct format
                logger.info(f"Image URL from image.src: {product['image']['src']}")
            # Fallback to images array if image.src is not available
            elif product.get('images') and len(product['images']) > 0 and 'src' in product['images'][0]:
                logger.info(f"Image URL from images array: {product['images'][0]['src']}")
                if not product.get('image'):
                    product['image'] = {}
                product['image']['src'] = product['images'][0]['src']

        return jsonify({'products': products, 'total': total_count, 'page': page, 'per_page': per_page})
    else:
        return jsonify({'products': [], 'total': 0, 'page': page, 'per_page': per_page})

@products_bp.route('/api/product-types', methods=['GET'])
@login_required
def get_all_product_types():
    pipeline = [
        {'$match': {'username': current_user.username}},
        {'$group': {'_id': '$product_type'}},
        {'$sort': {'_id': 1}}
    ]
    product_types = [doc['_id'] for doc in shopify_collection.aggregate(pipeline) if doc['_id']]
    return jsonify(product_types)

@products_bp.route('/api/products-by-type', methods=['GET'])
@login_required
def get_products_by_type():
    product_type = request.args.get('productType')
    if not product_type:
        return jsonify({'error': 'Product type is required'}), 400

    products = list(shopify_collection.find(
        {'username': current_user.username, 'product_type': product_type},
        {'_id': 1, 'title': 1, 'product_type': 1}
    ))

    for product in products:
        product['_id'] = str(product['_id'])

    return jsonify(products)

@products_bp.route('/api/products-by-vendor', methods=['GET'])
@login_required
def get_products_by_vendor():
    vendor = request.args.get('vendor')
    if not vendor:
        return jsonify({'error': 'Vendor is required'}), 400

    products = list(shopify_collection.find(
        {'username': current_user.username, 'vendor': vendor},
        {'_id': 1, 'title': 1, 'vendor': 1}
    ))

    for product in products:
        product['_id'] = str(product['_id'])

    return jsonify(products)

@products_bp.route('/api/staged-inventory', methods=['GET', 'DELETE'])
@login_required
def get_staged_inventory():
    try:
        logger.info("Starting staged inventory fetch")
        query = {'username': current_user.username}  # Keep username for now since staged_inventory uses it

        staged_inventory = list(db['staged_inventory'].find(query))
        logger.info(f"Found {len(staged_inventory)} staged inventory items")

        processed_items = []
        for item in staged_inventory:
            try:
                if '_id' in item:
                    item['_id'] = str(item['_id'])
                processed_items.append(item)
            except Exception as e:
                logger.error(f"Error processing staged item: {str(e)}")
                continue

        for item in processed_items:
            logger.info(f"Processing staged item: {item.get('name')}")

            # Try to find matching product in shProducts
            product_id = item.get('product_id')  # Get product_id from staged inventory
            if not product_id:
                logger.warning(f"No product_id found for staged item: {item.get('name')}")
                continue

            query = {
                'username': current_user.username,
                'productId': product_id
            }
            print(f"Looking for product with query: {query}")

            shopify_product = shopify_collection.find_one(query)
            print(f"Found matching product: {shopify_product is not None}")

            if shopify_product:
                print(f"Matched product title: {shopify_product.get('title')}")
                print(f"Matched product variants: {shopify_product.get('variants', [])}")

            if shopify_product:
                logger.info(f"Found matching product: {shopify_product.get('title')}")
                logger.info(f"Processing variants for product {shopify_product.get('title')}")
            else:
                logger.warning(f"No matching product found for staged item: {item.get('name')}")
                item['matchedVariants'] = []
                item['unmatchedVariants'] = item.get('variants', [])
                continue

            if shopify_product:
                matched_variants = []
                unmatched_variants = []

                for staged_variant in item.get('variants', []):
                    staged_condition = staged_variant.get('condition', '').lower()
                    staged_printing = staged_variant.get('printing', '').lower()
                    match_found = False

                    # Normalize the staged condition
                    normalized_staged_condition = normalize_condition(staged_condition)

                    for shopify_variant in shopify_product.get('variants', []):
                        shopify_title = shopify_variant.get('title', '').lower()
                        # Split the shopify title into parts to find the condition
                        title_parts = shopify_title.split()

                        # Try to find a matching condition in the title parts
                        shopify_condition = None
                        for part in title_parts:
                            normalized_part = normalize_condition(part)
                            if normalized_part in CONDITION_MAPPING.values():
                                shopify_condition = normalized_part
                                break

                        # If no condition found in parts, try normalizing the whole title
                        if not shopify_condition:
                            shopify_condition = normalize_condition(shopify_title)

                        # Check if both normalized conditions match exactly and printing type matches
                        if (shopify_condition == normalized_staged_condition and
                            staged_printing in shopify_title):
                            matched_variant = shopify_variant.copy()
                            matched_variant['staged_quantity'] = staged_variant.get('quantity', 0)
                            matched_variants.append(matched_variant)
                            match_found = True
                            logger.info(f"Matched variant for product {item.get('productId')}: Staged {staged_variant}, Shopify {shopify_variant}")
                            break

                    if not match_found:
                        unmatched_variants.append(staged_variant)
                        logger.warning(f"Unmatched variant for product {item.get('productId')}: Staged {staged_variant}")

                item['matchedVariants'] = matched_variants
                item['unmatchedVariants'] = unmatched_variants
            else:
                logger.warning(f"No Shopify product found for staged item: {item.get('productId')}")
                item['matchedVariants'] = []
                item['unmatchedVariants'] = item.get('variants', [])

        logger.info(f"Found {len(processed_items)} staged inventory items")

        # Process variants for each item
        for item in processed_items:
            if not item.get('matchedVariants'):
                item['matchedVariants'] = []
            if not item.get('unmatchedVariants'):
                item['unmatchedVariants'] = item.get('variants', [])

        if processed_items:
            logger.info(f"First processed item: {processed_items[0]}")
        else:
            logger.info("No staged inventory items found")

        return jsonify(processed_items)
    except Exception as e:
        logger.error(f"Error fetching staged inventory: {str(e)}")
        return jsonify({'error': str(e)}), 500

@products_bp.route('/api/bulk-edit', methods=['POST'])
@login_required
def bulk_edit():
    data = request.json
    edit_type = data.get('editType')
    original_value = data.get('originalValue')
    new_value = data.get('newValue')
    selected_product_ids = data.get('selectedProductIds', [])
    page = data.get('page', 1)
    per_page = data.get('per_page', 100)  # Process 100 products at a time

    if not edit_type or not original_value or not new_value:
        return jsonify({'error': 'Edit type, original value, and new value are required'}), 400

    try:
        # Build the base query
        query = {
            'username': current_user.username,
            edit_type: original_value
        }
        if selected_product_ids:
            query['_id'] = {'$in': [ObjectId(id) for id in selected_product_ids]}

        # Get total count of matching products
        total_count = shopify_collection.count_documents(query)

        # Calculate skip value for pagination
        skip = (page - 1) * per_page

        # Get paginated products
        products = shopify_collection.find(query).skip(skip).limit(per_page)

        # Update the current batch of products
        product_ids = [product['_id'] for product in products]
        if product_ids:
            # Update with the current timestamp for last_repriced
            update_data = {
                edit_type: new_value,
                'last_repriced': datetime.utcnow()
            }

            result = shopify_collection.update_many(
                {'_id': {'$in': product_ids}},
                {'$set': update_data}
            )
            modified_count = result.modified_count
        else:
            modified_count = 0

        # Calculate remaining products
        remaining_count = max(0, total_count - (skip + per_page))

        return jsonify({
            'message': f'Successfully updated {modified_count} products in the local database',
            'total_count': total_count,
            'remaining_count': remaining_count,
            'current_page': page,
            'has_more': remaining_count > 0
        })
    except Exception as e:
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@products_bp.route('/api/push_bulk_edit_to_shopify', methods=['GET', 'POST'])
@login_required
def push_bulk_edit_to_shopify():
    # For GET requests, establish an SSE connection
    if request.method == 'GET':
        def generate():
            # Send initial connection established message
            yield 'data: {"connected": true, "message": "SSE connection established"}\n\n'

            # Keep the connection open with a heartbeat
            heartbeat_interval = 15  # seconds
            last_heartbeat = time.time()

            while True:
                current_time = time.time()
                if current_time - last_heartbeat >= heartbeat_interval:
                    yield f'data: {{"heartbeat": true, "timestamp": {int(current_time)}}}\n\n'
                    last_heartbeat = current_time

                # Sleep briefly to avoid consuming too much CPU
                time.sleep(0.5)

        return Response(generate(), mimetype='text/event-stream')

    # For POST requests, process the bulk edit
    data = request.json
    edit_type = data.get('editType')
    new_value = data.get('newValue')
    selected_product_ids = data.get('selectedProductIds', [])

    if not edit_type or not new_value or not selected_product_ids:
        return jsonify({'error': 'Edit type, new value, and selected product IDs are required'}), 400

    # Process the bulk edit in a separate thread
    def process_bulk_edit():
        try:
            # Get user profile
            user_profile = user_collection.find_one({'username': current_user.username})
            if not user_profile:
                logger.error(f"User profile not found for {current_user.username}")
                return

            shopify_store_name = user_profile['shopifyStoreName']
            shopify_access_token = user_profile['shopifyAccessToken']

            headers = {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": shopify_access_token
            }

            # Create a session with retries and backoff
            session_obj = requests.Session()
            retries = Retry(
                total=3,
                backoff_factor=0.5,
                status_forcelist=[500, 502, 503, 504],
                allowed_methods=frozenset(['GET', 'PUT', 'POST'])
            )
            session_obj.mount('https://', HTTPAdapter(max_retries=retries))
            session_obj.timeout = 10  # 10 second timeout

            # Process products in batches to avoid overwhelming the server
            batch_size = 2  # Process 2 products at a time
            total_batches = (len(selected_product_ids) + batch_size - 1) // batch_size

            total_processed = 0
            all_results = []

            # Create a global variable to store the progress
            global bulk_edit_progress
            bulk_edit_progress = {
                'progress': 0,
                'processed': 0,
                'total': len(selected_product_ids),
                'results': [],
                'status': 'processing'
            }

            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(selected_product_ids))
                batch = selected_product_ids[start_idx:end_idx]

                batch_results = []
                for product_id in batch:
                    try:
                        # Get product from MongoDB
                        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
                        if not product:
                            batch_results.append({
                                'status': 'error',
                                'product_id': product_id,
                                'error': 'Product not found'
                            })
                            continue

                        # Get Shopify product ID
                        shopify_product_id = product.get('id')
                        if not shopify_product_id:
                            batch_results.append({
                                'status': 'error',
                                'product_id': product_id,
                                'error': 'Shopify product ID not found'
                            })
                            continue

                        # Update product in Shopify
                        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"
                        product_data = {
                            "product": {
                                "id": shopify_product_id,
                                edit_type: new_value
                            }
                        }

                        response = session_obj.put(url, headers=headers, json=product_data, timeout=10)
                        response.raise_for_status()

                        # Update last_repriced timestamp in MongoDB
                        shopify_collection.update_one(
                            {'_id': ObjectId(product_id)},
                            {'$set': {'last_repriced': datetime.utcnow()}}
                        )

                        batch_results.append({
                            'status': 'success',
                            'product_id': product_id,
                            'title': product.get('title', 'Unknown')
                        })

                    except requests.RequestException as e:
                        logger.error(f"Error updating product {product_id} in Shopify: {str(e)}")
                        batch_results.append({
                            'status': 'error',
                            'product_id': product_id,
                            'error': f"Shopify API error: {str(e)}"
                        })
                    except Exception as e:
                        logger.error(f"Error processing product {product_id}: {str(e)}")
                        batch_results.append({
                            'status': 'error',
                            'product_id': product_id,
                            'error': str(e)
                        })

                # Update progress
                total_processed += len(batch)
                all_results.extend(batch_results)

                # Update global progress variable
                progress = total_processed / len(selected_product_ids)
                bulk_edit_progress = {
                    'results': batch_results,
                    'progress': progress,
                    'processed': total_processed,
                    'total': len(selected_product_ids),
                    'status': 'processing'
                }

                # Rate limit: 2 products per second (Shopify API limit)
                time.sleep(1.0)

            # Update global progress variable with completion status
            bulk_edit_progress = {
                'result': 'complete',
                'progress': 1.0,
                'processed': total_processed,
                'total': len(selected_product_ids),
                'results': all_results,
                'status': 'complete'
            }

        except Exception as e:
            logger.error(f"Unexpected error in bulk edit: {str(e)}")
            # Update global progress variable with error status
            bulk_edit_progress = {
                'error': str(e),
                'result': 'error',
                'status': 'error'
            }

    # Initialize global variable for progress tracking
    global bulk_edit_progress
    bulk_edit_progress = {
        'progress': 0,
        'processed': 0,
        'total': len(selected_product_ids),
        'results': [],
        'status': 'starting'
    }

    # Start processing in a background thread
    import threading
    thread = threading.Thread(target=process_bulk_edit)
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Bulk edit task started', 'task_id': str(time.time())}), 202

# Add a new endpoint to get the current status of a bulk edit task
@products_bp.route('/api/bulk_edit_status', methods=['GET'])
@login_required
def get_bulk_edit_status():
    global bulk_edit_progress

    if not bulk_edit_progress:
        return jsonify({'error': 'No bulk edit task found'}), 404

    return jsonify(bulk_edit_progress), 200

@products_bp.route('/api/product/<product_id>')
@login_required
def get_product(product_id):
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    # Ensure image URL is available in the correct format
    if isinstance(product.get('image'), str):
        # If image is a direct string URL, convert it to an object with src property
        image_url = product['image']
        product['image'] = {'src': image_url}
        logger.info(f"Converted string image URL to object: {product['image']}")
    # Fallback to images array if image.src is not available
    elif product.get('image') is None or not product.get('image', {}).get('src'):
        if product.get('images') and len(product['images']) > 0:
            if not product.get('image'):
                product['image'] = {}
            product['image']['src'] = product['images'][0]['src']
            logger.info(f"Used image from images array: {product['image']['src']}")

    # Get user's Shopify store URL
    user_profile = user_collection.find_one({'username': current_user.username})
    if user_profile:
        product['shopify_store'] = user_profile.get('shopifyStoreName', '') + '.myshopify.com'

    # Get TCGPlayer URL and catalog data
    tcgplayer_url = None
    catalog_item = None
    if product.get('productId'):
        catalog_item = catalog_collection.find_one({'productId': int(product['productId'])})
        if catalog_item and 'url' in catalog_item:
            tcgplayer_url = catalog_item['url']
    product['tcgplayer_url'] = tcgplayer_url

    # Add catalog skus to each variant if available
    if catalog_item and 'skus' in catalog_item and catalog_item['skus']:
            # Create a mapping of condition codes to their respective skus
            sku_mapping = {}
            for sku in catalog_item['skus']:
                condition_code = sku.get('condAbbr', '').lower()
                if condition_code:
                    if condition_code not in sku_mapping:
                        sku_mapping[condition_code] = []
                    sku_mapping[condition_code].append(sku)

            # Add skuId to each variant based on condition and printing type
            for variant in product.get('variants', []):
                variant_title = variant.get('title', '').lower()

                # Try to match condition from the variant title
                matched_condition = None
                for code in sku_mapping.keys():
                    if code in variant_title or CONDITION_MAPPING.get(code, '') in variant_title:
                        matched_condition = code
                        break

                # If no direct match, try to infer condition
                if not matched_condition:
                    if any(word in variant_title for word in ['near mint', 'nearmint', 'nm']):
                        matched_condition = 'nm'
                    elif any(word in variant_title for word in ['lightly played', 'lightlyplayed', 'lp']):
                        matched_condition = 'lp'
                    elif any(word in variant_title for word in ['moderately played', 'moderatelyplayed', 'mp']):
                        matched_condition = 'mp'
                    elif any(word in variant_title for word in ['heavily played', 'heavilyplayed', 'hp']):
                        matched_condition = 'hp'
                    elif any(word in variant_title for word in ['damaged', 'dm']):
                        matched_condition = 'dm'

                # Add skus to variant if condition matched, but filter by printing type
                if matched_condition and matched_condition in sku_mapping:
                    matched_skus = []

                    # First try to find SKUs that match both condition and printing type
                    for sku in sku_mapping[matched_condition]:
                        printing_name = sku.get('printingName', '').lower()
                        if printing_name and printing_name in variant_title:
                            matched_skus.append(sku)

                    # If we found SKUs matching both condition and printing type, use those
                    # Otherwise, fall back to all SKUs for this condition
                    variant['catalog_skus'] = matched_skus if matched_skus else sku_mapping[matched_condition]

    tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
    if tcgplayer_key_doc:
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']
        headers = {
            'Authorization': f'Bearer {tcgplayer_api_key}',
            'Accept': 'application/json',
        }

        try:
            product_tcg_id = product.get('productId')
            if product_tcg_id:
                pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_tcg_id}"
                pricing_response = requests.get(pricing_url, headers=headers)
                pricing_response.raise_for_status()
                pricing_data = pricing_response.json().get('results', [])

                # Get user's currency and exchange rate
                user_currency = user_profile.get('currency', 'USD')
                from shautopricing import get_exchange_rate
                exchange_rate = get_exchange_rate(user_currency)

                # Convert prices to user's local currency
                converted_pricing_data = []
                for price_info in pricing_data:
                    # Check for null prices and get from collection if needed
                    all_null = all(price_info.get(price_type) is None for price_type in ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
                    if all_null:
                        collection_price = prices_collection.find_one({
                            'productId': product_tcg_id,
                            'subTypeName': price_info['subTypeName']
                        })
                        if collection_price:
                            # Update price info with collection prices
                            price_info.update({
                                'lowPrice': collection_price.get('lowPrice'),
                                'marketPrice': collection_price.get('marketPrice'),
                                'midPrice': collection_price.get('midPrice'),
                                'highPrice': collection_price.get('highPrice')
                            })

                    # Convert prices to local currency
                    converted_price_info = price_info.copy()
                    for price_type in ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']:
                        if converted_price_info.get(price_type) is not None:
                            converted_price_info[price_type] = round(float(converted_price_info[price_type]) * exchange_rate, 2)
                    converted_pricing_data.append(converted_price_info)

                product['tcgplayer_prices'] = converted_pricing_data
                product['currency'] = user_currency
        except:
            product['tcgplayer_prices'] = []

    if '_id' in product:
        product['_id'] = str(product['_id'])

    # Format last_repriced date if it exists
    if 'last_repriced' in product and product['last_repriced']:
        try:
            # Handle MongoDB $date format
            if isinstance(product['last_repriced'], dict) and '$date' in product['last_repriced']:
                last_repriced = product['last_repriced']['$date']
            else:
                last_repriced = str(product['last_repriced'])

            # Convert to UK formatted datetime
            from datetime import datetime
            dt = datetime.fromisoformat(last_repriced.replace('Z', '+00:00'))
            product['last_repriced'] = dt.strftime('%d/%m/%Y %H:%M:%S (UK)')
        except Exception as e:
            print(f"Error formatting last_repriced date: {e}")
            product['last_repriced'] = None
    else:
        product['last_repriced'] = None

    return jsonify(product)

@products_bp.route('/api/reprice_all', methods=['POST'])
@login_required
def reprice_all():
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    data = request.json
    filter_params = data.get('filterParams', {})

    # Build query based on filter parameters
    query = {'username': current_user.username}
    if filter_params.get('itemType') == 'tcg':
        query['tcgItem'] = True
    if filter_params.get('vendor'):
        query['vendor'] = filter_params['vendor']
    if filter_params.get('productType'):
        query['product_type'] = filter_params['productType']
    if filter_params.get('expansionName'):
        query['expansionName'] = filter_params['expansionName']
    if filter_params.get('searchTerm'):
        query['title'] = {'$regex': filter_params['searchTerm'], '$options': 'i'}
    if filter_params.get('inStockOnly'):
        query['variants.inventory_quantity'] = {'$gt': 0}
    if filter_params.get('soldOnly'):
        query['last_sold_date'] = {'$exists': True}

    # Get Shopify credentials
    shopify_store_name = user_profile['shopifyStoreName']
    shopify_access_token = user_profile['shopifyAccessToken']

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    # Get filtered products using aggregation pipeline
    pipeline = [
        {'$match': query},
        {'$project': {
            '_id': 1,
            'title': 1,
            'variants': 1,
            'id': 1,
            'vendor': 1,
            'product_type': 1,
            'expansionName': 1,
            'productId': 1
        }}
    ]

    products = list(shopify_collection.aggregate(pipeline))
    total_products = len(products)
    price_changes = []

    def generate():
        for i, product in enumerate(products):
            try:
                # Calculate new prices using the same logic as individual repricing
                old_variants = product['variants']
                new_variants = []
                product_changes = []

                for variant in old_variants:
                    old_price = float(variant['price'])
                    # Get standardized settings using the warehouse utils function
                    from routes.warehouse.utils import prepare_shopify_settings
                    settings_dict = prepare_shopify_settings(current_user.username)

                    # Only override custom stepping and currency, keep original min price
                    settings_dict['customStepping'] = user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})
                    settings_dict['currency'] = user_profile.get('currency', 'USD')

                    # Calculate new price using standardized settings with original min price
                    new_price = calculate_new_price(
                        product=product,
                        variant=variant,
                        custom_stepping=settings_dict['customStepping'],
                        min_price=settings_dict['minPrice'],
                        user_currency=settings_dict['currency']
                    )

                    variant['price'] = str(new_price)
                    new_variants.append(variant)

                    if abs(new_price - old_price) > 0.01:
                        product_changes.append({
                            'variant_title': variant['title'],
                            'old_price': old_price,
                            'new_price': new_price
                        })

                # Update MongoDB
                shopify_collection.update_one(
                    {'_id': product['_id']},
                    {'$set': {'variants': new_variants}}
                )

                # Always push to Shopify to ensure sync
                shopify_product_id = product.get('id')
                if shopify_product_id:
                    product_data = {
                        "product": {
                            "id": shopify_product_id,
                            "variants": [
                                {
                                    "id": variant['id'],
                                    "price": variant['price']
                                } for variant in new_variants
                            ]
                        }
                    }

                    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"
                    response = requests.put(url, headers=headers, json=product_data)
                    response.raise_for_status()

                    # Update MongoDB to mark as synced
                    shopify_collection.update_one(
                        {'_id': product['_id']},
                        {'$set': {'needsPushing': False}}
                    )

                    # Yield progress update
                    progress_data = {
                        'progress': (i + 1) / total_products,
                        'processed': i + 1,
                        'total': total_products,
                        'status': 'success',
                        'message': f"Updated {product['title']}" +
                                 (f" with {len(product_changes)} price changes" if product_changes else " (synced with Shopify)")
                    }
                    yield f"data: {json.dumps(progress_data)}\n\n"

                    # Rate limiting
                    time.sleep(0.5)

            except Exception as e:
                logger.error(f"Error processing product {product.get('title')}: {str(e)}")
                # Yield error update
                progress_data = {
                    'progress': (i + 1) / total_products,
                    'processed': i + 1,
                    'total': total_products,
                    'status': 'error',
                    'message': f"Failed to update {product.get('title', 'Unknown')}: {str(e)}"
                }
                yield f"data: {json.dumps(progress_data)}\n\n"
                continue

    return Response(generate(), mimetype='text/event-stream')

@products_bp.route('/api/manual_reprice', methods=['POST'])
@login_required
def manual_reprice():
    data = request.json
    product_id = data.get('productId')
    printing_type = data.get('printingType')
    variant_ids = data.get('variantIds', [])

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400
    if not printing_type:
        return jsonify({'error': 'Printing type is required for manual repricing'}), 400
    if not variant_ids:
        return jsonify({'error': 'At least one variant must be selected'}), 400

    try:
        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        custom_stepping = user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})
        min_price = user_profile.get('minPrice', 0.2)
        user_currency = user_profile.get('currency', 'USD')

        price_changes = []
        for variant in product['variants']:
            # Only process selected variants
            if str(variant.get('id')) not in variant_ids:
                continue
            old_price = float(variant['price'])
            # Get standardized settings using the warehouse utils function
            from routes.warehouse.utils import prepare_shopify_settings
            settings_dict = prepare_shopify_settings(current_user.username)

            # Only override custom stepping and currency, keep original min price
            settings_dict['customStepping'] = custom_stepping
            settings_dict['currency'] = user_currency

            # Calculate new price using standardized settings
            new_price = calculate_new_price(
                product=product,
                variant=variant,
                custom_stepping=settings_dict['customStepping'],
                min_price=settings_dict['minPrice'],
                user_currency=settings_dict['currency'],
                printing_type=printing_type  # Pass the selected printing type
            )
            variant['price'] = str(new_price)
            if abs(new_price - old_price) > 0.01:
                price_changes.append({
                    'variant_title': variant['title'],
                    'old_price': old_price,
                    'new_price': new_price
                })

        # Update MongoDB
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {
                'variants': product['variants'],
                'last_repriced': datetime.utcnow(),
                'needsManual': False
            }}
        )

        # Push to Shopify
        shopify_store_name = user_profile['shopifyStoreName']
        shopify_access_token = user_profile['shopifyAccessToken']

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        product_data = {
            "product": {
                "id": shopify_product_id,
                "variants": [
                    {
                        "id": variant['id'],
                        "price": variant['price']
                    } for variant in product['variants']
                ]
            }
        }

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"
        response = requests.put(url, headers=headers, json=product_data)
        response.raise_for_status()

        if price_changes:
            return jsonify({
                'message': 'Successfully repriced product with manual printing type selection',
                'price_changes': price_changes
            })
        else:
            return jsonify({
                'message': 'No price changes needed for the selected printing type'
            })

    except requests.RequestException as e:
        logger.error(f"Failed to push product to Shopify: {str(e)}")
        return jsonify({'error': f'Failed to push to Shopify: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error during manual repricing: {str(e)}")
        return jsonify({'error': f'An error occurred during manual repricing: {str(e)}'}), 500

# Constants
EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"
TEST_USERNAME = "Khaoz"  # Username for testing new pricing logic

# Initialize currency cache
from utils.currency_cache import CurrencyCache
currency_cache = CurrencyCache()

def get_exchange_rate(target_currency):
    """
    Get exchange rate with caching and error handling
    """
    if target_currency == 'USD':
        return 1.0

    # Check cache first
    cached_rate = currency_cache.get(target_currency)
    if cached_rate is not None:
        logger.debug(f"Using cached exchange rate for {target_currency}: {cached_rate}")
        return cached_rate

    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency)

        if rate is None:
            logger.error(f"Exchange rate not found for {target_currency}, using 1.0")
            return 1.0

        # Cache the new rate
        currency_cache.set(target_currency, rate)
        logger.info(f"Fetched and cached new exchange rate for {target_currency}: {rate}")
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate for {target_currency}: {str(e)}")
        return 1.0

def get_pricing_rules(user_profile, product):
    """Get pricing rules for a specific product"""
    advanced_key = f"{product.get('vendor')}_{product.get('product_type')}_{product.get('expansionName')}"

    if advanced_key in user_profile.get('advancedPricingRules', {}):
        return user_profile['advancedPricingRules'][advanced_key]
    return user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})

def get_price_from_collection(product_id, subtype_name, price_preference_order):
    """Get price from collection based on preference order"""
    collection_price = prices_collection.find_one({
        'productId': product_id,
        'subTypeName': subtype_name
    })
    if collection_price:
        # Convert collection price to same format as TCGPlayer pricing data
        pricing_data = {
            'lowPrice': collection_price.get('lowPrice'),
            'marketPrice': collection_price.get('marketPrice'),
            'midPrice': collection_price.get('midPrice'),
            'highPrice': collection_price.get('highPrice'),
            'subTypeName': subtype_name
        }
        return pricing_data
    return None

@products_bp.route('/api/reprice', methods=['POST'])
@products_bp.route('/api/auto_reprice', methods=['POST'])
@login_required
def auto_reprice():
    data = request.json
    product_id = data.get('productId')
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    # Create a session with retries and backoff
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504],
        allowed_methods=frozenset(['GET', 'PUT', 'POST'])
    )
    session.mount('https://', HTTPAdapter(max_retries=retries))
    session.timeout = 30  # Increase timeout to 30 seconds

    try:
        # Get product from MongoDB
        product = shopify_collection.find_one(
            {'_id': ObjectId(product_id), 'username': current_user.username}
        )
        if not product:
            logger.error(f"Product not found for ID: {product_id}")
            return jsonify({'error': 'Product not found'}), 404

        # Skip if manual override is enabled
        if product.get('manualOverride', False):
            return jsonify({'message': 'Product has manual price override enabled'}), 200

        # Get user profile
        user_profile = user_collection.find_one(
            {'username': current_user.username}
        )
        if not user_profile:
            logger.error(f"User profile not found for username: {current_user.username}")
            return jsonify({'error': 'User profile not found'}), 404

        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return jsonify({'error': 'TCGPlayer API key not found'}), 500
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']

        # Initialize saautopricing collections and use process_single_product function
        from saautopricing import init_collections, process_single_product
        init_collections(mongo_client['test'])

        # Process product with timeouts
        updated_variants, price_changes = process_single_product(
            product,
            current_user.username,
            tcgplayer_api_key
        )

        # Get Shopify credentials
        shopify_store_name = user_profile['shopifyStoreName']
        shopify_access_token = user_profile['shopifyAccessToken']
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        # Get Shopify product ID
        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        # Cache location ID in MongoDB to reduce API calls
        location_id = None
        location_cache = db.get_collection('location_cache')
        cached_location = location_cache.find_one({'store_name': shopify_store_name})

        if cached_location and cached_location.get('location_id'):
            location_id = cached_location['location_id']
        else:
            # Get location ID from Shopify
            locations_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/locations.json"
            locations_response = session.get(locations_url, headers=headers)
            locations_response.raise_for_status()
            locations = locations_response.json()['locations']
            if not locations:
                return jsonify({'error': 'No locations found for the store'}), 400
            location_id = locations[0]['id']

            # Cache the location ID
            location_cache.update_one(
                {'store_name': shopify_store_name},
                {'$set': {
                    'location_id': location_id,
                    'last_updated': datetime.utcnow()
                }},
                upsert=True
            )

        # Use current variants if no updates
        variants_to_use = updated_variants if updated_variants else product['variants']

        # Update MongoDB first
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {
                '$set': {
                    'variants': variants_to_use,
                    'last_repriced': datetime.utcnow()
                }
            }
        )

        # Prepare variant updates (prices only, not inventory)
        variant_updates = []

        for variant in variants_to_use:
            # Prepare variant price update only
            variant_updates.append({
                "id": variant['id'],
                "price": variant['price']
            })

        try:
            # Update all variant prices in one call
            product_data = {
                "product": {
                    "id": shopify_product_id,
                    "variants": variant_updates
                }
            }
            product_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"
            response = session.put(product_url, headers=headers, json=product_data)
            response.raise_for_status()

        except requests.RequestException as e:
            logger.error(f"Error updating Shopify: {str(e)}")
            # Don't return error immediately, try to continue with remaining updates
            if not response.ok:
                return jsonify({
                    'error': f'Failed to update product: {response.text}'
                }), response.status_code

        # Return success response
        return jsonify({
            'message': 'Successfully synced with Shopify',
            'price_changes': price_changes if price_changes else [],
            'variants_synced': len(variants_to_use),
            'variants_pushed': True
        })

    except requests.Timeout:
        logger.error("Request timed out during repricing")
        return jsonify({'error': 'Operation timed out. Please try again.'}), 504
    except requests.RequestException as e:
        logger.error(f"Failed to push product to Shopify: {str(e)}")
        return jsonify({'error': f'Failed to push to Shopify: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error during repricing: {str(e)}")
        return jsonify({'error': f'An error occurred during repricing: {str(e)}'}), 500
    finally:
        session.close()

@products_bp.route('/webhook', methods=['POST'])
def shopify_webhook():
    topic = request.headers.get('X-Shopify-Topic')
    shop_domain = request.headers.get('X-Shopify-Shop-Domain')
    hmac = request.headers.get('X-Shopify-Hmac-SHA256')

    # Verify webhook authenticity
    if not verify_webhook(request.get_data(), hmac, shop_domain):
        logger.error("Invalid webhook signature")
        return 'Invalid webhook signature', 401

    user = user_collection.find_one({'shopifyStoreName': shop_domain})
    if not user:
        logger.error(f"No user found for Shopify store: {shop_domain}")
        return 'User not found', 404

    # Prepare webhook data
    webhook_data = {
        'topic': topic,
        'shop_domain': shop_domain,
        'data': request.json,
        'received_at': datetime.utcnow().isoformat()
    }

    # Import webhook queue
    from utils.webhook_queue import webhook_queue

    try:
        # Enqueue webhook for processing
        if webhook_queue.enqueue_webhook(webhook_data):
            logger.info(f"Successfully enqueued {topic} webhook for {shop_domain}")
            return '', 200
        else:
            logger.error("Failed to enqueue webhook (possibly rate limited)")
            return 'Rate limit exceeded', 429
    except Exception as e:
        logger.error(f"Error enqueueing webhook: {str(e)}")
        return jsonify({'error': f'Failed to process webhook: {str(e)}'}), 500

def verify_webhook(data: bytes, hmac: str, shop_domain: str) -> bool:
    """
    Verify Shopify webhook authenticity using HMAC
    """
    try:
        import hmac as hmac_lib
        import hashlib
        import base64

        # Get shop's webhook secret
        user = user_collection.find_one({'shopifyStoreName': shop_domain})
        if not user or 'shopifyWebhookSecret' not in user:
            logger.error(f"No webhook secret found for shop: {shop_domain}")
            return False

        secret = user['shopifyWebhookSecret'].encode('utf-8')

        # Calculate HMAC
        digest = hmac_lib.new(secret, data, hashlib.sha256).digest()
        computed_hmac = base64.b64encode(digest).decode('utf-8')

        return hmac == computed_hmac
    except Exception as e:
        logger.error(f"Error verifying webhook: {str(e)}")
        return False

@products_bp.route('/api/export-csv')
@login_required
def export_csv():
    item_type = request.args.get('itemType')
    vendor = request.args.get('vendor')
    product_type = request.args.get('productType')
    expansion_name = request.args.get('expansionName')
    search_term = request.args.get('searchTerm', '')
    in_stock_only = request.args.get('inStockOnly', 'false').lower() == 'true'
    sold_only = request.args.get('soldOnly', 'false').lower() == 'true'

    match_stage = {'username': current_user.username}
    if item_type == 'tcg':
        match_stage['tcgItem'] = True
    if vendor:
        match_stage['vendor'] = vendor
    if product_type:
        match_stage['product_type'] = product_type
    if expansion_name:
        match_stage['expansionName'] = expansion_name
    if search_term:
        match_stage['title'] = {'$regex': search_term, '$options': 'i'}
    if in_stock_only:
        match_stage['variants.inventory_quantity'] = {'$gt': 0}
    if sold_only:
        match_stage['last_sold_date'] = {'$exists': True}

    pipeline = [
        {'$match': match_stage},
        {'$project': {
            'title': 1,
            'variants': 1,
            'last_sold_date': 1,
            'last_repriced': 1,
            'number': 1,
            'expansionName': 1
        }}
    ]

    # Add sorting if specified
    sort_price = request.args.get('sortPrice')
    if sort_price == 'low-to-high':
        pipeline.append({'$addFields': {'lowest_price': {'$min': '$variants.price'}}})
        pipeline.append({'$sort': {'lowest_price': 1, 'title': 1}})
    elif sort_price == 'high-to-low':
        pipeline.append({'$addFields': {'highest_price': {'$max': '$variants.price'}}})
        pipeline.append({'$sort': {'highest_price': -1, 'title': 1}})
    elif sort_price == 'number-asc':
        # Sort by number ascending as strings to handle alphanumeric card numbers
        pipeline.append({
            '$addFields': {
                'sortNumber': {'$ifNull': ['$number', '']}  # Use empty string if number is null
            }
        })
        pipeline.append({'$sort': {'sortNumber': 1, 'title': 1}})
    elif sort_price == 'number-desc':
        # Sort by number descending as strings to handle alphanumeric card numbers
        pipeline.append({
            '$addFields': {
                'sortNumber': {'$ifNull': ['$number', '']}  # Use empty string if number is null
            }
        })
        pipeline.append({'$sort': {'sortNumber': -1, 'title': 1}})
    else:
        pipeline.append({'$sort': {'title': 1}})

    products = list(shopify_collection.aggregate(pipeline))

    csv_data = io.StringIO()
    csv_writer = csv.writer(csv_data)
    csv_writer.writerow(['Product Title', 'Variant Title', 'SKU', 'Price', 'Quantity', 'Number', 'Expansion', 'Last Sold Date', 'Last Repriced'])

    for product in products:
        for variant in product['variants']:
            # Format last_repriced date if it exists
            last_repriced = 'Never'
            if 'last_repriced' in product and product['last_repriced']:
                try:
                    # Handle MongoDB $date format
                    if isinstance(product['last_repriced'], dict) and '$date' in product['last_repriced']:
                        last_repriced_date = product['last_repriced']['$date']
                    else:
                        last_repriced_date = str(product['last_repriced'])

                    # Convert to formatted datetime
                    dt = datetime.fromisoformat(last_repriced_date.replace('Z', '+00:00'))
                    last_repriced = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    logger.error(f"Error formatting last_repriced date: {e}")
                    last_repriced = 'Error'

            csv_writer.writerow([
                product['title'],
                variant.get('title', 'N/A'),
                variant.get('sku', 'N/A'),
                f"${float(variant.get('price', 0)):.2f}",
                variant.get('inventory_quantity', 0),
                product.get('number', 'N/A'),
                product.get('expansionName', 'N/A'),
                product.get('last_sold_date', 'No Sales').strftime('%Y-%m-%d') if isinstance(product.get('last_sold_date'), datetime) else 'No Sales',
                last_repriced
            ])

    output = make_response(csv_data.getvalue())
    output.headers["Content-Disposition"] = "attachment; filename=shopify_products.csv"
    output.headers["Content-type"] = "text/csv"
    return output

@products_bp.route('/api/update_variant', methods=['POST'])
@login_required
def update_variant():
    data = request.json
    product_id = data.get('productId')
    variant_id = data.get('variantId')
    new_price = data.get('price')
    new_quantity = data.get('quantity')

    if not all([product_id, variant_id, new_price is not None, new_quantity is not None]):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Get the product from MongoDB
        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        # Get user profile for Shopify credentials
        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        shopify_store_name = user_profile['shopifyStoreName']
        shopify_access_token = user_profile['shopifyAccessToken']

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        # Update variant in MongoDB
        for variant in product['variants']:
            if str(variant['id']) == str(variant_id):
                variant['price'] = str(new_price)
                variant['inventory_quantity'] = new_quantity

                # Update inventory in Shopify
                inventory_item_id = variant['inventory_item_id']

                # Get location ID
                locations_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/locations.json"
                locations_response = requests.get(locations_url, headers=headers)
                locations_response.raise_for_status()
                locations = locations_response.json()['locations']
                if not locations:
                    return jsonify({'error': 'No locations found for the store'}), 400
                location_id = locations[0]['id']

                # Update price
                variant_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/variants/{variant_id}.json"
                variant_data = {
                    "variant": {
                        "id": variant_id,
                        "price": str(new_price)
                    }
                }
                response = requests.put(variant_url, headers=headers, json=variant_data)
                response.raise_for_status()

                # Update inventory
                inventory_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/inventory_levels/set.json"
                inventory_data = {
                    "location_id": location_id,
                    "inventory_item_id": inventory_item_id,
                    "available": new_quantity
                }
                inventory_response = requests.post(inventory_url, headers=headers, json=inventory_data)
                inventory_response.raise_for_status()
                break

        # Update MongoDB
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {'variants': product['variants']}}
        )

        return jsonify({
            'message': 'Variant updated successfully',
            'new_price': new_price,
            'new_quantity': new_quantity
        })

    except requests.RequestException as e:
        logger.error(f"Failed to update variant in Shopify: {str(e)}")
        return jsonify({'error': f'Failed to update variant in Shopify: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error updating variant: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500
@products_bp.route('/api/push_to_shopify', methods=['POST'])
@login_required
def push_to_shopify():
    data = request.json
    product_id = data.get('productId')
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        shopify_store_name = user_profile['shopifyStoreName']
        shopify_access_token = user_profile['shopifyAccessToken']

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        product_data = {
            "product": {
                "id": shopify_product_id,
                "title": product.get('title'),
                "body_html": product.get('body_html'),
                "vendor": product.get('vendor'),
                "product_type": product.get('product_type'),
                "variants": product.get('variants', [])
            }
        }

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{shopify_product_id}.json"

        response = requests.put(url, headers=headers, json=product_data)
        response.raise_for_status()

        return jsonify({'message': 'Successfully pushed to Shopify'})
    except requests.RequestException as e:
        return jsonify({'error': f'Failed to push to Shopify: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'An unexpected error occurred: {str(e)}'}), 500

@products_bp.route('/update_high_score', methods=['POST'])
@login_required
def update_high_score():
    data = request.json
    new_high_score = data.get('highScore')

    if new_high_score is None:
        return jsonify({'error': 'High score is required'}), 400

    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    current_high_score = user_profile.get('snakeHighScore', 0)

    if new_high_score > current_high_score:
        user_collection.update_one(
            {'username': current_user.username},
            {'$set': {'snakeHighScore': new_high_score}}
        )
        return jsonify({'message': 'High score updated successfully'}), 200
    else:
        return jsonify({'message': 'Current score is not higher than the existing high score'}), 200

@products_bp.route('/get_user_currency', methods=['GET'])
@products_bp.route('/shopify/products/get_user_currency', methods=['GET'])
@login_required
def get_user_currency():
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    currency = user_profile.get('currency', 'USD')
    return jsonify({'currency': currency})

@products_bp.route('/metafields')
@login_required
def metafields():
    return render_template('shopify_metafields.html')

@products_bp.route('/api/metafields', methods=['GET', 'POST', 'PUT', 'DELETE'])
@login_required
def manage_metafields():
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile['shopifyStoreName']
    shopify_access_token = user_profile['shopifyAccessToken']

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    if request.method == 'GET':
        product_id = request.args.get('productId')
        if not product_id:
            return jsonify({'error': 'Product ID is required'}), 400

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{product_id}/metafields.json"
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return jsonify(response.json()), 200
        except requests.RequestException as e:
            return jsonify({'error': f'Failed to fetch metafields: {str(e)}'}), 500

    elif request.method == 'POST':
        data = request.json
        product_id = data.get('productId')
        key = data.get('key')
        value = data.get('value')
        value_type = data.get('valueType')

        if not all([product_id, key, value, value_type]):
            return jsonify({'error': 'Missing required fields'}), 400

        metafield_data = {
            "metafield": {
                "namespace": "custom",
                "key": key,
                "value": value,
                "type": value_type
            }
        }

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{product_id}/metafields.json"
        try:
            response = requests.post(url, headers=headers, json=metafield_data)
            response.raise_for_status()
            return jsonify({'message': 'Metafield added successfully'}), 200
        except requests.RequestException as e:
            return jsonify({'error': f'Failed to add metafield: {str(e)}'}), 500

    elif request.method == 'PUT':
        data = request.json
        product_id = data.get('productId')
        metafield_id = data.get('metafieldId')
        key = data.get('key')
        value = data.get('value')
        value_type = data.get('valueType')

        if not all([product_id, metafield_id, key, value, value_type]):
            return jsonify({'error': 'Missing required fields'}), 400

        metafield_data = {
            "metafield": {
                "id": metafield_id,
                "value": value,
                "type": value_type
            }
        }

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{product_id}/metafields/{metafield_id}.json"
        try:
            response = requests.put(url, headers=headers, json=metafield_data)
            response.raise_for_status()
            return jsonify({'message': 'Metafield updated successfully'}), 200
        except requests.RequestException as e:
            return jsonify({'error': f'Failed to update metafield: {str(e)}'}), 500

    elif request.method == 'DELETE':
        data = request.json
        product_id = data.get('productId')
        metafield_id = data.get('metafieldId')

        if not all([product_id, metafield_id]):
            return jsonify({'error': 'Missing required fields'}), 400

        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products/{product_id}/metafields/{metafield_id}.json"
        try:
            response = requests.delete(url, headers=headers)
            response.raise_for_status()
            return jsonify({'message': 'Metafield deleted successfully'}), 200
        except requests.RequestException as e:
            return jsonify({'error': f'Failed to delete metafield: {str(e)}'}), 500

    return jsonify({'error': 'Invalid request method'}), 405

@products_bp.route('/api/products/search', methods=['GET'])
@login_required
def search_products():
    query = request.args.get('query', '')
    vendor = request.args.get('vendor', '')
    product_type = request.args.get('productType', '')

    pipeline = [
        {'$match': {'username': current_user.username}},
    ]

    if query:
        pipeline.append({'$match': {'title': {'$regex': query, '$options': 'i'}}})
    if vendor:
        pipeline.append({'$match': {'vendor': vendor}})
    if product_type:
        pipeline.append({'$match': {'product_type': product_type}})

    pipeline.extend([
        {'$project': {'_id': 1, 'title': 1, 'id': 1}}
        # No limit on search results - return all matching products
    ])

    products = list(shopify_collection.aggregate(pipeline))

    for product in products:
        product['_id'] = str(product['_id'])

    return jsonify(products)

@products_bp.route('/api/user-products', methods=['GET'])
@login_required
def get_user_products():
    vendor = request.args.get('vendor', '')
    product_type = request.args.get('productType', '')

    query = {'username': current_user.username}
    if vendor:
        query['vendor'] = vendor
    if product_type:
        query['product_type'] = product_type

    products = list(shopify_collection.find(
        query,
        {'_id': 1, 'title': 1, 'vendor': 1, 'product_type': 1}
    ))

    for product in products:
        product['_id'] = str(product['_id'])

    return jsonify({'products': products})

@products_bp.route('/api/price-settings', methods=['GET'])
@login_required
def get_price_settings():
    product_id = request.args.get('productId')
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    # Get the product details
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    custom_stepping = user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})
    min_price = user_profile.get('minPrice', 0.2)
    currency = user_profile.get('currency', 'USD')

    # Check for advanced pricing rules
    advanced_rules = user_profile.get('advancedPricingRules', {})
    key = f"{product.get('vendor')}_{product.get('product_type')}_{product.get('expansionName')}"
    actual_stepping = advanced_rules.get(key, custom_stepping)

    # Create ordered stepping dictionary with default values in specific order
    ordered_stepping = {}
    condition_order = ['nm', 'lp', 'mp', 'hp', 'dm']  # Specific display order
    default_values = {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65}

    # Ensure conditions are added in the correct order
    for condition in condition_order:
        ordered_stepping[condition] = actual_stepping.get(condition, default_values[condition])

    return jsonify({
        'customStepping': ordered_stepping,
        'minPrice': min_price,
        'currency': currency,
        'isAdvancedPricing': key in advanced_rules,
        'expansionName': product.get('expansionName')
    })

@products_bp.route('/api/add_metafield', methods=['POST'])
@login_required
def add_metafield():
    data = request.json
    product_id = data.get('productId')
    key = data.get('key')

    if not product_id or not key:
        return jsonify({'error': 'Product ID and metafield key are required'}), 400

    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile['shopifyStoreName']
    shopify_access_token = user_profile['shopifyAccessToken']

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    # Fetch the Scryfall data for the product
    scryfall_data = fetch_scryfall_data(product_id)
    if not scryfall_data:
        return jsonify({'error': 'Scryfall data not found for this product'}), 404

    value = scryfall_data.get(key.replace('card_', ''))
    if value is None:
        return jsonify({'error': f'Scryfall data does not contain {key}'}), 404

    # Fetch the product from the database to get the Shopify product ID
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    metafield_data = {
        "metafield": {
            "namespace": "scryfall",
            "key": key,
            "value": str(value),
            "type": "single_line_text_field"
        }
    }

    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-10/products/{product['id']}/metafields.json"
    try:
        response = requests.post(url, headers=headers, json=metafield_data)
        response.raise_for_status()
        return jsonify({'message': 'Metafield added successfully'}), 200
    except requests.RequestException as e:
        return jsonify({'error': f'Failed to add metafield: {str(e)}'}), 500

def fetch_scryfall_data(product_id):
    product = shopify_collection.find_one({"_id": ObjectId(product_id)})
    if not product:
        return None
    tcgplayer_id = product.get('productId')
    if not tcgplayer_id:
        return None
    scryfall_data = db.scryfall.find_one({"tcgplayer_id": tcgplayer_id})
    if not scryfall_data:
        return None
    # Ensure all values in scryfall_data are strings
    return {k: str(v) if v is not None else '' for k, v in scryfall_data.items()}
@products_bp.route('/api/add_all_metafields', methods=['POST'])
@login_required
def add_all_metafields():
    data = request.json
    product_id = data.get('productId')

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile['shopifyStoreName']
    shopify_access_token = user_profile['shopifyAccessToken']

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    # Fetch the Scryfall data for the product
    scryfall_data = fetch_scryfall_data(product_id)
    if not scryfall_data:
        return jsonify({'error': 'Scryfall data not found for this product'}), 404

    # Fetch the product from the database to get the Shopify product ID
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    metafields = []
    for key, value in scryfall_data.items():
        if value:  # This will skip empty strings as well as None values
            metafields.append({
                "namespace": "scryfall",
                "key": f"card_{key}",
                "value": value,  # No need to str() as we've already converted in fetch_scryfall_data
                "type": "single_line_text_field"
            })

    metafield_data = {
        "metafields": metafields
    }

    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-10/products/{product['id']}/metafields.json"
    try:
        response = requests.post(url, headers=headers, json=metafield_data)
        response.raise_for_status()
        return jsonify({'message': 'All metafields added successfully'}), 200
    except requests.RequestException as e:
        logger.error("Failed to add metafields")
        return jsonify({'error': f'Failed to add metafields: {str(e)}', 'details': e.response.content if e.response else 'No response content'}), 500

@products_bp.route('/api/update_price', methods=['POST'])
@login_required
def update_price():
    data = request.json
    product_id = data.get('productId')
    variant_id = data.get('variantId')
    new_price = data.get('price')

    if not all([product_id, variant_id, new_price]):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Get the product from MongoDB
        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        # Get user profile for Shopify credentials
        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        shopify_store_name = user_profile['shopifyStoreName']
        shopify_access_token = user_profile['shopifyAccessToken']

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        # Update price in MongoDB
        for variant in product['variants']:
            if str(variant['id']) == str(variant_id):
                variant['price'] = str(new_price)
                break

        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {'variants': product['variants']}}
        )

        # Push to Shopify
        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        # Update variant price directly using the variant endpoint
        url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/variants/{variant_id}.json"
        variant_data = {
            "variant": {
                "id": variant_id,
                "price": str(new_price)
            }
        }
        response = requests.put(url, headers=headers, json=variant_data)
        response.raise_for_status()

        return jsonify({
            'message': 'Price updated successfully',
            'new_price': new_price
        })

    except requests.RequestException as e:
        logger.error(f"Failed to update price in Shopify: {str(e)}")
        return jsonify({'error': f'Failed to update price in Shopify: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error updating price: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@products_bp.route('/api/staged-inventory/<item_id>', methods=['DELETE'])
@login_required
def delete_staged_inventory(item_id):
    try:
        result = staged_inventory_collection.delete_one({
            "_id": ObjectId(item_id),
            "username": current_user.username
        })

        if result.deleted_count > 0:
            return jsonify({"message": "Item deleted successfully", "error": None}), 200
        else:
            return jsonify({"message": "Item not found", "error": None}), 200
    except Exception as e:
        logger.error(f"Error deleting staged inventory item: {str(e)}")
        return jsonify({"message": "Failed to delete item", "error": str(e)}), 500

@products_bp.route('/api/sync_to_shopify', methods=['POST'])
@login_required
def sync_to_shopify():
    data = request.json
    product_id = data.get('productId')
    item_id = data.get('itemId')

    try:
        # Handle staged inventory item if provided
        if item_id:
            staged_item = staged_inventory_collection.find_one({
                '_id': ObjectId(item_id),
                'username': current_user.username
            })
            if not staged_item or not staged_item.get('shProduct', {}).get('variant'):
                return jsonify({'message': 'Invalid staged inventory item', 'error': None}), 200

            # Update product variant quantity
            product = shopify_collection.find_one({
                'username': current_user.username,
                'id': staged_item['shProduct']['id']
            })
            if not product:
                return jsonify({'message': 'Product not found', 'error': None}), 200

            # Update only the matching variant quantity
            variant_info = staged_item['shProduct']['variant']
            updated_variants = []
            for variant in product['variants']:
                if str(variant['id']) == str(variant_info['id']):
                    variant['inventory_quantity'] = staged_item.get('quantity', 0)
                updated_variants.append(variant)

            # Update MongoDB with specific variant changes and cleanup staged item
            shopify_collection.update_one(
                {'_id': product['_id']},
                {'$set': {'variants': updated_variants}}
            )
            staged_inventory_collection.delete_one({'_id': ObjectId(item_id)})
            product_id = str(product['_id'])

        # Get product and user info
        if not product_id:
            return jsonify({'message': 'Product ID is required', 'error': None}), 200

        product = shopify_collection.find_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        if not product:
            return jsonify({'message': 'Product not found', 'error': None}), 200

        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'message': 'User profile not found', 'error': None}), 200

        # Prepare Shopify API request
        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": user_profile['shopifyAccessToken']
        }

        # Get the location ID
        locations_url = f"https://{user_profile['shopifyStoreName']}.myshopify.com/admin/api/2023-04/locations.json"
        locations_response = requests.get(locations_url, headers=headers)
        locations_response.raise_for_status()
        locations = locations_response.json()['locations']
        if not locations:
            return jsonify({'error': 'No locations found for the store'}), 400
        location_id = locations[0]['id']

        # Initialize response data
        inventory_updates = []
        success = True
        status_code = 200

        # Update inventory levels for each variant
        for variant in product['variants']:
            try:
                # Get current inventory level
                inventory_levels_url = f"https://{user_profile['shopifyStoreName']}.myshopify.com/admin/api/2023-04/inventory_levels.json?inventory_item_ids={variant['inventory_item_id']}"
                current_levels_response = requests.get(inventory_levels_url, headers=headers)
                current_levels_response.raise_for_status()
                current_levels = current_levels_response.json().get('inventory_levels', [])
                current_quantity = current_levels[0].get('available', 0) if current_levels else 0

                # Calculate the adjustment amount (difference between desired and current quantity)
                adjustment = variant.get('inventory_quantity', 0) - current_quantity

                # Only adjust inventory for the matched variant from staged inventory
                if item_id and staged_item and str(variant['id']) == str(staged_item['shProduct']['variant']['id']):
                    # Add to inventory using the adjust endpoint
                    adjust_url = f"https://{user_profile['shopifyStoreName']}.myshopify.com/admin/api/2023-04/inventory_levels/adjust.json"
                    inventory_data = {
                        "location_id": location_id,
                        "inventory_item_id": variant['inventory_item_id'],
                        "available_adjustment": variant.get('inventory_quantity', 0)  # Add the staged quantity
                    }
                    inventory_response = requests.post(adjust_url, headers=headers, json=inventory_data)
                    inventory_response.raise_for_status()

                inventory_updates.append({
                    'variant_id': variant['id'],
                    'success': True,
                    'old_quantity': current_quantity,
                    'new_quantity': variant.get('inventory_quantity', 0)
                })
            except Exception as e:
                inventory_updates.append({
                    'variant_id': variant['id'],
                    'success': False,
                    'error': str(e)
                })
                success = False
                status_code = 500

        return jsonify({
            'message': 'Inventory levels updated in Shopify',
            'variants_updated': len(product['variants']),
            'inventory_updates': inventory_updates,
            'success': success,
            'status_code': status_code
        })

    except Exception as e:
        logger.error(f"Error during sync: {str(e)}")
        return jsonify({'error': str(e)}), 500

@products_bp.route('/api/match-variant', methods=['POST'])
@login_required
def match_variant():
    data = request.json
    item_id = data.get('itemId')
    variant_id = data.get('variantId')

    if not item_id or not variant_id:
        return jsonify({'message': 'Missing required fields', 'error': None}), 200

    try:
        # Get the staged item
        staged_item = staged_inventory_collection.find_one({
            '_id': ObjectId(item_id),
            'username': current_user.username
        })

        if not staged_item:
            return jsonify({'message': 'Staged item not found', 'error': None}), 200

        # Get the Shopify product
        shopify_product = shopify_collection.find_one({
            'username': current_user.username,
            'id': staged_item['shProduct']['id']
        })

        if not shopify_product:
            return jsonify({'message': 'Shopify product not found', 'error': None}), 200

        # Find the matching variant
        matched_variant = None
        for variant in shopify_product['variants']:
            if str(variant['id']) == str(variant_id):
                matched_variant = variant
                break

        if not matched_variant:
            return jsonify({'message': 'Variant not found', 'error': None}), 200

        # Update the staged item with the matched variant and set matched_variant flag
        staged_inventory_collection.update_one(
            {'_id': ObjectId(item_id)},
            {'$set': {
                'shProduct.variant': matched_variant,
                'matched_variant': True
            }}
        )

        return jsonify({
            'message': 'Variant matched successfully',
            'variant': matched_variant,
            'error': None
        })

    except Exception as e:
        logger.error(f"Error matching variant: {str(e)}")
        return jsonify({'message': 'Failed to match variant', 'error': str(e)}), 500

@products_bp.route('/api/variants/<shopify_id>', methods=['GET'])
@login_required
def get_variants(shopify_id):
    try:
        shopify_id_int = int(shopify_id)
    except ValueError:
        logger.error(f"Invalid shopify_id format: {shopify_id}")
        return jsonify({'variants': [], 'error': None}), 200

    try:
        # Try to find product by either id or productId field
        product = shopify_collection.find_one({
            'username': current_user.username,
            '$or': [
                {'id': shopify_id_int},
                {'productId': str(shopify_id_int)}  # Try string version too
            ]
        })

        if not product:
            logger.warning(f"Product not found for ID: {shopify_id}")
            return jsonify({'variants': [], 'error': None}), 200

        variants = product.get('variants', [])
        if not variants:
            logger.info(f"No variants found for product ID: {shopify_id}")
            return jsonify({'variants': [], 'error': None}), 200

        # Get any matched variants from staged inventory
        staged_items = list(staged_inventory_collection.find({
            'username': current_user.username,
            'shProduct.id': shopify_id_int
        }))

        # Create a set of matched variant IDs
        matched_variant_ids = set()
        for item in staged_items:
            variant = item.get('shProduct', {}).get('variant', {})
            if variant and 'id' in variant:
                matched_variant_ids.add(str(variant['id']))

        # If there's only one variant, mark it as pre-selected
        if len(variants) == 1 and isinstance(variants[0], dict):
            variants[0]['matched'] = True
            # Auto-match in staged inventory if not already matched
            if variants[0].get('id') and str(variants[0]['id']) not in matched_variant_ids:
                try:
                    staged_inventory_collection.update_one(
                        {
                            'username': current_user.username,
                            'shProduct.id': shopify_id_int,
                            'shProduct.variant': None  # Only match if not already matched
                        },
                        {'$set': {
                            'shProduct.variant': variants[0],
                            'matched_variant': True
                        }}
                    )
                except Exception as e:
                    logger.error(f"Error auto-matching single variant: {str(e)}")
        else:
            # Mark variants as matched based on staged inventory
            for variant in variants:
                if not isinstance(variant, dict):
                    continue
                variant_id = variant.get('id')
                if variant_id is not None:
                    variant['matched'] = str(variant_id) in matched_variant_ids
                else:
                    variant['matched'] = False

        return jsonify({'variants': variants, 'error': None}), 200

    except requests.RequestException as e:
        logger.error(f"Failed to fetch variants from Shopify: {str(e)}")
        return jsonify({'error': f'Failed to fetch variants: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error getting variants: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@products_bp.route('/api/save_all_matches', methods=['POST'])
@login_required
def save_all_matches():
    try:
        data = request.json
        matches = data.get('matches', [])

        if not matches:
            return jsonify({'message': 'No matches provided', 'error': None}), 200

        success_count = 0
        error_count = 0

        for match in matches:
            try:
                item_id = match.get('itemId')
                variant = match.get('variant')

                if not item_id or not variant:
                    error_count += 1
                    continue

                # Update the staged item with the matched variant
                result = staged_inventory_collection.update_one(
                    {
                        '_id': ObjectId(item_id),
                        'username': current_user.username
                    },
                    {'$set': {
                        'shProduct.variant': variant,
                        'matched_variant': True
                    }}
                )

                if result.modified_count > 0:
                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                logger.error(f"Error saving match: {str(e)}")
                error_count += 1

        return jsonify({
            'message': f'Successfully saved {success_count} matches with {error_count} errors',
            'success_count': success_count,
            'error_count': error_count,
            'error': None
        })

    except Exception as e:
        logger.error(f"Error in save_all_matches: {str(e)}")
        return jsonify({'message': 'Failed to save matches', 'error': str(e)}), 500

@products_bp.route('/api/update_manual_override', methods=['POST'])
@login_required
def update_manual_override():
    data = request.json
    product_id = data.get('productId')
    manual_override = data.get('manualOverride')

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        result = shopify_collection.update_one(
            {'_id': ObjectId(product_id), 'username': current_user.username},
            {'$set': {'manualOverride': manual_override}}
        )

        if result.modified_count > 0:
            return jsonify({'message': 'Manual override setting updated successfully'}), 200
        else:
            return jsonify({'error': 'Product not found or no changes made'}), 404

    except Exception as e:
        logger.error(f"Error updating manual override: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@products_bp.route('/api/test_settings', methods=['POST'])
@login_required
def test_settings():
    """Get a random product and test the current pricing settings on it"""
    try:
        # Get a random product for the current user
        pipeline = [
            {'$match': {'username': current_user.username}},
            {'$sample': {'size': 1}}  # Get 1 random document
        ]

        product = next(shopify_collection.aggregate(pipeline), None)
        if not product:
            return jsonify({'error': 'No products found'}), 404

        # Store original variant prices
        original_variants = product['variants']
        original_prices = {str(v['id']): float(v['price']) for v in original_variants}

        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return jsonify({'error': 'TCGPlayer API key not found'}), 500
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']

        # Initialize shautopricing collections and use process_single_product function
        from shautopricing import init_collections, process_single_product
        init_collections(db)
        updated_variants, price_changes = process_single_product(product, current_user.username, tcgplayer_api_key)

        if not updated_variants:
            return jsonify({
                'message': 'No price changes needed',
                'product': {
                    'title': product['title'],
                    'image': product.get('image', {}).get('src'),
                    'variants': original_variants
                }
            })

        # Format response with before/after comparison
        # Get image URL from either image.src or images[0].src
        image_url = product.get('image', {}).get('src')
        if not image_url and product.get('images') and len(product['images']) > 0:
            image_url = product['images'][0].get('src')

        response_data = {
            'message': 'Test pricing completed',
            'product': {
                'title': product['title'],
                'image': image_url
            },
            'price_changes': [{
                'variant_title': change['variant_title'],
                'old_price': change['old_price'],
                'new_price': change['new_price'],
                'difference': round(change['new_price'] - change['old_price'], 2),
                'difference_percent': round((change['new_price'] - change['old_price']) / change['old_price'] * 100, 1)
            } for change in price_changes]
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error testing settings: {str(e)}")
        return jsonify({'error': f'An error occurred while testing settings: {str(e)}'}), 500

@products_bp.route('/api/reprice_by_sku', methods=['POST'])
@login_required
def reprice_by_sku():
    data = request.json
    product_id = data.get('productId')
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        # Get product from MongoDB
        product = shopify_collection.find_one(
            {'_id': ObjectId(product_id), 'username': current_user.username}
        )
        if not product:
            logger.error(f"Product not found for ID: {product_id}")
            return jsonify({'error': 'Product not found'}), 404

        # Skip if manual override is enabled
        if product.get('manualOverride', False):
            return jsonify({'message': 'Product has manual price override enabled'}), 200

        # Get user profile
        user_profile = user_collection.find_one(
            {'username': current_user.username}
        )
        if not user_profile:
            logger.error(f"User profile not found for username: {current_user.username}")
            return jsonify({'error': 'User profile not found'}), 404

        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return jsonify({'error': 'TCGPlayer API key not found'}), 500
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']

        # Import skupricer functionality
        from skupricer import get_tcgplayer_price, calculate_price, get_exchange_rate

        # Store original variant prices for comparison
        original_variants = product['variants']
        original_prices = {str(v['id']): float(v['price']) for v in original_variants}

        # Get user currency and exchange rate
        user_currency = user_profile.get('currency', 'USD')
        min_price_local = user_profile.get('minPrice', 0.2)
        exchange_rate = get_exchange_rate(user_currency)

        # Collect SKU IDs for all variants in this product
        variants = product.get('variants', [])
        sku_ids = []
        for variant in variants:
            if 'skuId' in variant:
                sku_ids.append(variant['skuId'])
            else:
                return jsonify({'error': 'One or more variants missing SKU ID. Please match SKUs first.'}), 400

        # Get prices for all SKUs in this product at once
        sku_prices = get_tcgplayer_price(sku_ids, tcgplayer_api_key)

        # Process each variant
        price_changes = []
        variants_changed = False

        for variant in variants:
            old_price = float(variant.get('price', '0.00'))
            sku_id = str(variant['skuId'])
            price_data = sku_prices.get(sku_id, {})

            # Calculate new price
            new_price, is_missing = calculate_price(
                variant=variant,
                price_data=price_data,
                user_profile=user_profile,
                min_price_local=min_price_local,
                exchange_rate=exchange_rate,
                user_currency=user_currency
            )

            # Enforce condition-based price hierarchy
            # Find the variant's position in the variants array
            variant_index = variants.index(variant)

            # If this is not the first variant (Near Mint), check against previous variant
            if variant_index > 0:
                prev_variant = variants[variant_index - 1]
                prev_price = float(prev_variant.get('price', '0.00'))

                # Ensure this variant's price doesn't exceed the previous (better condition) variant
                if new_price > prev_price:
                    logger.info(f"Adjusting price for {variant.get('title')} from ${new_price:.2f} to ${prev_price:.2f} to maintain condition hierarchy")
                    new_price = prev_price

            if not is_missing and new_price is not None:
                # Update variant price
                variant['price'] = f"{new_price:.2f}"
                variants_changed = True

                # Record price change
                price_changes.append({
                    'variant_title': variant.get('title', ''),
                    'old_price': old_price,
                    'new_price': new_price
                })

        # Update MongoDB if prices changed
        if variants_changed:
            # Create summary text
            summary_lines = []
            summary_lines.append("*" * 100)
            summary_lines.append("FINAL PRICE CALCULATION SUMMARY")
            summary_lines.append("*" * 100)
            summary_lines.append(f"\nProduct: {product.get('title')}")
            summary_lines.append(f"User: {current_user.username}")
            summary_lines.append(f"Currency: {user_currency}")
            summary_lines.append(f"Exchange Rate: {exchange_rate}")

            # Add TCGPlayer price fetch details
            summary_lines.append(f"\nTCGPlayer Prices (fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')})")
            for variant in variants:
                sku_id = variant['skuId']
                price_data = sku_prices.get(str(sku_id), {})
                if price_data:
                    summary_lines.append(f"\n  {variant.get('title')} (SKU {sku_id}):")
                    for price_type, value in price_data.items():
                        summary_lines.append(f"    - {price_type}: ${value:.2f}")
                else:
                    summary_lines.append(f"\n  {variant.get('title')} (SKU {sku_id}): No valid prices")

            summary_lines.append("\nSKU IDs:")
            for variant in variants:
                summary_lines.append(f"  {variant.get('title')}: {variant['skuId']}")

            # Add variant-specific details
            for variant in variants:
                summary_lines.append("\n" + "-" * 80)
                summary_lines.append(f"VARIANT: {variant.get('title')} (SKU ID: {variant['skuId']})")
                summary_lines.append("-" * 80)

                old_price = original_prices.get(str(variant['id']), 0.0)
                new_price = float(variant['price'])

                summary_lines.append("\n1. STARTING POINT")
                summary_lines.append(f"   Current price: ${old_price:.2f} USD")

                summary_lines.append("\n7. FINAL RESULT")
                summary_lines.append(f"   Starting price: ${old_price:.2f} {user_currency}")
                summary_lines.append(f"   Final price: ${new_price:.2f} {user_currency}")
                if old_price > 0:
                    change_pct = ((new_price/old_price)-1)*100
                    summary_lines.append(f"   Change: {change_pct:+.2f}%")

            summary_lines.append("\n" + "*" * 100 + "\n")
            summary_text = "\n".join(summary_lines)

            # Update MongoDB with new prices and summary
            update_doc = {
                '$set': {
                    'variants': variants,
                    'needsPushing': True,
                    'last_repriced': datetime.now(),
                    'summary': summary_text
                }
            }

            shopify_collection.update_one(
                {'_id': ObjectId(product_id)},
                update_doc
            )

            return jsonify({
                'message': 'Successfully repriced product using SKU data',
                'price_changes': price_changes
            })
        else:
            return jsonify({'message': 'No price changes needed'})

    except Exception as e:
        logger.error(f"Error during SKU repricing: {str(e)}")
        return jsonify({'error': f'An error occurred during repricing: {str(e)}'}), 500

@products_bp.route('/api/delete_product', methods=['POST'])
@login_required
def delete_product():
    data = request.json
    product_id = data.get('productId')

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    shopify_store_name = user_profile['shopifyStoreName']
    shopify_access_token = user_profile['shopifyAccessToken']

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopify_access_token
    }

    # Fetch the product from the database to get the Shopify product ID
    product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    shopify_product_id = product.get('id')
    if not shopify_product_id:
        return jsonify({'error': 'Shopify product ID not found'}), 404

    url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-10/products/{shopify_product_id}.json"
    try:
        response = requests.delete(url, headers=headers)
        response.raise_for_status()

        # Delete the product from the local database
        shopify_collection.delete_one({'_id': ObjectId(product_id), 'username': current_user.username})

        return jsonify({'message': 'Product deleted successfully from Shopify and local database', 'shopify_response': response.text}), 200
    except requests.RequestException as e:
        logging.error(f"Failed to delete product: {str(e)}")
        logging.error(f"Response content: {e.response.content if e.response else 'No response content'}")
        return jsonify({'error': f'Failed to delete product: {str(e)}', 'details': e.response.content if e.response else 'No response content'}), 500
