from typing import Dict, List, Optional, Tuple, Union
import re
import logging

logger = logging.getLogger(__name__)

class PricingCalculator:
    def __init__(self, settings: dict, currency: str = 'USD'):
        """
        Initialize the pricing calculator with Shopify pricing settings.
        
        Args:
            settings (dict): Shopify pricing settings from ShopifySettings model
            currency (str): Currency code (e.g., 'USD', 'GBP')
        """
        self.settings = settings
        self.currency = currency
        
        # Set defaults for any missing settings
        required_settings = [
            'minPrice', 'price_point', 'price_rounding_enabled', 'price_rounding_thresholds',
            'use_highest_price', 'price_comparison_pairs', 'price_modifiers', 'price_preference_order',
            'game_minimum_prices', 'advancedPricingRules', 'customStepping',
            'tcg_trend_increasing', 'tcg_trend_decreasing'
        ]
        
        defaults = {
            'minPrice': 0.50,
            'price_point': 'Low Price',
            'price_rounding_enabled': False,
            'price_rounding_thresholds': [49, 99],
            'use_highest_price': False,
            'price_comparison_pairs': [],
            'price_modifiers': {},
            'price_preference_order': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'],
            'game_minimum_prices': {},
            'advancedPricingRules': {},
            'customStepping': {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50},
            'tcg_trend_increasing': 0.0,
            'tcg_trend_decreasing': 0.0
        }
        
        for setting in required_settings:
            if setting not in self.settings:
                self.settings[setting] = defaults[setting]

    def apply_price_comparison(self, pricing_data: Dict[str, float]) -> Tuple[float, bool]:
        """
        Apply price comparison rules to determine the base price.
        
        Args:
            pricing_data (Dict[str, float]): Dictionary of price types and their values
            
        Returns:
            Tuple[float, bool]: (calculated price, is_missing flag)
        """
        import logging
        import traceback
        logger = logging.getLogger(__name__)
        
        try:
            if not pricing_data:
                logger.error("No pricing data provided")
                return None, True
                
            # Log available prices
            logger.info("Available prices:")
            for price_type, price in pricing_data.items():
                logger.info(f"  {price_type}: {price}")
                
            price_modifiers = self.settings.get('price_modifiers', {})
            
            # Apply modifiers to all prices upfront
            modified_prices = {}
            for price_type, price in pricing_data.items():
                if price is not None:
                    try:
                        modifier = price_modifiers.get(price_type, 0)
                        modified_price = round(float(price) * (1 + modifier/100), 2)
                        modified_prices[price_type] = modified_price
                        if modifier != 0:
                            logger.info(f"Applied {modifier}% modifier to {price_type}: {price} -> {modified_price}")
                    except (ValueError, TypeError) as e:
                        logger.error(f"Error applying modifier to {price_type}: {price} - {str(e)}")
                        # Skip this price but continue with others
                        continue
            
            if not modified_prices:
                logger.error("No valid prices after applying modifiers")
                return None, True
            
            if self.settings.get('use_highest_price', False):
                pairs = self.settings.get('price_comparison_pairs', [])
                logger.info("Using highest price mode with pairs:")
                
                highest_price = None
                winning_pair = None
                
                for pair in pairs:
                    try:
                        price1 = modified_prices.get(pair[0])
                        price2 = modified_prices.get(pair[1])
                        
                        if price1 is not None and price2 is not None:
                            pair_max = max(price1, price2)
                            winner = pair[0] if price1 >= price2 else pair[1]
                            logger.info(f"  Comparing {pair[0]}({price1}) vs {pair[1]}({price2}) -> {winner}({pair_max})")
                            
                            if highest_price is None or pair_max > highest_price:
                                highest_price = pair_max
                                winning_pair = f"{pair[0]} vs {pair[1]}"
                    except Exception as e:
                        logger.error(f"Error comparing pair {pair}: {str(e)}")
                        continue
                
                if highest_price is not None:
                    logger.info(f"Selected highest price {highest_price} from {winning_pair}")
                    return highest_price, False
            
            # Use price preference order
            price_preferences = self.settings.get('price_preference_order', 
                ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
                
            logger.info("Using price preference order:")
            for i, price_type in enumerate(price_preferences):
                logger.info(f"  {i+1}. {price_type}")
                if price_type in modified_prices:
                    price = modified_prices[price_type]
                    logger.info(f"Selected {price_type} with value {price}")
                    return price, False
            
            # If we get here, try any available price as a fallback
            logger.warning("No price found using preference order, trying any available price")
            for price_type, price in modified_prices.items():
                logger.info(f"Using fallback price {price_type}: {price}")
                return price, False
                    
            logger.error("No valid prices found after applying all rules")
            return None, True
            
        except Exception as e:
            logger.error(f"Error in apply_price_comparison: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None, True

    def apply_game_rules(self, price: float, game_name: str, product_type: str, rarity: str = None) -> float:
        if not game_name:
            return price
            
        game_settings = self.settings.get('game_minimum_prices', {}).get(game_name, {})
        
        game_default_min = game_settings.get('default_min_price')
        if game_default_min is not None:
            price = max(price, game_default_min)
                
        if rarity and 'rarities' in game_settings:
            rarity_min = game_settings['rarities'].get(rarity)
            if rarity_min is not None:
                price = max(price, rarity_min)
                    
        return price

    def apply_condition_stepping(self, price: float, condition: str, vendor: str, 
                               product_type: str, expansion: str) -> float:
        """
        Apply condition-based price stepping rules
        """
        import logging
        logger = logging.getLogger(__name__)
        
        # First check advanced rules
        key = f"{vendor}_{product_type}_{expansion}"
        stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
        
        # If no advanced rules, use custom stepping
        if not stepping_rules:
            stepping_rules = self.settings.get('customStepping')
            if not stepping_rules:
                stepping_rules = {
                    'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50
                }
            logger.info(f"Using custom stepping rules: {stepping_rules}")
        else:
            logger.info(f"Using advanced stepping rules for {key}: {stepping_rules}")

        condition_map = {
            'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
            'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
            'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
            'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
            'damaged': 'dm', 'dm': 'dm'
        }
            
        condition_code = condition_map.get(condition.lower().strip(), 'nm')
        
        stepping_percentage = stepping_rules.get(condition_code, 100)
        
        # Convert percentage to decimal
        stepping_decimal = float(stepping_percentage) / 100
        
        logger.info(f"Condition: {condition} -> {condition_code}")
        logger.info(f"Stepping percentage: {stepping_percentage}% -> {stepping_decimal}")
        logger.info(f"Original price: ${price}")
        logger.info(f"After stepping: ${round(price * stepping_decimal, 2)}")
        
        return round(price * stepping_decimal, 2)

    def apply_print_type_minimum(self, price: float, printing_type: str, game_name: str) -> Tuple[float, Optional[str]]:
        """
        Apply minimum price based on print type if applicable.
        
        Args:
            price (float): Current calculated price
            printing_type (str): The printing type (e.g., 'Foil', 'Reverse Holofoil')
            game_name (str): The game name (e.g., 'Pokemon', 'One Piece Card Game')
            
        Returns:
            Tuple[float, Optional[str]]: (adjusted price, details message or None if no change)
        """
        if not game_name or not printing_type:
            return price, None
            
        game_settings = self.settings.get('game_minimum_prices', {}).get(game_name, {})
        if not game_settings or 'print_types' not in game_settings:
            return price, None
            
        print_types = game_settings.get('print_types', {})
        min_price = None
        details = None
        
        # Check for exact match
        if printing_type in print_types:
            min_price = print_types[printing_type]
            details = f"Applied minimum price for {printing_type}"
        # Check for partial match (e.g., 'Foil' for 'Reverse Holofoil')
        elif 'Foil' in print_types and 'foil' in printing_type.lower():
            min_price = print_types['Foil']
            details = f"Using 'Foil' minimum price for {printing_type}"
        # Check for 'Normal' as fallback
        elif 'Normal' in print_types:
            min_price = print_types['Normal']
            details = f"Using 'Normal' minimum price for {printing_type}"
            
        if min_price is not None and price < min_price:
            return min_price, details
            
        return price, None

    def apply_price_rounding(self, price: float) -> float:
        if not self.settings.get('price_rounding_enabled', False):
            return round(price, 2)
            
        thresholds = sorted(self.settings.get('price_rounding_thresholds', [49, 99]))
        dollars = int(price)
        cents = round((price - dollars) * 100)
        
        for threshold in thresholds:
            if cents <= threshold:
                return dollars + (threshold / 100)
                
        return dollars + 1

    def calculate_final_price(self, sku_info: dict, catalog_item: dict) -> Tuple[float, bool, List[dict]]:
        """
        Calculate the final price in local currency.
        All input prices in sku_info['pricingInfo'] should already be in local currency.
        """
        import traceback
        price_history = []
        try:
            # Log input data for debugging
            logger.info(f"Starting price calculation with:")
            logger.info(f"  SKU Info: {sku_info}")
            logger.info(f"  Catalog Item: {catalog_item}")
            
            # Get base price in local currency using price comparison rules
            base_price, is_missing = self.apply_price_comparison(sku_info.get('pricingInfo', {}))
            if is_missing:
                logger.error("Failed to get base price from price comparison")
                return None, True, price_history
                
            price_history.append({
                'step': 'Initial Price',
                'old_price': 0,
                'new_price': base_price,
                'details': 'Price after applying comparison rules'
            })

            # Apply TCG trend adjustments if enabled
            if base_price > 0:
                old_price = base_price
                try:
                    tcg_trend_increasing = float(self.settings.get('tcg_trend_increasing', 0))
                    tcg_trend_decreasing = float(self.settings.get('tcg_trend_decreasing', 0))
                    
                    if tcg_trend_increasing > 0 and base_price > old_price:
                        increase = tcg_trend_increasing / 100
                        base_price = round(base_price * (1 + increase), 2)
                        price_history.append({
                            'step': 'TCG Trend Increasing',
                            'old_price': old_price,
                            'new_price': base_price,
                            'details': f'Applied {tcg_trend_increasing}% increase'
                        })
                    elif tcg_trend_decreasing > 0 and base_price < old_price:
                        decrease = tcg_trend_decreasing / 100
                        base_price = round(base_price * (1 - decrease), 2)
                        price_history.append({
                            'step': 'TCG Trend Decreasing',
                            'old_price': old_price,
                            'new_price': base_price,
                            'details': f'Applied {tcg_trend_decreasing}% decrease'
                        })
                except (ValueError, TypeError) as e:
                    logger.error(f"Error applying TCG trend adjustments: {str(e)}")
                    # Continue with the base price

            # Apply game rules (min prices are already in local currency)
            old_price = base_price
            try:
                nm_price = self.apply_game_rules(
                    base_price,
                    catalog_item.get('gameName'),
                    catalog_item.get('product_type'),
                    catalog_item.get('rarity')
                )
                if nm_price != old_price:
                    price_history.append({
                        'step': 'Game Rules',
                        'old_price': old_price,
                        'new_price': nm_price,
                        'details': f'Applied game minimum price rules for {catalog_item.get("gameName")}'
                    })
            except Exception as e:
                logger.error(f"Error applying game rules: {str(e)}")
                nm_price = base_price  # Continue with the base price

            condition = sku_info.get('condName', 'Near Mint').lower().strip()
            
            # Get the NM multiplier from custom stepping
            key = f"{catalog_item.get('vendor', '')}_{catalog_item.get('product_type', '')}_{catalog_item.get('expansionName', '')}"
            stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
            
            # If no advanced rules, use custom stepping
            if not stepping_rules:
                stepping_rules = self.settings.get('customStepping')
                if not stepping_rules:
                    stepping_rules = {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50}
                logger.info(f"Using custom stepping rules: {stepping_rules}")
            else:
                logger.info(f"Using advanced stepping rules for {key}: {stepping_rules}")
            
            # Apply condition stepping
            old_price = nm_price
            try:
                price = self.apply_condition_stepping(
                    nm_price,  # Use the min-price-adjusted NM price as base
                    condition,
                    catalog_item.get('vendor', ''),
                    catalog_item.get('product_type', ''),
                    catalog_item.get('expansionName', '')
                )
                if price != old_price:
                    price_history.append({
                        'step': 'Condition Stepping',
                        'old_price': old_price,
                        'new_price': price,
                        'details': f'Applied condition stepping for {condition}'
                    })
            except Exception as e:
                logger.error(f"Error applying condition stepping: {str(e)}")
                price = old_price  # Continue with the previous price

            # Apply minimum price check
            try:
                min_price = float(self.settings.get('minPrice', 0))
                old_price = price
                if price < min_price:
                    price = min_price
                    price_history.append({
                        'step': 'Minimum Price',
                        'old_price': old_price,
                        'new_price': price,
                        'details': f'Applied minimum price of {min_price}'
                    })
            except (ValueError, TypeError) as e:
                logger.error(f"Error applying minimum price: {str(e)}")
                # Continue with the previous price

            # Apply price rounding in local currency
            old_price = price
            try:
                if self.settings.get('price_rounding_enabled', False):
                    price = self.apply_price_rounding(price)
                    if price != old_price:
                        price_history.append({
                            'step': 'Price Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': f'Rounded to thresholds {self.settings.get("price_rounding_thresholds", [])}'
                        })
                else:
                    price = round(price, 2)
                    if price != old_price:
                        price_history.append({
                            'step': 'Standard Rounding',
                            'old_price': old_price,
                            'new_price': price,
                            'details': 'Rounded to 2 decimal places'
                        })
            except Exception as e:
                logger.error(f"Error applying price rounding: {str(e)}")
                price = round(old_price, 2)  # Ensure we have a rounded price
            
            # Apply print type minimum price check
            try:
                printing_type = sku_info.get('printingName')
                game_name = catalog_item.get('gameName')
                
                if printing_type and game_name:
                    old_price = price
                    price, details = self.apply_print_type_minimum(price, printing_type, game_name)
                    
                    if details and price != old_price:
                        price_history.append({
                            'step': 'Print Type Minimum Price',
                            'old_price': old_price,
                            'new_price': price,
                            'details': details
                        })
                        logger.info(f"{details}: ${old_price} -> ${price}")
            except Exception as e:
                logger.error(f"Error applying print type minimum price: {str(e)}")
                # Continue with the previous price
            
            logger.info(f"Final calculated price: {price}")
            return price, False, price_history
            
        except Exception as e:
            logger.error(f"Error calculating final price: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            price_history.append({
                'step': 'Error',
                'old_price': 0,
                'new_price': 0,
                'details': f'Error calculating price: {str(e)}'
            })
            return None, True, price_history
