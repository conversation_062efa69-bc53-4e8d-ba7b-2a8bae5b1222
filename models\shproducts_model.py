from mongoengine import DynamicDocument, StringField, IntField, FloatField, ListField, DictField, BooleanField

class ShProducts(DynamicDocument):
    productId = StringField(required=True)
    username = StringField(required=True)
    title = StringField()
    variants = ListField(DictField())
    vendor = StringField()
    product_type = StringField()
    expansionName = StringField()
    uses_advanced_pricing = BooleanField(default=False)  # Indicates if this product uses advanced pricing rules

    meta = {
        # Remove index definitions from the model to avoid conflicts with the create_shproducts_indexes.py script
        # The indexes are now managed by the create_shproducts_indexes.py script
        'collection': 'shProducts'
    }

    def update_pricing_status(self, user):
        """Update whether this product uses advanced pricing rules"""
        key = f"{self.vendor}_{self.product_type}_{self.expansionName}"
        self.uses_advanced_pricing = key in user.advancedPricingRules
        self.save()
