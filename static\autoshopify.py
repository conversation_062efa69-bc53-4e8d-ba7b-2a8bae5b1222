import threading
import schedule
import time
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import requests
import json
import logging
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s [%(threadName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# MongoDB connection
mongo_uri = '***********************************************************************'
client = MongoClient(mongo_uri)
db = client['test']

user_collection = db['user']
catalog_collection = db['catalog']
tcgplayer_key_collection = db['tcgplayerKey']
exchange_rates_collection = db['exchange_rates']  # Collection containing exchange rates
missing_items_collection = db['missing_items']    # Collection containing missing items

# Fetch TCGplayer API key
tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
if not tcgplayer_key_doc:
    logging.error("TCGplayer API key not found")
    exit(1)

tcgplayer_api_key = tcgplayer_key_doc.get('latestKey')

# Rate Limiter Class
class RateLimiter:
    def __init__(self, max_calls, period):
        self.lock = threading.Lock()
        self.max_calls = max_calls
        self.period = period
        self.allowance = max_calls
        self.last_check = time.perf_counter()

    def acquire(self):
        with self.lock:
            current = time.perf_counter()
            time_passed = current - self.last_check
            self.last_check = current
            self.allowance += time_passed * (self.max_calls / self.period)
            if self.allowance > self.max_calls:
                self.allowance = self.max_calls

            if self.allowance < 1.0:
                sleep_time = (1.0 - self.allowance) * (self.period / self.max_calls)
                time.sleep(sleep_time)
                self.last_check = time.perf_counter()
                self.allowance = self.max_calls - 1.0
            else:
                self.allowance -= 1.0

# Global rate limiters per store
rate_limiters = {}
rate_limiters_lock = threading.Lock()

def get_rate_limiter(store_name):
    with rate_limiters_lock:
        if store_name not in rate_limiters:
            rate_limiters[store_name] = RateLimiter(max_calls=2, period=1)
        return rate_limiters[store_name]

def make_shopify_api_call(shopify_api_url, headers, data, store_name):
    rate_limiter = get_rate_limiter(store_name)
    max_retries = 5
    backoff_factor = 2
    for attempt in range(max_retries):
        try:
            rate_limiter.acquire()
            response = requests.post(shopify_api_url, headers=headers, data=data)
            if response.status_code == 429:
                # Hit the rate limit, wait and retry
                retry_after = int(response.headers.get('Retry-After', 1))
                logging.warning(f"Rate limit exceeded for store '{store_name}'. Retrying after {retry_after} seconds.")
                time.sleep(retry_after)
                continue
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            if attempt < max_retries - 1:
                sleep_time = backoff_factor ** attempt
                logging.warning(f"Request failed: {e}. Retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)
            else:
                logging.error(f"Request failed after {max_retries} attempts: {e}")
                raise

# Fetch exchange rates once to avoid multiple API calls
def get_exchange_rate(target_currency):
    if target_currency == 'USD':
        return 1.0  # No conversion needed

    exchange_rate_doc = exchange_rates_collection.find_one({'currency': target_currency})
    if exchange_rate_doc:
        return exchange_rate_doc.get('rate', 1.0)
    else:
        # If exchange rate not found in the database, fetch from external API
        logging.warning(f"Exchange rate for {target_currency} not found in the database. Fetching from external API.")
        try:
            response = requests.get(f"https://api.exchangerate-api.com/v4/latest/USD")
            response.raise_for_status()
            rates = response.json().get('rates', {})
            rate = rates.get(target_currency, 1.0)
            # Save the rate to the database for future use
            exchange_rates_collection.insert_one({'currency': target_currency, 'rate': rate, 'date': datetime.now(timezone.utc)})
            return rate
        except requests.RequestException as e:
            logging.error(f"Error fetching exchange rate: {str(e)}")
            return 1.0  # Default to 1.0 if there's an error

def process_user_missing_items(username):
    try:
        # Create a new MongoDB client per thread to avoid potential threading issues
        client = MongoClient(mongo_uri)
        db = client['test']
        user_collection = db['user']
        catalog_collection = db['catalog']
        missing_items_collection = db['missing_items']
        exchange_rates_collection = db['exchange_rates']

        # Fetch user profile
        user_profile = user_collection.find_one({'username': username})

        if not user_profile:
            logging.error(f"User profile not found for username {username}")
            return  # Skip to next user

        # Use the user's customStepping and currency settings
        custom_stepping_raw = user_profile.get('customStepping', {})
        custom_stepping = {}
        for key, value in custom_stepping_raw.items():
            custom_stepping[key.lower()] = float(value)

        min_price = user_profile.get('minPrice', 0.2)
        user_currency = user_profile.get('currency', 'USD').upper()
        min_price = float(min_price)

        exchange_rate = get_exchange_rate(user_currency)
        logging.info(f"User '{username}': Exchange rate from USD to {user_currency}: {exchange_rate}")

        # Shopify credentials
        shopify_store_name = user_profile.get('shopifyStoreName')
        shopify_access_token = user_profile.get('shopifyAccessToken')

        if not shopify_store_name or not shopify_access_token:
            logging.error(f"Shopify credentials not found for user '{username}'")
            return  # Skip to next user

        # Fetch missing items for the user
        missing_items = list(missing_items_collection.find({'username': username, 'processed': {'$ne': True}}))

        if not missing_items:
            logging.info(f"No missing items to process for user '{username}'.")
            return

        for item in missing_items:
            # Process each missing item
            process_missing_item(item, user_profile, exchange_rate, tcgplayer_api_key, client)
    except Exception as e:
        logging.error(f"Error processing missing items for user '{username}': {str(e)}")

def process_missing_item(item, user_profile, exchange_rate, tcgplayer_api_key, client):
    try:
        username = item.get('username')
        product_id = item.get('productId')

        logging.info(f"Processing productId {product_id} for user '{username}'")

        min_price = user_profile.get('minPrice', 0.2)
        custom_stepping = user_profile.get('customStepping', {})
        custom_stepping = {k.lower(): float(v) for k, v in custom_stepping.items()}
        user_currency = user_profile.get('currency', 'USD').upper()
        min_price = float(min_price)

        shopify_store_name = user_profile.get('shopifyStoreName')
        shopify_access_token = user_profile.get('shopifyAccessToken')

        # Fetch product from catalog
        catalog_collection = client['test']['catalog']
        product = catalog_collection.find_one({'productId': product_id})
        if not product:
            logging.error(f"Product not found with productId {product_id}")
            return  # Skip to next item

        # Fetch product_id again
        product_id = product.get('productId')

        # Check if product is sealed
        is_sealed = product.get('isSealed', False)

        # Group SKUs by printingName
        sku_groups = {}
        for sku in product.get('skus', []):
            if sku.get('langAbbr', '').upper() != 'EN':
                continue  # Only process English SKUs
            printing_name = sku.get('printingName', '').lower().strip()
            if printing_name not in sku_groups:
                sku_groups[printing_name] = []
            sku_groups[printing_name].append(sku)

        def calculate_prices(product_id, printing_name, custom_stepping, min_price, exchange_rate, tcgplayer_api_key, sku_groups, is_sealed=False):
            headers = {
                'Authorization': f'Bearer {tcgplayer_api_key}',
                'Accept': 'application/json',
            }

            base_price = None

            if is_sealed:
                # For sealed products, use the available SKU's price
                sku_list = sku_groups.get(printing_name, [])
                if not sku_list:
                    return None, f"No SKUs found for printingName '{printing_name}'"

                # Use the first SKU (since there's typically only one for sealed products)
                sku = sku_list[0]
                sku_id = sku.get('skuId')

                # Fetch pricing data for the SKU
                pricing_url = f"https://api.tcgplayer.com/pricing/sku/{sku_id}"
                try:
                    pricing_response = requests.get(pricing_url, headers=headers)
                    pricing_response.raise_for_status()
                    pricing_data = pricing_response.json().get('results', [])
                    if pricing_data:
                        base_price = pricing_data[0].get('lowPrice') or pricing_data[0].get('marketPrice') or 0
                        logging.info(f"Base price for SKU {sku_id} is {base_price}")
                    else:
                        base_price = 0
                except requests.RequestException as e:
                    logging.error(f"Error fetching pricing data for SKU {sku_id}: {str(e)}")
                    return None, f"Error fetching pricing data for SKU {sku_id}"

                if base_price is None or base_price == 0:
                    logging.warning(f"No base price available. Setting base price to minimum price.")
                    base_price = min_price

                # Convert base price to user's currency
                base_price *= exchange_rate

                # Apply NM stepping to the base price
                stepping_value = custom_stepping.get('nm', 100)
                condition_percentage = stepping_value / 100
                condition_price = base_price * condition_percentage
                final_price = round(max(condition_price, min_price), 2)

                final_prices = {
                    'DEFAULT': final_price  # Use 'DEFAULT' since there's only one condition
                }
            else:
                # For non-sealed products, proceed as before
                nm_condition_id = 1  # NM condition ID is 1
                nm_price_entry = None

                # Fetch pricing data for the product
                pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_id}"
                try:
                    pricing_response = requests.get(pricing_url, headers=headers)
                    pricing_response.raise_for_status()
                    pricing_data = pricing_response.json().get('results', [])
                except requests.RequestException as e:
                    logging.error(f"Error fetching pricing data for product {product_id}: {str(e)}")
                    pricing_data = []

                # Find NM price for this printing_name
                for price in pricing_data:
                    condition_id = price.get('conditionId')
                    sub_type_name = price.get('subTypeName', '').lower().strip()
                    if condition_id == nm_condition_id and sub_type_name == printing_name:
                        nm_price_entry = price
                        break

                if nm_price_entry:
                    low_price = nm_price_entry.get('lowPrice', 0) or 0
                    market_price = nm_price_entry.get('marketPrice', 0) or 0
                    base_price = max(low_price, market_price)
                    if base_price == 0:
                        base_price = nm_price_entry.get('midPrice', 0) or 0  # Use midPrice if others are zero
                else:
                    # NM price not found in TCGplayer API, use NM SKU's price
                    nm_sku = next(
                        (sku for sku in sku_groups[printing_name] if sku.get('condAbbr', '').upper().strip() == 'NM'),
                        None
                    )
                    if nm_sku:
                        nm_sku_id = nm_sku.get('skuId')
                        # Fetch pricing data for the NM SKU
                        pricing_url = f"https://api.tcgplayer.com/pricing/sku/{nm_sku_id}"
                        try:
                            pricing_response = requests.get(pricing_url, headers=headers)
                            pricing_response.raise_for_status()
                            pricing_data = pricing_response.json().get('results', [])
                            if pricing_data:
                                base_price = pricing_data[0].get('lowPrice') or pricing_data[0].get('marketPrice') or 0
                                logging.info(f"Base price for NM SKU {nm_sku_id} is {base_price}")
                            else:
                                base_price = 0
                        except requests.RequestException as e:
                            logging.error(f"Error fetching pricing data for NM SKU {nm_sku_id}: {str(e)}")
                            return None, f"Error fetching pricing data for NM SKU {nm_sku_id}"
                    else:
                        return None, f"NM SKU not found for printingName '{printing_name}'"

                if base_price is None or base_price == 0:
                    logging.warning(f"No base price available. Setting base price to minimum price.")
                    base_price = min_price

                # Convert base price to user's currency
                base_price *= exchange_rate

                # Apply custom stepping to calculate prices for all conditions
                final_prices = {}
                for condition in ['nm', 'lp', 'mp', 'hp', 'dm']:
                    stepping_value = custom_stepping.get(condition.lower(), 100)
                    condition_percentage = stepping_value / 100
                    condition_price = base_price * condition_percentage
                    final_price = round(max(condition_price, min_price), 2)
                    final_prices[condition.upper()] = final_price

            return final_prices, None

        # Collect all variants and price summaries
        variants = []
        price_summary = []

        if is_sealed:
            # For sealed products, process differently
            for printing_name, skus in sku_groups.items():
                calculated_prices, error = calculate_prices(
                    product_id,
                    printing_name,
                    custom_stepping,
                    min_price,
                    exchange_rate,
                    tcgplayer_api_key,
                    sku_groups,
                    is_sealed=True
                )
                if error:
                    logging.error(f"Error calculating prices for '{printing_name}': {error}")
                    continue

                # Use the first SKU
                sku = skus[0]
                variant_title = f"{sku.get('printingName', '')} - {sku.get('condName', '')} - {sku.get('langAbbr', '')}"
                calculated_price = calculated_prices.get('DEFAULT')

                if calculated_price is None:
                    logging.error(f"Calculated price is None for variant '{variant_title}'. Skipping variant.")
                    continue  # Skip this variant

                # Collect price summary
                price_summary.append({
                    'printingName': sku.get('printingName', ''),
                    'condition': sku.get('condName', ''),
                    'price': calculated_price,
                    'sku': sku.get('skuId')
                })

                variants.append({
                    "title": variant_title,
                    "price": "{:.2f}".format(calculated_price),
                    "sku": str(sku.get("skuId", "")),
                    "barcode": str(sku.get("skuId", "")),
                    "weight": 0.3,
                    "weight_unit": "g",
                    "option1": variant_title,
                    "requires_shipping": True,
                    "inventory_management": "shopify",
                    "inventory_quantity": 0
                })
        else:
            # For non-sealed products, proceed as before
            conditions_order = ['NM', 'LP', 'MP', 'HP', 'DM']

            for printing_name, skus in sku_groups.items():
                calculated_prices, error = calculate_prices(
                    product_id,
                    printing_name,
                    custom_stepping,
                    min_price,
                    exchange_rate,
                    tcgplayer_api_key,
                    sku_groups,
                    is_sealed=False
                )
                if error:
                    logging.error(f"Error calculating prices for '{printing_name}': {error}")
                    continue

                # Build SKU mapping for this printing_name
                sku_mapping = {}
                for sku in skus:
                    cond_abbr = sku.get('condAbbr', '').upper().strip()
                    if cond_abbr in conditions_order:
                        sku_mapping[cond_abbr] = sku

                for condition in conditions_order:
                    sku = sku_mapping.get(condition)
                    if not sku:
                        continue  # Skip if SKU for the condition is not available

                    variant_title = f"{sku.get('printingName', '')} - {sku.get('condName', '')} - {sku.get('langAbbr', '')}"
                    calculated_price = calculated_prices.get(condition)

                    if calculated_price is None:
                        logging.error(f"Calculated price is None for variant '{variant_title}'. Skipping variant.")
                        continue  # Skip this variant

                    # Collect price summary
                    price_summary.append({
                        'printingName': sku.get('printingName', ''),
                        'condition': sku.get('condName', ''),
                        'price': calculated_price,
                        'sku': sku.get('skuId')
                    })

                    variants.append({
                        "title": variant_title,
                        "price": "{:.2f}".format(calculated_price),
                        "sku": str(sku.get("skuId", "")),
                        "barcode": str(sku.get("skuId", "")),
                        "weight": 0.3,
                        "weight_unit": "g",
                        "option1": variant_title,
                        "requires_shipping": True,
                        "inventory_management": "shopify",
                        "inventory_quantity": 0
                    })

        if not variants:
            logging.error(f"No variants generated for productId {product_id}")
            return  # Skip to next item

        # Display the price summary
        print(f"\nFinal Prices for '{product.get('name')}':")
        for item_price in price_summary:
            print(f" - Printing: {item_price['printingName']}, Condition: {item_price['condition']}, Price: {item_price['price']} {user_currency}, SKU: {item_price['sku']}")

        # Build other product data

        # Build tags
        tags_list = [
            product.get("gameName", ""),
            product.get("expansionName", ""),
            product.get("abbreviation", ""),
            product.get("language", ""),
            product.get("rarity", "")
        ]
        tags = ', '.join(filter(None, tags_list))

        # Build body_html
        extended_data_list = product.get("extendedData", [])
        body_html_parts = []

        if extended_data_list:
            for data in extended_data_list:
                display_name = data.get('displayName', '')
                value = data.get('value', '')
                body_html_parts.append(f"<li><strong>{display_name}:</strong> {value}</li>")
        else:
            # If extendedData is empty, include other product details
            product_details = [
                ("Game", product.get("gameName", "")),
                ("Set", product.get("expansionName", "")),
                ("Rarity", product.get("rarity", "")),
                ("Number", product.get("number", "")),
                ("Language", product.get("language", "")),
            ]
            for label, value in product_details:
                if value:
                    body_html_parts.append(f"<li><strong>{label}:</strong> {value}</li>")

        body_html = "<ul>" + "".join(body_html_parts) + "</ul>"

        if product.get("description"):
            body_html += f"<p>{product['description']}</p>"
        else:
            # Include a default description if none is provided
            body_html += f"<p>{product.get('cleanName', 'No description available.')}</p>"

        # Determine data-cardtype based on gameName
        game_name = product.get("gameName", "")
        game_name_to_cardtype = {
            "Magic: The Gathering": "mtg",
            "Pok�mon": "pokemon",
            "Yu-Gi-Oh!": "yugioh",
            "Akora TCG": "akora",
            "One Piece Card Game": "onepiece",
            # Add other mappings as needed
        }
        data_cardtype = game_name_to_cardtype.get(game_name, "other")  # Use "other" as default

        product_id_str = product.get("productId", "N/A")
        body_html += f'''
        <div class="catalogMetaData" style="visibility: hidden;" data-cardtype="{data_cardtype}" data-cardid="5" data-tcgid="{product_id_str}" data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''

        # Build title
        title = product.get("cleanName") if product.get("cleanName") else "No Title"
        number = product.get("number")
        extra_info = product.get("expansionName") if product.get("gameName") == "Akora TCG" else product.get("abbreviation")

        if number:
            title += f" ({number})"
        if extra_info:
            title += f" [{extra_info}]"

        # Set product status
        product_status = "draft" if any(float(variant["price"]) == 0.00 for variant in variants) else "active"

        # Set product_type and vendor
        if product.get('gameName') == "Magic: The Gathering":
            product_type = "MTG Single" if product.get('isSingle') else "MTG Sealed"
            vendor = "Magic: The Gathering"
        elif product.get('gameName') == "One Piece Card Game":
            product_type = "One Piece Single" if product.get('isSingle') else "One Piece Sealed"
            vendor = "One Piece"
        else:
            product_type = f"{product.get('gameName', 'Unknown')} {'Single' if product.get('isSingle') else 'Sealed'}"
            vendor = product.get("gameName", "Unknown Vendor")

        # Build product data with metafields
        product_data = {
            "product": {
                "title": title,
                "body_html": body_html,
                "vendor": vendor,
                "product_type": product_type,
                "tags": tags,
                "variants": variants,
                "options": [{"name": "Title"}],
                "status": product_status,
                "published_at": datetime.now().isoformat(),
                "images": [{"src": product.get("image", "")}],
                "metafields": [
                    {"namespace": "TCGSync", "key": "TCGPlayer ID", "value": str(product.get("productId", "N/A")), "type": "single_line_text_field"},
                    {"namespace": "TCGSync", "key": "Expansion Name", "value": product.get("expansionName", "N/A"), "type": "single_line_text_field"},
                    {"namespace": "TCGSync", "key": "Product Family", "value": product.get("gameName", "N/A"), "type": "single_line_text_field"},
                    # Add other metafields as needed
                ]
            }
        }

        # Make API request to Shopify to create the product
        shopify_api_url = f"https://{shopify_store_name}.myshopify.com/admin/api/2023-04/products.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_access_token
        }

        try:
            response = make_shopify_api_call(shopify_api_url, headers, json.dumps(product_data), shopify_store_name)
            response.raise_for_status()
            shopify_product = response.json()
            logging.info(f"Product '{title}' created successfully in Shopify store '{shopify_store_name}'")

            # Update the item to mark it as processed and record prices
            missing_items_collection = client['test']['missing_items']
            item_id = item.get('_id')
            if item_id is not None:
                # Ensure item_id is ObjectId
                if isinstance(item_id, dict) and '$oid' in item_id:
                    item_id = ObjectId(item_id['$oid'])
                elif not isinstance(item_id, ObjectId):
                    item_id = ObjectId(str(item_id))

                missing_items_collection.update_one(
                    {'_id': item_id},
                    {'$set': {'processed': True, 'prices_pushed': price_summary}}
                )
                logging.info(f"Marked productId {product_id} as processed in missing_items")
            else:
                logging.error(f"Cannot update item without '_id' for productId {product_id}")
        except requests.RequestException as e:
            logging.error(f"Error creating product in Shopify: {str(e)}")
            # Do not mark as processed; it will be retried in the next run
            return  # Skip to next item
    except Exception as e:
        logging.error(f"Error processing productId {item.get('productId')} for user '{item.get('username')}': {str(e)}")

def main():
    try:
        # Fetch unique usernames who have missing items to process
        usernames = missing_items_collection.distinct('username', {'processed': {'$ne': True}})
        if not usernames:
            logging.info("No users with missing items to process.")
            return

        max_workers = 5  # Adjust based on your system's capacity
        threads = []
        for username in usernames:
            thread = threading.Thread(target=process_user_missing_items, args=(username,))
            thread.start()
            threads.append(thread)

        # Wait for all threads to finish
        for thread in threads:
            thread.join()
    except Exception as e:
        logging.error(f"Error in main processing: {str(e)}")

# Schedule the main function to run every 15 minutes
schedule.every(15).minutes.do(main)

# Run the scheduler
if __name__ == "__main__":
    logging.info("Starting the scheduler to run every 15 minutes.")
    # Run once at startup
    main()
    while True:
        schedule.run_pending()
        time.sleep(1)
