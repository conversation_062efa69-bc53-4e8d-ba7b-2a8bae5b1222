#!/usr/bin/env python3
"""
Server Updater Script

This script connects to a remote server using Paramiko and performs system updates.
It's designed for initial setup of a new Ubuntu server.
"""

import paramiko
import time
import sys
import logging
import os
from datetime import datetime

# Get the script directory for proper log file path
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file_path = os.path.join(script_dir, "server_update.log")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ServerUpdater")

# Server connection details
SERVER_IP = "***************"
USERNAME = "ubuntu"
PASSWORD = "Reggie2805!"
PORT = 22

def execute_command(ssh, command, timeout=60):
    """Execute a command on the remote server and return the output."""
    logger.info(f"Executing: {command}")
    stdin, stdout, stderr = ssh.exec_command(command, timeout=timeout)
    
    # Wait for the command to complete
    exit_status = stdout.channel.recv_exit_status()
    
    # Get the output
    stdout_str = stdout.read().decode('utf-8')
    stderr_str = stderr.read().decode('utf-8')
    
    if exit_status != 0:
        logger.warning(f"Command exited with status {exit_status}")
        logger.warning(f"STDERR: {stderr_str}")
    else:
        logger.info(f"Command completed successfully")
    
    logger.debug(f"STDOUT: {stdout_str}")
    
    return stdout_str, stderr_str, exit_status

def setup_ssl(ssh, domain="enterprise.tcgsync.com"):
    """Set up SSL for the specified domain using Let's Encrypt."""
    logger.info(f"Setting up SSL for {domain}...")
    
    # Install Nginx if not already installed
    logger.info("Installing Nginx...")
    execute_command(ssh, "sudo apt-get install -y nginx", timeout=300)
    
    # Install Certbot and Nginx plugin
    logger.info("Installing Certbot and Nginx plugin...")
    execute_command(ssh, "sudo apt-get install -y certbot python3-certbot-nginx", timeout=300)
    
    # Create website directory
    logger.info("Creating website directory...")
    website_root = f"/var/www/{domain}"
    execute_command(ssh, f"sudo mkdir -p {website_root}", timeout=60)
    
    # Create a sample index.html file
    logger.info("Creating a sample index.html file...")
    sample_html = f"""<!DOCTYPE html>
<html>
<head>
    <title>{domain}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        h1 {{
            color: #333;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to {domain}!</h1>
        <p>This is a placeholder page. Replace this with your actual website content.</p>
        <p>Upload your files to: <code>{website_root}</code></p>
    </div>
</body>
</html>
"""
    execute_command(ssh, f"echo '{sample_html}' | sudo tee {website_root}/index.html", timeout=60)
    
    # Set proper permissions for both www-data and ubuntu user
    logger.info("Setting proper permissions...")
    # Add ubuntu user to www-data group if not already a member
    execute_command(ssh, "sudo usermod -a -G www-data ubuntu", timeout=60)
    
    # Set ownership to www-data:www-data
    execute_command(ssh, f"sudo chown -R www-data:www-data {website_root}", timeout=60)
    
    # Set directory permissions to 775 (rwxrwxr-x) to allow group writing
    execute_command(ssh, f"sudo chmod -R 775 {website_root}", timeout=60)
    
    # Set the setgid bit on directories so new files inherit the group
    execute_command(ssh, f"sudo find {website_root} -type d -exec chmod g+s {{}} \\;", timeout=60)
    
    # Ensure the ubuntu user can access the directory
    logger.info("Verifying permissions...")
    execute_command(ssh, f"ls -la {website_root}", timeout=60)
    
    # Create a basic Nginx configuration for the domain
    logger.info(f"Creating Nginx configuration for {domain}...")
    nginx_config = f"""server {{
    listen 80;
    server_name {domain};
    
    root {website_root};
    index index.html index.htm index.php;
    
    location / {{
        try_files $uri $uri/ =404;
    }}
}}
"""
    
    # Create a temporary file with the Nginx configuration
    execute_command(ssh, f"echo '{nginx_config}' > /tmp/nginx-{domain}.conf")
    
    # Move the configuration file to the Nginx sites-available directory
    execute_command(ssh, f"sudo mv /tmp/nginx-{domain}.conf /etc/nginx/sites-available/{domain}")
    
    # Create a symbolic link to enable the site
    execute_command(ssh, f"sudo ln -sf /etc/nginx/sites-available/{domain} /etc/nginx/sites-enabled/")
    
    # Test Nginx configuration
    logger.info("Testing Nginx configuration...")
    _, stderr, exit_status = execute_command(ssh, "sudo nginx -t")
    
    if exit_status != 0:
        logger.error(f"Nginx configuration test failed: {stderr}")
        return False
    
    # Reload Nginx to apply the configuration
    logger.info("Reloading Nginx...")
    execute_command(ssh, "sudo systemctl reload nginx")
    
    # Obtain SSL certificate using Certbot
    logger.info(f"Obtaining SSL certificate for {domain}...")
    certbot_cmd = f"sudo certbot --nginx -d {domain} --non-interactive --agree-tos --email admin@{domain} --redirect"
    stdout, stderr, exit_status = execute_command(ssh, certbot_cmd, timeout=300)
    
    if exit_status != 0:
        logger.error(f"Failed to obtain SSL certificate: {stderr}")
        return False
    
    # Set up automatic renewal
    logger.info("Setting up automatic certificate renewal...")
    execute_command(ssh, "sudo systemctl enable certbot.timer")
    execute_command(ssh, "sudo systemctl start certbot.timer")
    
    # Verify the timer is active
    execute_command(ssh, "sudo systemctl status certbot.timer")
    
    logger.info(f"SSL setup for {domain} completed successfully!")
    return True

def install_python312(ssh):
    """Install Python 3.12 on the server."""
    logger.info("Installing Python 3.12...")
    
    # Add the deadsnakes PPA
    logger.info("Adding deadsnakes PPA...")
    execute_command(ssh, "sudo apt-get install -y software-properties-common", timeout=300)
    execute_command(ssh, "sudo add-apt-repository -y ppa:deadsnakes/ppa", timeout=300)
    
    # Update package lists
    logger.info("Updating package lists...")
    execute_command(ssh, "sudo apt-get update -y", timeout=300)
    
    # Install Python 3.12 and related packages
    logger.info("Installing Python 3.12 and related packages...")
    execute_command(ssh, "sudo apt-get install -y python3.12 python3.12-venv python3.12-dev", timeout=600)
    
    # Install pip for Python 3.12
    logger.info("Installing pip for Python 3.12...")
    execute_command(ssh, "curl -sS https://bootstrap.pypa.io/get-pip.py | sudo python3.12", timeout=300)
    
    # Verify the installation
    logger.info("Verifying Python 3.12 installation...")
    execute_command(ssh, "python3.12 --version", timeout=60)
    execute_command(ssh, "python3.12 -m pip --version", timeout=60)
    
    logger.info("Python 3.12 installation completed successfully!")

def update_server(ssh):
    """Perform system updates on the server."""
    logger.info("Starting server update process...")
    
    # Update package lists
    logger.info("Updating package lists...")
    execute_command(ssh, "sudo apt-get update -y", timeout=300)
    
    # Upgrade packages
    logger.info("Upgrading packages...")
    execute_command(ssh, "sudo DEBIAN_FRONTEND=noninteractive apt-get upgrade -y", timeout=600)
    
    # Install some common useful packages
    logger.info("Installing common packages...")
    execute_command(ssh, "sudo apt-get install -y htop net-tools curl wget unzip", timeout=300)
    
    # Clean up
    logger.info("Cleaning up...")
    execute_command(ssh, "sudo apt-get autoremove -y", timeout=300)
    execute_command(ssh, "sudo apt-get autoclean -y", timeout=300)
    
    # Check system status
    logger.info("Checking system status...")
    execute_command(ssh, "df -h")
    execute_command(ssh, "free -m")
    execute_command(ssh, "uptime")
    
    logger.info("Server update completed successfully!")

def main():
    """Main function to connect to the server and perform updates."""
    start_time = datetime.now()
    logger.info(f"Script started at {start_time}")
    logger.info(f"Connecting to {SERVER_IP}:{PORT} as {USERNAME}")
    
    # Create SSH client
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        # Connect to the server
        ssh.connect(SERVER_IP, port=PORT, username=USERNAME, password=PASSWORD)
        logger.info("Successfully connected to the server")
        
        # Update the server
        update_server(ssh)
        
        # Install Python 3.12
        install_python312(ssh)
        
        # Note: SSL setup has already been completed for enterprise.tcgsync.com
        # If you need to set up SSL again, uncomment the following line:
        # setup_ssl(ssh, domain="enterprise.tcgsync.com")
        
        # Change ownership of website directory to ubuntu user
        logger.info("Setting website directory permissions for ubuntu user...")
        website_root = "/var/www/enterprise.tcgsync.com"
        execute_command(ssh, f"sudo chown -R ubuntu:ubuntu {website_root}", timeout=60)
        execute_command(ssh, f"sudo chmod -R 755 {website_root}", timeout=60)
        execute_command(ssh, f"ls -la {website_root}", timeout=60)
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        return 1
    finally:
        # Close the connection
        ssh.close()
        logger.info("SSH connection closed")
    
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"Script completed at {end_time}")
    logger.info(f"Total duration: {duration}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
