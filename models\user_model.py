from mongoengine import Document, StringField, ListField, EmailField, BooleanField, DictField, FloatField, EmbeddedDocument, EmbeddedDocumentField, DateTimeField, ValidationError, IntField, ReferenceField, DoesNotExist, ObjectIdField
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime, timedelta
from bson import ObjectId
import logging
from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB Configuration using Config
client = Config.get_mongo_client()
db = client[Config.MONGO_DBNAME]

class LoginHistory(EmbeddedDocument):
    _id = ObjectIdField()
    timestamp = DateTimeField(required=True)
    ip_address = StringField(required=True)
    location = StringField()
    success = BooleanField(required=True)
    error_message = StringField()

class Address(EmbeddedDocument):
    _id = ObjectIdField()
    street_address = StringField()
    city = StringField()
    state = StringField()
    zip_code = StringField()
    country = StringField()

class DiscountOffer(EmbeddedDocument):
    offer_percentage = IntField()
    created_at = DateTimeField()
    expires_at = DateTimeField()
    offer_claimed = BooleanField(default=False)
    offer_viewed = BooleanField(default=False)
    offer_code = StringField()
    discount_type = StringField()
    original_subscription = StringField()

class Subscription(Document):
    name = StringField(required=True, choices=['Free', 'Monthly', 'Annual', 'Lifetime', 'Trial', 'Monthly Basic', 'Annual Basic', 'Buylist Monthly', 'Buylist Annual', 'Suspended', 'Enterprise'])
    start_date = DateTimeField(default=datetime.utcnow)
    end_date = DateTimeField()
    next_invoice_date = DateTimeField()
    trial_expiry_date = DateTimeField()  # New field to track when a trial expires

    def __str__(self):
        return f"Subscription(name={self.name})"

    def is_active(self):
        """Check if the subscription is still active based on end_date"""
        if self.name == 'Lifetime':
            return True
        if not self.end_date:
            return True
        return datetime.utcnow() < self.end_date

    def set_trial_expiry(self):
        """Set the trial expiry date to 7 days from now"""
        if self.name == 'Trial':
            self.trial_expiry_date = datetime.utcnow() + timedelta(days=7)
            # Also set the end_date to match the trial expiry date
            self.end_date = self.trial_expiry_date

    def is_trial_expired(self):
        """Check if the trial has expired"""
        if self.name != 'Trial':
            return False
        if not self.trial_expiry_date:
            return False
        return datetime.utcnow() > self.trial_expiry_date

class Staff(Document, UserMixin):
    # Required fields
    username = StringField(required=True)
    email = EmailField(required=True)
    name = StringField(required=True)

    # PIN-based authentication (4-digit PIN)
    pin = StringField(required=True)

    # Legacy password field (for backward compatibility)
    password = StringField()

    # Staff specific fields
    role = StringField(required=True, default='staff', choices=['staff'])  # Only 'staff' role is available
    status = StringField(default='active', choices=['active', 'inactive'])
    notes = StringField()

    # Staff invitation tracking
    invited_by = StringField(required=True)  # Username of the person who invited this staff member
    created_at = DateTimeField(default=datetime.utcnow)

    # Login history
    recent_logins = ListField(EmbeddedDocumentField(LoginHistory), default=list)
    last_login = DateTimeField()

    # Authentication fields
    reset_token = StringField()
    reset_token_expiration = DateTimeField()
    email_verified = BooleanField(default=True)  # Staff are automatically verified

    # Permissions
    permissions = DictField(default={
        'pos': True,
        'buylist_orders': True,
        'inventory': True,
        'card_scanning': True
    })

    meta = {
        'collection': 'staff',  # Use a separate collection for staff
        'indexes': [
            {'fields': ['email'], 'unique': True}
            # Removed unique constraint on username to allow multiple records with same username
        ]
    }

    def set_pin(self, pin):
        """Set the 4-digit PIN for staff authentication"""
        if not pin or not pin.isdigit() or len(pin) != 4:
            raise ValidationError("PIN must be a 4-digit number")
        self.pin = pin

    def check_pin(self, pin):
        """Check if the provided PIN matches the stored PIN"""
        try:
            if not self.pin:
                logger.error(f"No PIN found for staff {self.username}")
                return False
            return self.pin == pin
        except Exception as e:
            logger.error(f"PIN check failed for staff {self.username}: {str(e)}")
            return False

    # Legacy password methods for backward compatibility
    def set_password(self, password):
        if not password:
            raise ValidationError("Password cannot be empty")
        self.password = generate_password_hash(password)

    def check_password(self, password):
        try:
            if not self.password:
                logger.error(f"No password hash found for staff {self.username}")
                return False
            return check_password_hash(self.password, password)
        except Exception as e:
            logger.error(f"Password check failed for staff {self.username}: {str(e)}")
            return False

    def add_login_record(self, ip_address, location, success=True, error_message=None):
        """Add a new login record and maintain only the last 7 days of history"""
        try:
            # Create new login record
            login_record = LoginHistory(
                timestamp=datetime.utcnow(),
                ip_address=ip_address,
                location=location,
                success=success,
                error_message=error_message
            )

            # Add new record to the beginning of the list
            self.recent_logins.insert(0, login_record)

            # Remove records older than 7 days
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            self.recent_logins = [record for record in self.recent_logins
                                if record.timestamp > seven_days_ago]

            # Update last_login for successful logins
            if success:
                self.last_login = datetime.utcnow()

            self.save()
            logger.info(f"Login record added for staff {self.username}")
        except Exception as e:
            logger.error(f"Error adding login record for staff {self.username}: {str(e)}")

    @property
    def is_active(self):
        return self.status == 'active'

    @property
    def is_authenticated(self):
        try:
            # Check for PIN-based authentication
            return bool(self.username and self.pin)
        except Exception as e:
            logger.error(f"Error checking is_authenticated for staff {getattr(self, 'username', 'unknown')}: {str(e)}")
            return False

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        try:
            return str(self.id) if self.id else None
        except Exception as e:
            logger.error(f"Error getting ID for staff {getattr(self, 'username', 'unknown')}: {str(e)}")
            return None

    def __str__(self):
        return f"Staff(username={self.username}, email={self.email}, role={self.role})"


class User(Document, UserMixin):
    # Required fields
    username = StringField(required=True)
    password = StringField(required=True)
    email = EmailField(required=True)
    use_skuid_pricing = BooleanField(default=False)  # For TCGPlayer SKU ID based pricing

    # Favorites feature
    favorite_quicklinks = ListField(StringField(), default=list)  # Store route names of favorite quicklinks

    # Login history
    recent_logins = ListField(EmbeddedDocumentField(LoginHistory), default=list)

    # Legacy support - keep field but not required
    activated = BooleanField()

    # Staff-specific fields
    pin = StringField()  # 4-digit PIN for staff authentication
    role = StringField(choices=['staff', 'user'])  # Role can be 'staff' or 'user'
    status = StringField(default='active', choices=['active', 'inactive'])
    notes = StringField()
    invited_by = StringField()  # Username of the person who invited this staff member
    permissions = DictField()  # Staff permissions

    # Staff members array - stores staff members associated with this user
    staff_members = ListField(DictField(), default=list)  # Array of staff member objects

    # Discount offers
    discount_offers = ListField(EmbeddedDocumentField(DiscountOffer), default=list)
    has_active_offer = BooleanField(default=False)

    repull = BooleanField(default=False)

    subscription_type = StringField()

    # Optional fields with defaults
    roles = ListField(StringField(), default=list)
    name = StringField()
    business_name = StringField()
    website = StringField()
    address = EmbeddedDocumentField(Address)
    phone = StringField()
    country = StringField()
    shopifyStoreName = StringField()
    shopifyAccessToken = StringField(default='')
    shopifyAccessToken1 = StringField(default='')
    shopifyAccessToken2 = StringField(default='')
    shopifyAccessToken3 = StringField(default='')
    shopifyAccessToken4 = StringField(default='')
    shopifyAccessToken5 = StringField(default='')
    shopifyAccessToken6 = StringField(default='')
    shopifyAccessToken7 = StringField(default='')
    shopifyAccessToken8 = StringField(default='')
    shopifyAccessToken9 = StringField(default='')
    shopifyAccessToken10 = StringField(default='')
    shopifyLocationId = StringField()
    needsOnboarding = BooleanField()
    customStepping = DictField(default={
        "lp": 80,
        "mp": 70,
        "hp": 65,
        "dm": 50,
        "nm": 100
    })
    # Advanced pricing rules structure:
    # {
    #   "vendor_productType_expansionName": {
    #     "nm": 100,
    #     "lp": 80,
    #     "mp": 70,
    #     "hp": 65,
    #     "dm": 50
    #   }
    # }
    advancedPricingRules = DictField(default={})
    minPrice = FloatField(default=0.50)
    game_minimum_prices = DictField(required=False, default={})  # Make field optional and initialize empty
    price_preference_order = ListField(StringField(), default=['lowPrice', 'marketPrice', 'midPrice', 'highPrice'])
    use_highest_price = BooleanField(default=False)
    price_comparison_pairs = ListField(ListField(StringField()), default=[])  # List of pairs like [['lowPrice', 'marketPrice']]
    price_modifiers = DictField(default={})  # Dict of price type to modifier percentage e.g. {'lowPrice': 110} for +10%
    price_rounding_enabled = BooleanField(default=False)  # Enable/disable price rounding
    price_rounding_thresholds = ListField(IntField(), default=[49, 99])  # List of rounding thresholds

    # CardMarket autopricing settings
    use_cardmarket_pricing = BooleanField(default=False)
    cardmarket_price_preference_order = ListField(StringField(), default=['trend', 'low', 'avg', 'avg7', 'avg30'])
    cardmarket_use_highest_price = BooleanField(default=False)
    cardmarket_price_comparison_pairs = ListField(ListField(StringField()), default=[])
    cardmarket_price_modifiers = DictField(default={})
    cardmarket_price_rounding_enabled = BooleanField(default=False)
    cardmarket_price_rounding_thresholds = ListField(IntField(), default=[49, 99])
    cardmarket_game_minimum_prices = DictField(default={})
    cardmarket_custom_stepping = DictField(default={'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50})
    cardmarket_trend_increasing = FloatField(default=0.0)
    cardmarket_trend_decreasing = FloatField(default=0.0)

    logo_url = StringField()
    shopifyOrders = BooleanField()
    shopifyCustomers = BooleanField()
    shopifyProducts = BooleanField()
    invoice_amount = FloatField(default=0.0)

    # Title format settings for Shopify product titles
    title_format_settings = DictField(default={
        "includeName": True,
        "includeNumber": True,
        "includeAbbreviation": True,
        "order": ["name", "number", "abbreviation"]
    })

    # Shopify update catalog settings
    shopify_update_catalog_settings = DictField(default={
        "selected_conditions": ["Near Mint", "Lightly Played", "Moderately Played", "Heavily Played", "Damaged"]
    })

    lastFetchedOrderDate = DateTimeField()
    lastFetchedCustomerDate = DateTimeField()
    lastFetchedProductDate = DateTimeField()
    last_reprice_all = DateTimeField()

    lastOrdersPaginationLink = StringField()
    lastCustomersPaginationLink = StringField()
    lastProductsPaginationLink = StringField()

    fbtusername = StringField()
    royalMailApiKey = StringField()
    cardmarketUsername = StringField()
    cardmarketAppToken = StringField()
    cardmarketAppSecret = StringField()
    cardmarketAccessToken = StringField()
    cardmarketAccessTokenSecret = StringField()
    cardtraderJwtToken = StringField()  # JWT token for CardTrader API
    cardtraderGamePercentages = DictField(default={})  # Game-specific price increase percentages for CardTrader

    ebay_active = BooleanField(default=False)
    ebay_tokens = DictField(default={})  # Stores access_token, refresh_token, expires_at
    ebay_user_id = StringField()
    ebay_last_order_sync = DateTimeField()

    registered_on = DateTimeField(default=datetime.utcnow)
    last_login = DateTimeField()

    sidebar_items = ListField(StringField())
    sidebar_order = ListField(StringField())
    nav_order = ListField(StringField())

    buylist_settings = DictField()

    autopricing_module = BooleanField()
    buylists_module = BooleanField()
    card_scanner_module = BooleanField()
    deal_finder_module = BooleanField()
    downloads_module = BooleanField()
    event_module = BooleanField()
    fbt_module = BooleanField()
    pos_module = BooleanField()
    store_credit_module = BooleanField()
    tcg_catalog_module = BooleanField()
    directory_module = BooleanField()
    employees_module = BooleanField()
    ebay_module = BooleanField()

    nav_permissions = ListField(StringField())
    currency = StringField(default='USD')  # Added default value
    is_eu_region = BooleanField(default=False)  # UK/EU region setting
    customerCount = IntField()
    orderCount = IntField()
    reset_token = StringField()
    reset_token_expiration = DateTimeField()
    subscription = ReferenceField(Subscription)
    free_subscription = DictField()
    is_admin = BooleanField()
    email_verified = BooleanField(default=False)
    email_verification_token = StringField()
    email_verification_sent_at = DateTimeField()

    # Card scanning usage tracking
    daily_scans = IntField(default=0)
    purchased_scans = IntField(default=0)  # Track purchased scan packages
    last_scan_date = DateTimeField()
    cardGraderScans = IntField(default=10)  # Initial free scans for card grader

    # Activation codes
    activation_codes = ListField(StringField(), default=list)  # List of valid activation codes for this user

    # Login verification codes
    verification_code = StringField()  # Current verification code for login
    verification_code_expires_at = DateTimeField()  # Expiration time for verification code

    def set_verification_code(self, code, expires_minutes=10):
        """Set a verification code for login with expiration time"""
        try:
            self.verification_code = code
            self.verification_code_expires_at = datetime.utcnow() + timedelta(minutes=expires_minutes)
            self.save()
            logger.info(f"Verification code set for user {self.username}")
            return True
        except Exception as e:
            logger.error(f"Error setting verification code for user {self.username}: {str(e)}")
            return False

    def verify_code(self, code):
        """Verify the login code and clear it if valid"""
        try:
            # Check if code exists and is not expired
            if not self.verification_code or not self.verification_code_expires_at:
                logger.warning(f"No verification code found for user {self.username}")
                return False

            # Check if code has expired
            if datetime.utcnow() > self.verification_code_expires_at:
                logger.warning(f"Verification code expired for user {self.username}")
                # Clear expired code
                self.verification_code = None
                self.verification_code_expires_at = None
                self.save()
                return False

            # Check if code matches
            if self.verification_code != code:
                logger.warning(f"Invalid verification code for user {self.username}")
                return False

            # Code is valid, clear it to prevent reuse
            self.verification_code = None
            self.verification_code_expires_at = None
            self.save()
            logger.info(f"Verification code validated for user {self.username}")
            return True
        except Exception as e:
            logger.error(f"Error verifying code for user {self.username}: {str(e)}")
            return False

    def add_activation_code(self, code):
        """Add an activation code to the user's list of valid codes"""
        try:
            if code not in self.activation_codes:
                self.activation_codes.append(code)
                self.save()
                logger.info(f"Added activation code for user {self.username}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error adding activation code for user {self.username}: {str(e)}")
            return False

    def validate_activation_code(self, code):
        """Check if the provided code is valid for this user"""
        try:
            # Check if code exists in user's activation codes
            if code in self.activation_codes:
                return True
            return False
        except Exception as e:
            logger.error(f"Error validating activation code for user {self.username}: {str(e)}")
            return False

    def remove_activation_code(self, code):
        """Remove an activation code after it has been used"""
        try:
            if code in self.activation_codes:
                self.activation_codes.remove(code)
                self.save()
                logger.info(f"Removed activation code for user {self.username}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing activation code for user {self.username}: {str(e)}")
            return False

    # Resync confirmation setting
    confirm_resync = BooleanField(default=True)  # Whether to show confirmation popup before resync

    meta = {
        'collection': 'user',  # Explicitly set collection name
        'strict': False,  # Allow fields in DB that aren't defined in model
        'indexes': [
            {'fields': ['email'], 'unique': True}
            # Removed unique constraint on username to allow multiple records with same username
        ]
    }

    def add_login_record(self, ip_address, location, success=True, error_message=None):
        """Add a new login record and maintain only the last 7 days of history"""
        try:
            # Create new login record
            login_record = LoginHistory(
                timestamp=datetime.utcnow(),
                ip_address=ip_address,
                location=location,
                success=success,
                error_message=error_message
            )

            # Add new record to the beginning of the list
            self.recent_logins.insert(0, login_record)

            # Remove records older than 7 days
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            self.recent_logins = [record for record in self.recent_logins
                                if record.timestamp > seven_days_ago]

            # Update last_login for successful logins
            if success:
                self.last_login = datetime.utcnow()

            self.save()
            logger.info(f"Login record added for user {self.username}")
        except Exception as e:
            logger.error(f"Error adding login record for user {self.username}: {str(e)}")

    def get_recent_logins(self, days=7):
        """Get login history for the specified number of days"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            return [record for record in self.recent_logins
                   if record.timestamp > cutoff_date]
        except Exception as e:
            logger.error(f"Error getting recent logins for user {self.username}: {str(e)}")
            return []

    def set_password(self, password):
        if not password:
            raise ValidationError("Password cannot be empty")
        self.password = generate_password_hash(password)

    def check_password(self, password):
        try:
            if not self.password:
                logger.error(f"No password hash found for user {self.username}")
                return False
            return check_password_hash(self.password, password)
        except Exception as e:
            logger.error(f"Password check failed for user {self.username}: {str(e)}")
            return False

    def get_pricing_rules(self, vendor, product_type, expansion_name):
        """Get pricing rules for a specific expansion, falling back to default rules if none exist"""
        key = f"{vendor}_{product_type}_{expansion_name}"
        return self.advancedPricingRules.get(key, self.customStepping)

    def get_title_format(self):
        """Get the user's title format settings, falling back to defaults if none exist"""
        default_format = {
            "includeName": True,
            "includeNumber": True,
            "includeAbbreviation": True,
            "order": ["name", "number", "abbreviation"]
        }
        return self.title_format_settings or default_format

    def set_title_format(self, format_settings):
        """Update the user's title format settings"""
        try:
            self.title_format_settings = format_settings
            self.save()
            logger.info(f"Title format settings updated for user {self.username}")
            return True
        except Exception as e:
            logger.error(f"Error updating title format settings for user {self.username}: {str(e)}")
            return False

    def get_shopify_update_catalog_settings(self):
        """Get the user's Shopify update catalog settings, falling back to defaults if none exist"""
        default_settings = {
            "selected_conditions": ["Near Mint", "Lightly Played", "Moderately Played", "Heavily Played", "Damaged"]
        }
        return self.shopify_update_catalog_settings or default_settings

    def set_shopify_update_catalog_settings(self, settings):
        """Update the user's Shopify update catalog settings"""
        try:
            self.shopify_update_catalog_settings = settings
            self.save()
            logger.info(f"Shopify update catalog settings updated for user {self.username}")
            return True
        except Exception as e:
            logger.error(f"Error updating Shopify update catalog settings for user {self.username}: {str(e)}")
            return False

    @property
    def is_active(self):
        return True  # Always return True to allow login

    @property
    def is_authenticated(self):
        try:
            # Only check essential fields
            return bool(self.username and self.password)
        except Exception as e:
            logger.error(f"Error checking is_authenticated for user {getattr(self, 'username', 'unknown')}: {str(e)}")
            return False

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        try:
            return str(self.id) if self.id else None
        except Exception as e:
            logger.error(f"Error getting ID for user {getattr(self, 'username', 'unknown')}: {str(e)}")
            return None

    def generate_email_verification_token(self):
        """Generate a new email verification token"""
        try:
            import secrets
            self.email_verification_token = secrets.token_urlsafe(32)
            self.email_verification_sent_at = datetime.utcnow()
            self.save()
            return self.email_verification_token
        except Exception as e:
            logger.error(f"Error generating verification token for user {self.username}: {str(e)}")
            return None

    def verify_email(self, token):
        """Verify user's email with the given token"""
        try:
            if not self.email_verification_token or not self.email_verification_sent_at:
                return False

            # Check if token is expired (24 hours)
            if datetime.utcnow() - self.email_verification_sent_at > timedelta(hours=24):
                return False

            if self.email_verification_token == token:
                self.email_verified = True
                self.email_verification_token = None
                self.email_verification_sent_at = None
                self.save()
                return True
            return False
        except Exception as e:
            logger.error(f"Error verifying email for user {self.username}: {str(e)}")
            return False

    def check_admin_status(self):
        try:
            return bool(getattr(self, 'is_admin', False) or 'Admin' in getattr(self, 'roles', []))
        except Exception as e:
            logger.error(f"Error checking admin status: {str(e)}")
            return False

    def update_last_login(self):
        try:
            self.last_login = datetime.utcnow()
            self.save()
            logger.info(f"Updated last login for user {self.username}")
        except Exception as e:
            logger.error(f"Failed to update last login for user {self.username}: {str(e)}")

    def is_currency_set(self):
        return bool(self.currency)

    def clean(self):
        # Ensure currency has a value
        if not self.currency:
            self.currency = 'USD'

        # Initialize game_minimum_prices if it doesn't exist
        if not hasattr(self, 'game_minimum_prices'):
            self.game_minimum_prices = {}

    def delete_user_data(self):
        try:
            user_id = self.id
            username = self.username
            email = self.email
            fbtusername = getattr(self, 'fbtusername', None)

            # Delete user's invoices
            db.invoice.delete_many({"user": user_id})

            if fbtusername:
                # Delete user's transactions
                db.transactions.delete_many({"fbtusername": fbtusername})
                # Delete user's FBT records
                db.fbtSent.delete_many({"Comments": {"$regex": f".*{fbtusername}.*", "$options": "i"}})
                # Delete user's accounting records
                db.accounting.delete_many({"fbtusername": fbtusername})

            # Delete user's subscription
            if hasattr(self, 'subscription') and self.subscription:
                self.subscription.delete()

            # Delete the user document
            self.delete()

            logger.info(f"User {username} (ID: {user_id}) and associated data deleted successfully")
            return True
        except Exception as e:
            logger.error(f"Error deleting user {getattr(self, 'username', 'unknown')}: {str(e)}")
            return False

    def __str__(self):
        return f"User(username={self.username}, email={self.email})"

    def get_subscription_name(self):
        """
        Get the user's subscription name safely, handling any invalid references.
        Returns the subscription name ('Lifetime', 'Monthly', 'Annual', 'Trial'), 'Free', or 'None' based on subscription status.
        """
        try:
            if self.subscription:
                try:
                    subscription = Subscription.objects.get(id=self.subscription.id)
                    return subscription.name
                except DoesNotExist:
                    # If subscription reference is invalid, just clear it without defaulting to free
                    self.subscription = None
                    self.save()
                    return 'None'
            elif self.free_subscription:
                return 'Free'
            else:
                return 'None'
        except Exception as e:
            logger.error(f"Error getting subscription name for user {self.username}: {str(e)}")
            return 'None'  # Default to None on any error to prevent unauthorized access

    def get_trial_expiry_date(self):
        """
        Get the trial expiry date for the user.
        Returns the trial expiry date if the user has a trial subscription, None otherwise.
        """
        try:
            if self.subscription and self.get_subscription_name() == 'Trial':
                try:
                    subscription = Subscription.objects.get(id=self.subscription.id)
                    return subscription.trial_expiry_date
                except DoesNotExist:
                    return None
            return None
        except Exception as e:
            logger.error(f"Error getting trial expiry date for user {self.username}: {str(e)}")
            return None

    def is_trial_expired(self):
        """
        Check if the user's trial subscription has expired.
        Returns True if the trial has expired, False otherwise.
        """
        try:
            if self.subscription and self.get_subscription_name() == 'Trial':
                try:
                    subscription = Subscription.objects.get(id=self.subscription.id)
                    if not subscription.trial_expiry_date:
                        return False
                    return datetime.utcnow() > subscription.trial_expiry_date
                except DoesNotExist:
                    return False
            return False
        except Exception as e:
            logger.error(f"Error checking if trial expired for user {self.username}: {str(e)}")
            return False

    def has_card_scanning_access(self):
        """
        Check if the user has access to card scanning features.
        Users with Lifetime, Monthly, Annual, Monthly Basic, Annual Basic, Enterprise, or Trial subscriptions have access.
        """
        try:
            if self.subscription:
                try:
                    subscription = Subscription.objects.get(id=self.subscription.id)
                    # All subscription types have card scanning access with different limits
                    return subscription.name in ['Lifetime', 'Monthly', 'Annual', 'Trial', 'Monthly Basic', 'Annual Basic', 'Enterprise']
                except DoesNotExist:
                    return False
            return False
        except Exception as e:
            logger.error(f"Error checking card scanning access for user {self.username}: {str(e)}")
            return False

    def get_remaining_scans(self):
        """
        Get the number of remaining scans.
        Lifetime, Monthly, and Annual users have unlimited scans.
        Trial, Monthly Basic, and Annual Basic users have purchased scans + daily allowance.
        """
        try:
            # Check user's subscription type
            subscription_name = self.get_subscription_name()

            # Unlimited scans for Lifetime, Monthly, Annual, and Enterprise subscriptions
            if subscription_name in ['Lifetime', 'Monthly', 'Annual', 'Enterprise']:
                return -1  # Unlimited scans

            # Check if it's a new day
            today = datetime.utcnow().date()
            if not self.last_scan_date or self.last_scan_date.date() < today:
                # Reset daily scans for a new day
                self.daily_scans = 0
                self.save()

            # Trial, Monthly Basic, and Annual Basic users get purchased scans + daily allowance
            if subscription_name in ['Trial', 'Monthly Basic', 'Annual Basic']:
                daily_remaining = max(10 - self.daily_scans, 0)
                return self.purchased_scans + daily_remaining

            # Default for other subscription types
            return 0
        except Exception as e:
            logger.error(f"Error getting remaining scans for user {self.username}: {str(e)}")
            return 0  # Default to 0 on error to prevent unauthorized access

    def increment_scan_count(self, count=1):
        """
        Increment the user's scan count.
        First uses purchased scans, then daily allowance.
        Returns True if successful, False otherwise.
        """
        try:
            # Check user's subscription type
            subscription_name = self.get_subscription_name()

            # Unlimited scans for Lifetime, Monthly, Annual, and Enterprise subscriptions - no need to track
            if subscription_name in ['Lifetime', 'Monthly', 'Annual', 'Enterprise']:
                return True

            # Check if it's a new day
            today = datetime.utcnow().date()
            if not self.last_scan_date or self.last_scan_date.date() < today:
                # Reset daily scans for a new day
                self.daily_scans = 0

            # If user has purchased scans, use those first
            if self.purchased_scans > 0:
                self.purchased_scans -= count
                self.last_scan_date = datetime.utcnow()
                self.save()
                logger.info(f"Used purchased scan for user {self.username}. Remaining: {self.purchased_scans}")
                return True

            # If no purchased scans, use daily allowance
            self.daily_scans += count
            self.last_scan_date = datetime.utcnow()
            self.save()

            logger.info(f"Used daily scan for user {self.username}. Daily scans used: {self.daily_scans}")
            return True
        except Exception as e:
            logger.error(f"Error incrementing scan count for user {self.username}: {str(e)}")
            return False

    # Staff member management methods
    def add_staff_member(self, staff_data):
        """
        Add a new staff member to the staff_members array.

        Args:
            staff_data (dict): Dictionary containing staff member data.
                Required keys: username, email, name, pin
                Optional keys: role, status, notes, permissions

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Ensure required fields are present
            required_fields = ['username', 'email', 'name', 'pin']
            for field in required_fields:
                if field not in staff_data:
                    logger.error(f"Missing required field '{field}' for staff member")
                    return False

            # Validate PIN (must be 4 digits)
            pin = staff_data.get('pin')
            if not pin or not pin.isdigit() or len(pin) != 4:
                logger.error("PIN must be a 4-digit number")
                return False

            # Add created_at timestamp and invited_by field
            staff_data['created_at'] = datetime.utcnow()
            staff_data['invited_by'] = self.username

            # Set default values if not provided
            staff_data.setdefault('role', 'staff')
            staff_data.setdefault('status', 'active')
            staff_data.setdefault('permissions', {
                'pos': True,
                'buylist_orders': True,
                'inventory': True,
                'card_scanning': True
            })

            # Check if staff member with same username already exists
            for staff in self.staff_members:
                if staff.get('username') == staff_data['username']:
                    logger.error(f"Staff member with username '{staff_data['username']}' already exists")
                    return False

            # Add staff member to array
            self.staff_members.append(staff_data)
            self.save()

            logger.info(f"Staff member '{staff_data['username']}' added successfully")
            return True
        except Exception as e:
            logger.error(f"Error adding staff member: {str(e)}")
            return False

    def update_staff_member(self, username, update_data):
        """
        Update an existing staff member in the staff_members array.

        Args:
            username (str): Username of the staff member to update.
            update_data (dict): Dictionary containing fields to update.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Find staff member with matching username
            for i, staff in enumerate(self.staff_members):
                if staff.get('username') == username:
                    # Don't allow updating username or invited_by
                    if 'username' in update_data:
                        del update_data['username']
                    if 'invited_by' in update_data:
                        del update_data['invited_by']

                    # Validate PIN if provided
                    if 'pin' in update_data:
                        pin = update_data.get('pin')
                        if not pin or not pin.isdigit() or len(pin) != 4:
                            logger.error("PIN must be a 4-digit number")
                            return False

                    # Update staff member data
                    self.staff_members[i].update(update_data)
                    self.save()

                    logger.info(f"Staff member '{username}' updated successfully")
                    return True

            logger.error(f"Staff member with username '{username}' not found")
            return False
        except Exception as e:
            logger.error(f"Error updating staff member: {str(e)}")
            return False

    def remove_staff_member(self, username):
        """
        Remove a staff member from the staff_members array.

        Args:
            username (str): Username of the staff member to remove.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Find staff member with matching username
            for i, staff in enumerate(self.staff_members):
                if staff.get('username') == username:
                    # Remove staff member from array
                    self.staff_members.pop(i)
                    self.save()

                    logger.info(f"Staff member '{username}' removed successfully")
                    return True

            logger.error(f"Staff member with username '{username}' not found")
            return False
        except Exception as e:
            logger.error(f"Error removing staff member: {str(e)}")
            return False

    def get_staff_member(self, username):
        """
        Get a staff member from the staff_members array.

        Args:
            username (str): Username of the staff member to get.

        Returns:
            dict: Staff member data if found, None otherwise.
        """
        try:
            # Find staff member with matching username
            for staff in self.staff_members:
                if staff.get('username') == username:
                    return staff

            return None
        except Exception as e:
            logger.error(f"Error getting staff member: {str(e)}")
            return None

    def authenticate_staff_member(self, username, pin):
        """
        Authenticate a staff member using their username and PIN.

        Args:
            username (str): Username of the staff member.
            pin (str): PIN of the staff member.

        Returns:
            dict: Staff member data if authenticated, None otherwise.
        """
        try:
            # Find staff member with matching username
            staff = self.get_staff_member(username)
            if not staff:
                logger.error(f"Staff member with username '{username}' not found")
                return None

            # Check if PIN matches
            if staff.get('pin') != pin:
                logger.error(f"Invalid PIN for staff member '{username}'")
                return None

            # Check if staff member is active
            if staff.get('status') != 'active':
                logger.error(f"Staff member '{username}' is not active")
                return None

            logger.info(f"Staff member '{username}' authenticated successfully")
            return staff
        except Exception as e:
            logger.error(f"Error authenticating staff member: {str(e)}")
            return None

    def add_staff_login_record(self, username, ip_address, location, success=True, error_message=None):
        """
        Add a login record for a staff member.

        Args:
            username (str): Username of the staff member.
            ip_address (str): IP address of the login attempt.
            location (str): Location of the login attempt.
            success (bool): Whether the login attempt was successful.
            error_message (str): Error message if login failed.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Find staff member with matching username
            staff = self.get_staff_member(username)
            if not staff:
                logger.error(f"Staff member with username '{username}' not found")
                return False

            # Create login record
            login_record = {
                'timestamp': datetime.utcnow(),
                'ip_address': ip_address,
                'location': location,
                'success': success,
                'error_message': error_message
            }

            # Initialize recent_logins array if it doesn't exist
            if 'recent_logins' not in staff:
                staff['recent_logins'] = []

            # Add login record to beginning of array
            staff['recent_logins'].insert(0, login_record)

            # Remove records older than 7 days
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            staff['recent_logins'] = [record for record in staff['recent_logins']
                                    if record['timestamp'] > seven_days_ago]

            # Update last_login for successful logins
            if success:
                staff['last_login'] = datetime.utcnow()

            # Save changes
            self.save()

            logger.info(f"Login record added for staff member '{username}'")
            return True
        except Exception as e:
            logger.error(f"Error adding login record for staff member: {str(e)}")
            return False
