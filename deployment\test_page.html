<!DOCTYPE html>
<html>
<head>
    <title>Test Page for enterprise.tcgsync.com</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
            text-align: center;
            font-size: 1.2em;
            margin: 20px 0;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 4px;
        }
        .info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .timestamp {
            text-align: right;
            font-size: 0.8em;
            color: #7f8c8d;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Page for enterprise.tcgsync.com</h1>
        
        <div class="success">
            File upload successful!
        </div>
        
        <p>This test page confirms that:</p>
        <ul>
            <li>The file upload script is working correctly</li>
            <li>The web server is properly configured</li>
            <li>The SSL certificate is installed and working</li>
        </ul>
        
        <div class="info">
            <p><strong>Server Information:</strong></p>
            <p>Your website files should be uploaded to: <code>/var/www/enterprise.tcgsync.com</code></p>
            <p>The domain is configured with HTTPS and will automatically redirect HTTP traffic to HTTPS.</p>
        </div>
        
        <div class="timestamp">
            Generated on: April 13, 2025
        </div>
    </div>
</body>
</html>
