from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON>tField, <PERSON>loat<PERSON>ield, List<PERSON>ield, BooleanField, DictField

class WarhammerProduct(Document):
    id = StringField()
    name = StringField(required=True)
    description = StringField()
    price = FloatField()
    ctPrice = DictField()
    images = ListField(StringField())
    game_system = StringField()
    game_alliance = StringField()
    game_faction = StringField()
    game_subcategories = ListField(StringField())
    game_tags = StringField()
    material = StringField()
    productType = StringField()
    isAvailable = BooleanField(default=False)
    isInStock = BooleanField(default=False)
    isPreOrder = BooleanField(default=False)
    sku = StringField()
    slug = StringField()
    version = IntField(default=1)

    meta = {
        'db_alias': 'test',
        'collection': 'warhammer',
        'indexes': [
            'name',
            'game_system',
            'game_alliance',
            'game_faction',
            'game_tags',
            'sku'
        ]
    }

    @classmethod
    def get_distinct_values(cls, field, match_conditions=None):
        try:
            if match_conditions is None:
                match_conditions = {}
            values = cls.objects(**match_conditions).distinct(field)
            return sorted(list(filter(None, values))) if values else []
        except Exception:
            return []

    @classmethod
    def search(cls, query=None, page=1, per_page=25):
        try:
            # Start with base query
            base_query = cls.objects

            # Add search conditions if query provided
            if query:
                base_query = base_query.filter(__raw__={
                    '$or': [
                        {'name': {'$regex': query, '$options': 'i'}},
                        {'game_system': {'$regex': query, '$options': 'i'}},
                        {'game_alliance': {'$regex': query, '$options': 'i'}},
                        {'game_faction': {'$regex': query, '$options': 'i'}},
                        {'game_tags': {'$regex': query, '$options': 'i'}}
                    ]
                })

            # Get total count
            total_count = base_query.count()

            # Get paginated results
            products = base_query.order_by('name').skip((page - 1) * per_page).limit(per_page)

            # Convert to dict for JSON serialization
            products_list = []
            for product in products:
                product_dict = product.to_mongo().to_dict()
                product_dict['_id'] = str(product_dict['_id'])
                products_list.append(product_dict)

            return products_list, total_count
        except Exception:
            return [], 0
