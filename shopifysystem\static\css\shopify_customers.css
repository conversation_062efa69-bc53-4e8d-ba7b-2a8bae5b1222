.avatar-circle {
    width: 50px;
    height: 50px;
    background-color: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: bold;
}

.customer-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.stat-item label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

/* Make all text white for better readability on colored backgrounds */
.bg-primary .card-title,
.bg-success .card-title,
.bg-info .card-title,
.bg-warning .card-title,
.bg-primary h6,
.bg-success h6,
.bg-info h6,
.bg-warning h6,
.bg-primary .card-body,
.bg-success .card-body,
.bg-info .card-body,
.bg-warning .card-body {
    color: #ffffff !important;
}

/* Lighter text for small elements */
.bg-primary small,
.bg-success small,
.bg-info small,
.bg-warning small {
    color: rgba(255, 255, 255, 0.8) !important;
}

.stat-item h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #212529;
}

.activity-timeline {
    position: relative;
    padding: 1rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.activity-item {
    position: relative;
    padding-left: 2.5rem;
    padding-bottom: 1.5rem;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 1.25rem;
    top: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: #007bff;
    transform: translateX(-50%);
}

.insight-item {
    margin-bottom: 1.5rem;
}

.insight-item label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.insight-item h4 {
    margin: 0;
    font-size: 1.2rem;
    color: #212529;
}

.nav-tabs .nav-link {
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #007bff;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-weight: 500;
}

.sortable {
    cursor: pointer;
}

.sorted-asc::after {
    content: ' ▲';
}

.sorted-desc::after {
    content: ' ▼';
}
