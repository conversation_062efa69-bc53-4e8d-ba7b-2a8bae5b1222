from flask import request, jsonify, redirect, url_for
from flask_login import current_user
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def subscription_required(f):
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        # Skip subscription check for activation-related routes
        if request.blueprint == 'activation':
            return f(*args, **kwargs)

        # Get subscription name
        subscription_name = current_user.get_subscription_name()

        # Allow access for Lifetime, Monthly, Annual, Monthly Basic, Annual Basic, Enterprise, and Trial subscriptions
        if subscription_name in ['Lifetime', 'Monthly', 'Annual', 'Trial', 'Monthly Basic', 'Annual Basic', 'Enterprise']:
            return f(*args, **kwargs)

        # Redirect to activation only for Free users
        logger.warning(f"Free user access attempt by user: {current_user.username}")
        if request.is_json:
            return jsonify({"message": "Subscription required"}), 403
        return redirect(url_for('activation.activating'))

    return decorated_function

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            logger.warning("Unauthenticated access attempt - redirecting to login")
            if request.is_json:
                return jsonify({"message": "Authentication required"}), 401

            # Check if we've already flashed a login message in this session
            from flask import session, flash
            if 'login_message_flashed' not in session:
                flash('Please log in to access this page.', 'info')
                session['login_message_flashed'] = True

            # Always redirect to login without next parameter to prevent loops
            return redirect('/auth/login')

        # Clear the login message flag when user is authenticated
        from flask import session
        if 'login_message_flashed' in session:
            session.pop('login_message_flashed')

        logger.info(f"Authenticated access by user: {current_user.username}")
        return f(*args, **kwargs)
    return decorated_function

def roles_required(*roles):
    def wrapper(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if not hasattr(current_user, 'roles'):
                logger.warning(f"User {current_user.username} has no roles defined")
                return jsonify({"message": "Access denied!"}), 403

            user_roles = current_user.roles
            if not any(role in user_roles for role in roles):
                logger.warning(f"Access denied - User {current_user.username} roles: {user_roles}, Required roles: {roles}")
                return jsonify({"message": "Access denied!"}), 403

            logger.info(f"Role check passed for user {current_user.username}")
            return f(*args, **kwargs)
        return decorated_function
    return wrapper
