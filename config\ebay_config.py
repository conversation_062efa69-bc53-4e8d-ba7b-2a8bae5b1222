import os
from typing import List

# eBay API Credentials
CLIENT_ID = os.getenv('EBAY_CLIENT_ID')
CLIENT_SECRET = os.getenv('EBAY_CLIENT_SECRET')
RUNAME = os.getenv('EBAY_RUNAME')

# eBay API URLs
AUTH_URL = os.getenv('EBAY_AUTH_URL', 'https://auth.ebay.com/oauth2/authorize')
TOKEN_URL = os.getenv('EBAY_TOKEN_URL', 'https://api.ebay.com/identity/v1/oauth2/token')
CALLBACK_URL = os.getenv('EBAY_CALLBACK_URL', 'https://login.tcgsync.com/api/ebay/auth/callback')

# eBay API Scopes
SCOPES: List[str] = [
    'https://api.ebay.com/oauth/api_scope',
    'https://api.ebay.com/oauth/api_scope/sell.marketing.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.marketing',
    'https://api.ebay.com/oauth/api_scope/sell.inventory.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.inventory',
    'https://api.ebay.com/oauth/api_scope/sell.account.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.account',
    'https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.fulfillment',
    'https://api.ebay.com/oauth/api_scope/sell.analytics.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.finances',
    'https://api.ebay.com/oauth/api_scope/sell.payment.dispute',
    'https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.reputation',
    'https://api.ebay.com/oauth/api_scope/sell.reputation.readonly',
    'https://api.ebay.com/oauth/api_scope/commerce.notification.subscription',
    'https://api.ebay.com/oauth/api_scope/commerce.notification.subscription.readonly',
    'https://api.ebay.com/oauth/api_scope/sell.stores',
    'https://api.ebay.com/oauth/api_scope/sell.stores.readonly',
    'https://api.ebay.com/oauth/scope/sell.edelivery'
]

def get_auth_url() -> str:
    """Generate the eBay OAuth authorization URL."""
    scope_string = ' '.join(SCOPES)
    return f"{AUTH_URL}?client_id={CLIENT_ID}&response_type=code&redirect_uri={CALLBACK_URL}&scope={scope_string}"
