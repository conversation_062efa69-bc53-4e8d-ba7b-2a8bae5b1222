from mongoengine import Document, StringField, ListField, DictField, IntField, FloatField, BooleanField, EmbeddedDocument, EmbeddedDocumentField, DateTimeField

class ShopifyVariant(EmbeddedDocument):
    id = StringField()
    product_id = StringField()
    title = StringField()
    price = StringField()
    sku = StringField()
    position = IntField()
    inventory_policy = StringField()
    compare_at_price = StringField()
    fulfillment_service = StringField()
    inventory_management = StringField()
    option1 = StringField()
    option2 = StringField()
    option3 = StringField()
    created_at = DateTimeField()
    updated_at = DateTimeField()
    taxable = BooleanField()
    barcode = StringField()
    grams = IntField()
    image_id = StringField()
    weight = FloatField()
    weight_unit = StringField()
    inventory_item_id = StringField()
    inventory_quantity = IntField()
    old_inventory_quantity = IntField()
    requires_shipping = BooleanField()
    admin_graphql_api_id = StringField()
    
    # New fields
    language = StringField()
    langAbbr = StringField()
    condition = StringField()
    pricingInfo = DictField()
    printingName = StringField()
    conditionId = StringField()
    skuId = StringField()
    languageId = StringField()
    pricePointUpdated = DateTimeField()
    printingId = StringField()
    condAbbr = StringField()
    rarity = StringField()

class ShopifyProduct(Document):
    username = StringField(required=True)
    product_id = StringField(unique=True, sparse=True)
    title = StringField()
    body_html = StringField()
    vendor = StringField()
    product_type = StringField()
    created_at = DateTimeField()
    handle = StringField()
    updated_at = DateTimeField()
    published_at = DateTimeField()
    template_suffix = StringField()
    status = StringField()
    published_scope = StringField()
    tags = StringField()
    admin_graphql_api_id = StringField()
    variants = ListField(EmbeddedDocumentField(ShopifyVariant))
    options = ListField(DictField())
    images = ListField(DictField())
    image = DictField()

    # Additional fields
    expansionName = StringField()
    expansionCode = StringField()
    abbreviation = StringField()
    rarity = StringField()
    tcgItem = StringField()
    needsMatching = BooleanField()
    productId = StringField()
    productName = StringField()
    last_repriced = DateTimeField()
    number = StringField()
    groupId = StringField()

    # Dynamic field to store any additional fields
    additional_fields = DictField()

    meta = {
        'collection': 'shProducts',
        'indexes': [
            {'fields': ['product_id'], 'unique': True, 'sparse': True}
        ]
    }

    def __setattr__(self, name, value):
        if name in self._fields or name in ['_changed_fields', '_initialised', 'id']:
            super().__setattr__(name, value)
        else:
            if 'additional_fields' not in self.__dict__:
                self.__dict__['additional_fields'] = {}
            self.__dict__['additional_fields'][name] = value

    def __getattr__(self, name):
        if name in self.__dict__:
            return self.__dict__[name]
        additional_fields = self.__dict__.get('additional_fields', {})
        if name in additional_fields:
            return additional_fields[name]
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
