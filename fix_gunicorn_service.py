#!/usr/bin/env python3
"""
Quick Fix for Gunicorn Service Setup
Fixes permissions and creates a working systemd service
"""

import paramiko
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def fix_gunicorn_service():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to fix gunicorn service...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n🚀 FIXING GUNICORN SERVICE SETUP")
        print("="*50)
        
        # 1. Stop any existing gunicorn processes
        print("1. 🛑 Stopping existing processes...")
        execute_command(ssh_client, "systemctl stop gunicorn")
        execute_command(ssh_client, "pkill -f gunicorn")
        print("✅ Stopped existing processes")
        
        # 2. Fix directory and file permissions
        print("2. 🔧 Fixing permissions...")
        commands = [
            "mkdir -p /var/www/html/logs/app",
            "chown -R www-data:www-data /var/www/html/logs",
            "chmod -R 755 /var/www/html/logs",
            "chown www-data:www-data /var/www/html/emergency.conf.py",
            "chmod 644 /var/www/html/emergency.conf.py",
            "chown -R www-data:www-data /var/www/html/venv",
            "chmod +x /var/www/html/venv/bin/gunicorn"
        ]
        
        for cmd in commands:
            execute_command(ssh_client, cmd)
        print("✅ Fixed permissions")
        
        # 3. Create a working systemd service file
        print("3. 📝 Creating systemd service file...")
        
        service_content = '''[Unit]
Description=Gunicorn instance to serve TCG Sync Application
After=network.target
Wants=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
ExecStart=/var/www/html/venv/bin/gunicorn --config emergency.conf.py wsgi:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
'''
        
        # Write the service file
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        execute_command(ssh_client, service_command)
        print("✅ Service file created")
        
        # 4. Reload systemd and enable service
        print("4. 🔄 Reloading systemd...")
        execute_command(ssh_client, "systemctl daemon-reload")
        execute_command(ssh_client, "systemctl enable gunicorn")
        print("✅ Service enabled for auto-start")
        
        # 5. Start the service
        print("5. 🚀 Starting gunicorn service...")
        output, error, code = execute_command(ssh_client, "systemctl start gunicorn")
        
        import time
        time.sleep(3)
        
        # 6. Check service status
        print("6. ✅ Checking service status...")
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ SUCCESS! Gunicorn service is running")
            
            # Test application
            output, error, code = execute_command(ssh_client, "curl -s -w 'Status: %{http_code} Time: %{time_total}s\\n' -o /dev/null http://localhost:8000/")
            if code == 0:
                print(f"🚀 Application test: {output}")
            
            # Verify auto-start
            output, error, code = execute_command(ssh_client, "systemctl is-enabled gunicorn")
            if "enabled" in output:
                print("✅ Auto-start on boot is ENABLED")
            
            # Show worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"📊 Running with {worker_count} workers")
            
        else:
            print("❌ Service failed to start")
            print("Status output:")
            print(output[:500])
            
            # Check logs for errors
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -10")
            print("\nRecent logs:")
            print(output)
        
        print("\n" + "="*50)
        print("📋 SERVICE MANAGEMENT COMMANDS:")
        print("="*50)
        print("# Check status:")
        print("systemctl status gunicorn")
        print("\n# Restart service:")
        print("systemctl restart gunicorn")
        print("\n# View logs:")
        print("journalctl -u gunicorn -f")
        print("\n# Test application:")
        print("curl http://localhost:8000/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    fix_gunicorn_service()
