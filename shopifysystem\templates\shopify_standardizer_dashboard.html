{% extends "base.html" %}

{% block title %}Shopify Product Standardizer{% endblock %}


{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Shopify Product Standardizer</h1>
    
    <!-- Direct link to API endpoint -->
    <p>
        <a href="/shopify/standardizer/api/games" target="_blank">Test API Endpoint Directly</a>
        <button onclick="manualFetch()" class="btn btn-sm btn-info ml-2">Manual Fetch</button>
    </p>
    
    <script>
        function manualFetch() {
            alert('Manual fetch button clicked');
            fetch('/shopify/standardizer/api/games')
                .then(response => {
                    alert('Manual fetch - API response status: ' + response.status);
                    return response.json();
                })
                .then(data => {
                    alert('Manual fetch - Games data: ' + JSON.stringify(data));
                    
                    // Try to update the dropdown directly
                    const gameSelect = document.getElementById('game');
                    if (gameSelect) {
                        gameSelect.innerHTML = '<option value="">Games loaded manually!</option>';
                        
                        if (data.games && data.games.length > 0) {
                            data.games.forEach(game => {
                                const option = document.createElement('option');
                                option.value = game;
                                option.textContent = game;
                                gameSelect.appendChild(option);
                            });
                        }
                    } else {
                        alert('Game select element not found!');
                    }
                })
                .catch(error => {
                    alert('Manual fetch - Error: ' + error.message);
                });
        }
    </script>
    
    <!-- Test script inline in the content block -->
    <script>
        alert('Inline script in content block');
        
        // Try to fetch games directly
        fetch('/shopify/standardizer/api/games')
            .then(response => {
                alert('API response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                alert('Games data: ' + JSON.stringify(data));
                
                // Try to update the dropdown directly
                const gameSelect = document.getElementById('game');
                if (gameSelect) {
                    gameSelect.innerHTML = '<option value="">Games loaded!</option>';
                    
                    if (data.games && data.games.length > 0) {
                        data.games.forEach(game => {
                            const option = document.createElement('option');
                            option.value = game;
                            option.textContent = game;
                            gameSelect.appendChild(option);
                        });
                    }
                } else {
                    alert('Game select element not found!');
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
    </script>
    
    <!-- Simple UI with just game and expansion dropdowns -->
    <div class="row">
        <div class="col-lg-6 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Select Products to Standardize</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('standardizer.run_analysis') }}" method="post" id="analysisForm">
                        <div class="form-group mb-3">
                            <label for="game">Game</label>
                            <select class="form-control" id="game" name="game">
                                <option value="">Select a Game</option>
                                <!-- Will be populated from user's shProducts collection -->
                            </select>
                            <small class="form-text text-muted">Select a specific game to analyze</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="expansion">Expansion</label>
                            <select class="form-control" id="expansion" name="expansion" disabled>
                                <option value="">Select Game First</option>
                                <!-- Will be populated based on selected game -->
                            </select>
                            <small class="form-text text-muted">Select a specific expansion to analyze</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">Run Standardizer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-container {
    width: 80%;
    max-width: 400px;
    margin-top: 20px;
}

.progress-bar {
    height: 10px;
    background-color: #3498db;
    width: 0%;
    border-radius: 5px;
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fetch games from the user's shProducts collection
    fetchGames();
    
    // Add event listener for game selection change
    document.getElementById('game').addEventListener('change', function() {
        const selectedGame = this.value;
        const expansionSelect = document.getElementById('expansion');
        
        if (selectedGame) {
            // Enable expansion dropdown and fetch expansions for the selected game
            expansionSelect.disabled = false;
            fetchExpansions(selectedGame);
        } else {
            // Disable expansion dropdown if no game is selected
            expansionSelect.disabled = true;
            expansionSelect.innerHTML = '<option value="">Select Game First</option>';
        }
    });
    
    // Function to fetch games from the user's shProducts collection
    function fetchGames() {
        alert('Attempting to fetch games...');
        console.log('Fetching games from API...');
        fetch('/shopify/standardizer/api/games')
            .then(response => {
                console.log('API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Games data received:', data);
                const gameSelect = document.getElementById('game');
                
                // Keep the first option (Select a Game)
                const firstOption = gameSelect.options[0];
                gameSelect.innerHTML = '';
                gameSelect.appendChild(firstOption);
                
                // Add games from the user's shProducts collection
                if (data.games && data.games.length > 0) {
                    console.log(`Found ${data.games.length} games`);
                    data.games.forEach(game => {
                        console.log(`Adding game: ${game}`);
                        const option = document.createElement('option');
                        option.value = game;
                        option.textContent = game;
                        gameSelect.appendChild(option);
                    });
                } else {
                    console.log('No games found in data');
                    // If no games found, add a message
                    const option = document.createElement('option');
                    option.value = "";
                    option.textContent = "No games found in your products";
                    option.disabled = true;
                    gameSelect.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error fetching games:', error);
                // Add error message to dropdown
                const gameSelect = document.getElementById('game');
                gameSelect.innerHTML = '<option value="">Error loading games</option>';
            });
    }
    
    // Function to fetch expansions for a selected game
    function fetchExpansions(game) {
        console.log(`Fetching expansions for game: ${game}`);
        fetch(`/shopify/standardizer/api/expansions?game=${encodeURIComponent(game)}`)
            .then(response => {
                console.log('Expansions API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Expansions data received:', data);
                const expansionSelect = document.getElementById('expansion');
                
                // Reset expansion dropdown
                expansionSelect.innerHTML = '<option value="">All Expansions</option>';
                
                // Add expansions for the selected game
                if (data.expansions && data.expansions.length > 0) {
                    console.log(`Found ${data.expansions.length} expansions`);
                    data.expansions.forEach(expansion => {
                        console.log(`Adding expansion: ${expansion}`);
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                } else {
                    console.log('No expansions found for this game');
                    // If no expansions found, add a message
                    const option = document.createElement('option');
                    option.value = "";
                    option.textContent = "No expansions found for this game";
                    option.disabled = true;
                    expansionSelect.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error fetching expansions:', error);
                // Add error message to dropdown
                const expansionSelect = document.getElementById('expansion');
                expansionSelect.innerHTML = '<option value="">Error loading expansions</option>';
            });
    }
    
    // Add loading overlay to the page with inline styles for maximum compatibility
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.style.display = 'none';
    loadingOverlay.style.position = 'fixed';
    loadingOverlay.style.top = '0';
    loadingOverlay.style.left = '0';
    loadingOverlay.style.width = '100%';
    loadingOverlay.style.height = '100%';
    loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    loadingOverlay.style.zIndex = '99999';
    loadingOverlay.style.justifyContent = 'center';
    loadingOverlay.style.alignItems = 'center';
    loadingOverlay.style.flexDirection = 'column';
    
    // Create spinner
    const spinner = document.createElement('div');
    spinner.style.border = '5px solid #f3f3f3';
    spinner.style.borderTop = '5px solid #3498db';
    spinner.style.borderRadius = '50%';
    spinner.style.width = '50px';
    spinner.style.height = '50px';
    spinner.style.animation = 'spin 2s linear infinite';
    spinner.style.marginBottom = '20px';
    
    // Create loading text
    const loadingText = document.createElement('div');
    loadingText.id = 'loadingText';
    loadingText.style.color = 'white';
    loadingText.style.fontSize = '18px';
    loadingText.style.fontWeight = 'bold';
    loadingText.textContent = 'Processing...';
    
    // Create progress container
    const progressContainer = document.createElement('div');
    progressContainer.className = 'progress-container';
    
    // Create progress bar
    const progressBar = document.createElement('div');
    progressBar.id = 'progressBar';
    progressBar.className = 'progress-bar';
    
    // Assemble the overlay
    progressContainer.appendChild(progressBar);
    loadingOverlay.appendChild(spinner);
    loadingOverlay.appendChild(loadingText);
    loadingOverlay.appendChild(progressContainer);
    document.body.appendChild(loadingOverlay);
    
    // Add event listener to the Run Analysis form
    const analysisForm = document.getElementById('analysisForm');
    if (analysisForm) {
        analysisForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading overlay with progress bar
            document.getElementById('loadingText').textContent = 'Starting analysis...';
            document.getElementById('progressBar').style.width = '0%';
            loadingOverlay.style.display = 'flex';
            
            // Simulate progress updates
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 6;
                if (progress > 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                }
                document.getElementById('progressBar').style.width = progress + '%';
                
                // Update text based on progress
                if (progress < 30) {
                    document.getElementById('loadingText').textContent = 'Preparing analysis...';
                } else if (progress < 60) {
                    document.getElementById('loadingText').textContent = 'Scanning product catalog...';
                } else if (progress < 90) {
                    document.getElementById('loadingText').textContent = 'Setting up background task...';
                } else {
                    document.getElementById('loadingText').textContent = 'Almost ready...';
                }
            }, 250);
            
            // Submit the form after a short delay to show the progress
            setTimeout(() => {
                clearInterval(progressInterval);
                this.submit();
            }, 2500);
        });
    }
});

// Direct call to fetchGames outside of DOMContentLoaded
console.log('Direct call to fetchGames');
fetchGames();

function fetchGames() {
    alert('Direct fetchGames call');
    console.log('Direct fetchGames call - Fetching games from API...');
    fetch('/shopify/standardizer/api/games')
        .then(response => {
            console.log('Direct call - API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Direct call - Games data received:', data);
            alert('Games data received: ' + JSON.stringify(data));
        })
        .catch(error => {
            console.error('Direct call - Error fetching games:', error);
            alert('Error fetching games: ' + error.message);
        });
}
</script>
{% endblock %}
