#!/usr/bin/env python3
"""
Optimize Gun<PERSON> with Unix Socket
This script configures gun<PERSON> to use a Unix socket instead of TCP port
for better performance, and updates nginx to use this socket.
"""

import paramiko
import time
import sys

def execute_command(ssh_client, command, timeout=30):
    """Execute a command and return output, error, and exit code"""
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()
        exit_code = stdout.channel.recv_exit_status()
        return output, error, exit_code
    except Exception as e:
        return "", str(e), 1

def optimize_with_unix_socket():
    hostname = "**************"
    username = "root"
    password = "ReggieReggie2805?"
    port = 22
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print(f"🔧 Connecting to {hostname} to optimize with Unix socket...")
        ssh_client.connect(hostname=hostname, port=port, username=username, password=password, timeout=10)
        print("✅ Connected!")
        
        print("\n" + "="*70)
        print("⚡ OPTIMIZING GUNICORN WITH UNIX SOCKET")
        print("="*70)
        
        # 1. Stop any running gunicorn processes
        print("1. 🛑 Stopping gunicorn service...")
        execute_command(ssh_client, "systemctl stop gunicorn")
        execute_command(ssh_client, "pkill -9 -f gunicorn")
        time.sleep(2)
        
        # 2. Create socket directory with proper permissions
        print("\n2. 📁 Creating socket directory with proper permissions...")
        execute_command(ssh_client, "mkdir -p /var/www/html/run")
        execute_command(ssh_client, "chown www-data:www-data /var/www/html/run")
        execute_command(ssh_client, "chmod 755 /var/www/html/run")
        
        # 3. Create optimized gunicorn configuration with Unix socket
        print("\n3. ⚡ Creating optimized gunicorn configuration with Unix socket...")
        
        # Get CPU count
        output, error, code = execute_command(ssh_client, "nproc")
        cpu_count = int(output.strip()) if output.strip().isdigit() else 4
        
        # Calculate optimal workers
        optimal_workers = cpu_count * 2
        optimal_workers = max(optimal_workers, 4)  # At least 4 workers
        
        socket_path = "/var/www/html/run/gunicorn.sock"
        
        socket_config = f'''
# Ultra-Optimized Gunicorn Configuration with Unix Socket
workers = {optimal_workers}
worker_class = "gevent"
worker_connections = 1000
threads = 2
timeout = 120
keepalive = 5

# Unix socket for better performance
bind = "unix:{socket_path}"

# Request settings
max_requests = 1000
max_requests_jitter = 200
graceful_timeout = 30

# Logging
accesslog = "/var/www/html/logs/app/gunicorn-access.log"
errorlog = "/var/www/html/logs/app/gunicorn-error.log"
loglevel = "warning"

# Performance optimizations
preload_app = True
worker_tmp_dir = "/dev/shm"

# Prevent thundering herd
forwarded_allow_ips = "*"
secure_scheme_headers = {{"X-Forwarded-Proto": "https"}}

def post_fork(server, worker):
    # Optimize worker settings after fork
    import gevent
    from gevent import monkey
    monkey.patch_all()
    
    # Set worker priority
    import os
    os.nice(10)
'''
        
        config_command = f'cat > /var/www/html/gunicorn.conf.py << \'EOF\'\n{socket_config}\nEOF'
        output, error, code = execute_command(ssh_client, config_command)
        
        if code == 0:
            print("✅ Created optimized gunicorn configuration with Unix socket")
            
            # Set permissions
            execute_command(ssh_client, "chown www-data:www-data /var/www/html/gunicorn.conf.py")
            execute_command(ssh_client, "chmod 644 /var/www/html/gunicorn.conf.py")
        else:
            print(f"❌ Failed to create configuration: {error}")
            return False
        
        # 4. Update systemd service file
        print("\n4. 📄 Updating systemd service file...")
        
        service_content = '''[Unit]
Description=Gunicorn daemon for TCG Sync Application
After=network.target
Wants=network.target
StartLimitIntervalSec=0

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/html
Environment=PATH=/var/www/html/venv/bin
Environment=PYTHONPATH=/var/www/html
ExecStart=/var/www/html/venv/bin/gunicorn --config gunicorn.conf.py wsgi:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=15
PrivateTmp=true
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target
'''
        
        service_command = f'cat > /etc/systemd/system/gunicorn.service << \'EOF\'\n{service_content}\nEOF'
        output, error, code = execute_command(ssh_client, service_command)
        
        if code == 0:
            print("✅ Updated systemd service file")
        else:
            print(f"❌ Failed to update service file: {error}")
            return False
        
        # 5. Update nginx configuration to use Unix socket
        print("\n5. 🔄 Updating nginx configuration to use Unix socket...")
        
        # Check if nginx is installed
        output, error, code = execute_command(ssh_client, "which nginx")
        
        if output:
            # Find nginx configuration files
            output, error, code = execute_command(ssh_client, "find /etc/nginx/sites-available -type f | xargs grep -l 'proxy_pass' 2>/dev/null")
            
            if output:
                print(f"Found nginx configuration files: {output}")
                
                # Update each file
                for config_file in output.split('\n'):
                    if config_file.strip():
                        # Backup the file
                        execute_command(ssh_client, f"cp {config_file} {config_file}.bak.$(date +%Y%m%d%H%M%S)")
                        
                        # Check if it's using proxy_pass
                        output, error, code = execute_command(ssh_client, f"grep -l 'proxy_pass.*http://127.0.0.1:' {config_file}")
                        
                        if output:
                            print(f"Updating {config_file} to use Unix socket...")
                            
                            # Create updated nginx configuration
                            nginx_content = f'''
upstream app_server {{
    server unix:{socket_path} fail_timeout=0;
}}

server {{
    listen 80;
    server_name _;

    client_max_body_size 100M;
    keepalive_timeout 70;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    location / {{
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_buffering off;
        proxy_pass http://app_server;
    }}

    location /static/ {{
        alias /var/www/html/static/;
    }}
}}
'''
                            
                            # Write the new configuration
                            nginx_command = f'cat > {config_file} << \'EOF\'\n{nginx_content}\nEOF'
                            execute_command(ssh_client, nginx_command)
                            print(f"✅ Updated {config_file}")
                
                # Test nginx configuration
                output, error, code = execute_command(ssh_client, "nginx -t")
                
                if code == 0:
                    print("✅ Nginx configuration test passed")
                    
                    # Reload nginx
                    execute_command(ssh_client, "systemctl reload nginx")
                    print("✅ Reloaded nginx with new configuration")
                else:
                    print(f"❌ Nginx configuration test failed: {error}")
                    
                    # Restore backups
                    print("Restoring nginx configuration backups...")
                    for config_file in output.split('\n'):
                        if config_file.strip():
                            backup_files = execute_command(ssh_client, f"ls -t {config_file}.bak.* | head -1")[0]
                            if backup_files:
                                backup_file = backup_files.strip()
                                execute_command(ssh_client, f"cp {backup_file} {config_file}")
                                print(f"Restored {config_file} from {backup_file}")
            else:
                print("No nginx configuration files found with proxy_pass")
                
                # Create a new nginx configuration
                print("Creating new nginx configuration...")
                
                nginx_content = f'''
upstream app_server {{
    server unix:{socket_path} fail_timeout=0;
}}

server {{
    listen 80;
    server_name _;

    client_max_body_size 100M;
    keepalive_timeout 70;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    location / {{
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_buffering off;
        proxy_pass http://app_server;
    }}

    location /static/ {{
        alias /var/www/html/static/;
    }}
}}
'''
                
                nginx_command = f'cat > /etc/nginx/sites-available/default << \'EOF\'\n{nginx_content}\nEOF'
                execute_command(ssh_client, nginx_command)
                
                # Test nginx configuration
                output, error, code = execute_command(ssh_client, "nginx -t")
                
                if code == 0:
                    print("✅ Nginx configuration test passed")
                    
                    # Reload nginx
                    execute_command(ssh_client, "systemctl reload nginx")
                    print("✅ Reloaded nginx with new configuration")
                else:
                    print(f"❌ Nginx configuration test failed: {error}")
        else:
            print("Nginx not installed, skipping nginx configuration")
        
        # 6. Install gevent if not already installed
        print("\n6. 📦 Installing gevent for async workers...")
        execute_command(ssh_client, "cd /var/www/html && source venv/bin/activate && pip install gevent")
        print("✅ Gevent installed/updated")
        
        # 7. Reload systemd and start service
        print("\n7. 🔄 Reloading systemd and starting service...")
        execute_command(ssh_client, "systemctl daemon-reload")
        execute_command(ssh_client, "systemctl enable gunicorn")
        execute_command(ssh_client, "systemctl start gunicorn")
        time.sleep(5)
        
        # Check if service is running
        output, error, code = execute_command(ssh_client, "systemctl status gunicorn")
        
        if "active (running)" in output:
            print("✅ Gunicorn service is running!")
            
            # Check worker count
            output, error, code = execute_command(ssh_client, "ps aux | grep gunicorn | grep -v grep | wc -l")
            worker_count = output.strip()
            print(f"✅ Running with {worker_count} workers")
            
            # Check if socket file exists
            output, error, code = execute_command(ssh_client, f"ls -la {socket_path}")
            
            if "No such file" not in error:
                print(f"✅ Unix socket file exists: {socket_path}")
                
                # Check socket permissions
                if "www-data" in output:
                    print("✅ Socket has correct ownership")
                else:
                    print("⚠️ Socket may have incorrect ownership, fixing...")
                    execute_command(ssh_client, f"chown www-data:www-data {socket_path}")
            else:
                print(f"❌ Unix socket file does not exist: {socket_path}")
                print("Checking gunicorn logs for errors...")
                
                output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log | tail -20")
                print("Gunicorn error log:")
                print(output)
        else:
            print("❌ Service failed to start")
            print("Status output:")
            print(output[:300])
            
            # Check logs
            output, error, code = execute_command(ssh_client, "journalctl -u gunicorn --no-pager -l | tail -20")
            print("Recent logs:")
            print(output)
            
            # Check gunicorn error log
            output, error, code = execute_command(ssh_client, "cat /var/www/html/logs/app/gunicorn-error.log | tail -20")
            print("Gunicorn error log:")
            print(output)
        
        # 8. Test website through nginx
        print("\n8. 🧪 Testing website through nginx...")
        
        # Get server hostname
        output, error, code = execute_command(ssh_client, "hostname -f")
        server_hostname = output.strip()
        
        # Test with curl
        output, error, code = execute_command(ssh_client, f"curl -s -w 'Response time: %{{time_total}}s\\n' -o /dev/null http://localhost/")
        
        if code == 0:
            print(f"✅ Website response through nginx: {output}")
            
            # Run multiple tests to get average response time
            print("\n9. 📊 Running performance tests...")
            total_time = 0
            tests = 5
            
            for i in range(tests):
                output, error, code = execute_command(ssh_client, f"curl -s -w '%{{time_total}}' -o /dev/null http://localhost/")
                if code == 0:
                    try:
                        response_time = float(output.strip())
                        total_time += response_time
                        print(f"Test {i+1}: {response_time:.3f}s")
                    except:
                        print(f"Test {i+1}: Failed to parse time")
                else:
                    print(f"Test {i+1}: Failed")
                
                time.sleep(1)
            
            if total_time > 0:
                avg_time = total_time / tests
                print(f"\nAverage response time: {avg_time:.3f}s")
                
                if avg_time < 1.0:
                    print("✅ EXCELLENT performance!")
                elif avg_time < 3.0:
                    print("✅ GOOD performance")
                else:
                    print("⚠️ Performance still needs improvement")
        else:
            print(f"❌ Failed to test website through nginx: {error}")
        
        # 9. Final verification and summary
        print("\n" + "="*70)
        print("✅ UNIX SOCKET OPTIMIZATION COMPLETE!")
        print("="*70)
        print("The following optimizations have been applied:")
        print("1. ✅ Configured gunicorn to use Unix socket instead of TCP port")
        print("2. ✅ Updated nginx to use the Unix socket")
        print(f"3. ✅ Optimized worker count: {optimal_workers} workers")
        print("4. ✅ Gevent async workers with post-fork optimizations")
        print("5. ✅ RAM-based temporary directory")
        print("6. ✅ Auto-restart on failure")
        print("7. ✅ Auto-start on server reboot")
        
        print("\nTo manage the service, use these commands:")
        print("- Check status: systemctl status gunicorn")
        print("- Restart service: systemctl restart gunicorn")
        print("- View logs: journalctl -u gunicorn -f")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        ssh_client.close()
        print("\n🔌 Connection closed.")

if __name__ == "__main__":
    optimize_with_unix_socket()
