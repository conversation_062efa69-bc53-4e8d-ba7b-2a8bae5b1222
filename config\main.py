import os
import sys
import json
import time
import logging
import requests
from datetime import datetime, timedelta
import threading
import subprocess
from functools import wraps
from typing import Any, Callable, Dict, Optional, Tuple, Union

from flask import Flask, redirect, url_for, request, render_template, abort, session, Blueprint, jsonify
from flask_login import Login<PERSON><PERSON><PERSON>, current_user, login_required, login_user
from werkzeug.security import check_password_hash
from flask_cors import CORS
from mongoengine.connection import ConnectionFailure
from bson import ObjectId
from pymongo.mongo_client import MongoClient
from cache_config import init_cache

from routes.auth_routes import send_email, is_production
from routes.admin_routes import ensure_user_subscription
from config import Config
from models.user_model import User
from models.user_settings_model import UserSettings
from utils.template_filters import register_template_filters
from optimized_mongo_connection import OptimizedMongoConnection, with_mongo_retry
from routes.mobile_card_scanning_routes import mobile_card_scanning

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
werkzeug_logger = logging.getLogger('werkzeug')
werkzeug_logger.setLevel(logging.INFO)

# Get MongoDB connection instance
mongo = OptimizedMongoConnection.get_instance()

def get_location_from_ip(ip_address):
    try:
        logger.debug(f"Attempting to get location for IP: {ip_address}")
        response = requests.get(f"https://ipapi.co/{ip_address}/json/")
        data = response.json()
        location = f"{data.get('city', 'Unknown')}, {data.get('region', 'Unknown')}, {data.get('country_name', 'Unknown')}"
        logger.debug(f"Location found: {location}")
        return location
    except Exception as e:
        logger.error(f"Error getting location: {str(e)}")
        return "Location lookup failed"

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling MongoDB ObjectId and datetime objects."""
    def default(self, obj: Any) -> Any:
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class FlaskAppFactory:
    """Factory class for creating and configuring Flask application."""
    
    @staticmethod
    def create_app(config_class: type = Config) -> Flask:
        """Create and configure the Flask application."""
        app = Flask(__name__)
        FlaskAppFactory._configure_app(app, config_class)
        FlaskAppFactory._init_extensions(app)
        FlaskAppFactory._register_error_handlers(app)
        
        return app
    
    @staticmethod
    def _configure_app(app: Flask, config_class: type) -> None:
        """Configure Flask application settings."""
        app.config.from_object(config_class)
        
        # Session configuration
        session_config = {
            'PERMANENT_SESSION_LIFETIME': config_class.PERMANENT_SESSION_LIFETIME,
            'SESSION_COOKIE_SECURE': config_class.SESSION_COOKIE_SECURE,
            'SESSION_COOKIE_HTTPONLY': config_class.SESSION_COOKIE_HTTPONLY,
            'SESSION_COOKIE_SAMESITE': config_class.SESSION_COOKIE_SAMESITE,
            'SESSION_TYPE': config_class.SESSION_TYPE,
            'REMEMBER_COOKIE_DURATION': config_class.REMEMBER_COOKIE_DURATION,
            'REMEMBER_COOKIE_SECURE': config_class.REMEMBER_COOKIE_SECURE,
            'REMEMBER_COOKIE_HTTPONLY': config_class.REMEMBER_COOKIE_HTTPONLY,
            'REMEMBER_COOKIE_SAMESITE': config_class.REMEMBER_COOKIE_SAMESITE
        }
        app.config.update(session_config)
        
        # Register template filters
        register_template_filters(app)
        
        # Configure CORS
        CORS(app, resources={
            r"/*": {
                "origins": [
                    "http://localhost:5011",
                    "http://localhost:8000",
                    "http://************:8000",
                    "https://login.tcgsync.com",
                    "http://***********/24",
                    "https://*.myshopify.com",
                    "https://*.shopify.com"
                ],
                "methods": ["GET", "POST", "OPTIONS"],
                "allow_headers": ["Authorization", "Content-Type"],
                "expose_headers": ["Content-Type"]
            }
        })
        
        # Configure JSON handling for Flask 2.2.5
        from flask.json.provider import JSONProvider

        class CustomJSONProvider(JSONProvider):
            def dumps(self, obj, **kwargs):
                return json.dumps(obj, cls=CustomJSONEncoder, **kwargs)
            
            def loads(self, s, **kwargs):
                return json.loads(s, **kwargs)

        app.json = CustomJSONProvider(app)
    
    @staticmethod
    def _init_extensions(app: Flask) -> None:
        """Initialize Flask extensions."""
        # Initialize MongoDB
        mongo.init_app(app)
        
        # Initialize Login Manager
        login_manager = LoginManager()
        login_manager.init_app(app)
        login_manager.login_view = 'index'
        login_manager.session_protection = 'strong'

        # Initialize Cache
        init_cache(app)
        
        @login_manager.user_loader
        def load_user(user_id: str) -> Optional[User]:
            return User.objects(pk=user_id).first()
    
    @staticmethod
    def _register_error_handlers(app: Flask) -> None:
        """Register error handlers for the application."""
        @app.errorhandler(404)
        def page_not_found(e: Exception) -> Tuple[str, int]:
            logger.error(f"404 error: {request.url}")
            return render_template('404.html'), 404

def admin_required(f: Callable) -> Callable:
    """Decorator to require admin privileges for a route."""
    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> Any:
        if not current_user.is_authenticated or not current_user.is_admin:
            logger.error(f"Unauthorized admin access attempt by user: {getattr(current_user, 'username', 'anonymous')}")
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

@with_mongo_retry(max_retries=3, delay=1)
def connect_to_mongodb() -> MongoClient:
    """Establish connection to MongoDB with retry mechanism."""
    try:
        return mongo.get_connection()
    except ConnectionFailure as e:
        logger.error(f"Failed to connect to MongoDB server: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error when connecting to MongoDB server: {str(e)}")
        raise

def register_blueprints(app: Flask, mongo_client: MongoClient) -> None:
    """Register all application blueprints."""
    from routes.warehouse import warehouse_bp
    from routes import (
        auth_routes, profile_routes, integration_routes, orders_routes, shopify_orders_routes,
        bored_routes, misc_routes, pos_routes, dashboard_routes, card_routes, ebay_auth_routes,
        mobile_uploads_routes, ebay_orders_routes,
        cardmarket_routes, cardmarket_products_routes, cardmarket_prices_routes,
        cardmarket_order_management, board_games_routes, video_games_routes,
        ebay_policies_routes, ebay_inventory_routes, ebay_settings_routes,
        manual_inventory_routes, view_inventory_routes, seo_routes, reports_routes, onboarding_routes, 
        shopify_products, shopify_webhooks,
        shopify_update_catalog, shopify_add_inventory, shopify_cardmarket_routes,
        shopify_customers_routes, shopify_overview_routes, shopify_store_credit_routes,
        csv_routes, fbt_routes, global_sales_routes, cm_scanning_routes, admin_routes,
        tcgland_routes, cm_csv_routes, buylistrules_routes, customadd_routes,
        buylistsettings_routes, buylist_routes, shopify_export_routes, currency_routes,
        shopify_routes, activation_routes, card_scanning_routes, sports_card_routes,
        automations_routes, reporting_routes, user_settings_routes, ebay_routes,
        history_routes, card_details_routes, advanced_rules_routes, excluded_cards_routes,
        invoice_routes, cardmarket_api_routes, chatgpt_routes, shopify_autopricing,
        cardmarket_research_routes, ebay_research_routes, downloads_routes, psa_checker_routes,
        stripe_webhook_routes, contact_routes, advanced_routes, eu_purchasing_routes,
        my_orders_routes, wallet_routes, bulk_cards_routes, cards_available_routes,
        cart_routes, biggest_gainers_routes, approve_item_routes, view_order_routes,
        cardmarket_syncing_routes, mobile_card_scanning_routes
    )

    blueprints: Dict[str, Tuple[Union[Blueprint, Callable[[], Blueprint]], Optional[str]]] = {
        'auth': (lambda: auth_routes.auth_bp, None),
        'profile': (profile_routes.profile_bp, None),
        'integration': (integration_routes.integration_bp, None),
        'orders': (lambda: orders_routes.create_orders_bp(mongo_client), None),
        'bored': (bored_routes.bored_bp, None),
        'misc': (misc_routes.misc_bp, None),
        'pos': (lambda: pos_routes.create_pos_bp(), None),
        'shopify_products': (shopify_products.products_bp, None),
        'shopify_webhooks': (shopify_webhooks.shopify_webhook_bp, '/shopify'),
        'shopify_autopricing': (lambda: shopify_autopricing.create_autopricing_bp(), None),
        'shopify_update_catalog': (lambda: shopify_update_catalog.create_update_catalog_bp(mongo_client), None),
        'shopify_cardmarket': (lambda: shopify_cardmarket_routes.create_shopify_cardmarket_bp(mongo_client), None),
        'cardmarket_syncing': (lambda: cardmarket_syncing_routes.create_cardmarket_syncing_bp(mongo_client), None),
        'shopify_add_inventory': (lambda: shopify_add_inventory.create_inventory_bp(mongo_client), None),
        'shopify_customers': (lambda: shopify_customers_routes.create_customers_bp(mongo_client), None),
        'shopify_orders': (lambda: shopify_orders_routes.create_orders_bp(mongo_client), None),
        'dashboard': (dashboard_routes.dashboard_bp, None),
        'card': (lambda: card_routes.create_card_bp(), None),
        'cardmarket': (cardmarket_routes.cardmarket_bp, None),
        'cardmarket_products': (cardmarket_products_routes.cardmarket_products_bp, None),
        'cardmarket_prices': (cardmarket_prices_routes.cardmarket_prices_bp, None),
        'cardmarket_order_management': (cardmarket_order_management.cardmarket_order_management, None),
        'cardmarket_research': (cardmarket_research_routes.cardmarket_research_bp, None),
        'ebay_research': (ebay_research_routes.ebay_research_bp, None),
        'downloads': (downloads_routes.downloads_bp, None),
        'psa_checker': (psa_checker_routes.psa_checker_bp, None),
        'reports': (reports_routes.reports_bp, None),
        'onboarding': (onboarding_routes.onboarding_bp, None),
        'csv': (csv_routes.csv_bp, None),
        'fbt': (fbt_routes.fbt_bp, None),
        'shopify_overview': (shopify_overview_routes.shopify_overview_bp, None),
        'shopify_store_credit': (lambda: shopify_store_credit_routes.create_store_credit_bp(), None),
        'global_sales': (global_sales_routes.global_sales_bp, None),
        'cm_scanning': (cm_scanning_routes.cm_scanning_bp, None),
        'admin': (admin_routes.admin_bp, None),
        'tcgland': (tcgland_routes.tcgland_bp, None),
        'cm_csv': (cm_csv_routes.cm_csv_bp, '/cm_csv'),
        'buylistrules': (buylistrules_routes.buylistrules_bp, None),
        'buylist': (lambda: buylist_routes.create_buylist_bp(), '/buylist'),
        'customadd': (customadd_routes.customadd_bp, '/shopify'),
        'buylistsettings': (lambda: buylistsettings_routes.create_buylist_settings_bp(), '/buylist/settings'),
        'shopify_export': (shopify_export_routes.shopify_export_bp, None),
        'currency': (currency_routes.currency_bp, '/currency'),
        'shopify': (shopify_routes.shopify_bp, '/shopify'),
        'activation': (activation_routes.activation_bp, None),
        'card_scanning': (card_scanning_routes.card_scanning_bp, '/card_scanning'),
        'sports_card': (sports_card_routes.sports_card_scanning_bp, '/sports_card_scanning'),
        'automations': (automations_routes.automations_bp, None),
        'reporting': (reporting_routes.reporting_bp, None),
        'user_settings': (user_settings_routes.user_settings, None),
        'ebay': (ebay_routes.ebay_bp, '/ebay'),
        'history': (history_routes.history_bp, None),
        'card_details': (card_details_routes.card_details_bp, None),
        'advanced_rules': (advanced_rules_routes.advanced_rules, '/advanced_rules'),
        'excluded_cards': (excluded_cards_routes.excluded_cards, '/excluded_cards'),
        'invoice': (invoice_routes.invoices_bp, '/invoices'),
        'cardmarket_api': (cardmarket_api_routes.cardmarket_api_bp, '/cardmarket_api'),
        'chatgpt': (chatgpt_routes.chatgpt_routes, '/chatgpt'),
        'stripe_webhook': (stripe_webhook_routes.stripe_webhook_bp, None),
        'contact': (contact_routes.contact_bp, None),
        'advanced': (advanced_routes.advanced, None),
        'eu_purchasing': (eu_purchasing_routes.eu_purchasing_bp, None),
        'my_orders': (my_orders_routes.my_orders_bp, None),
        'wallet': (wallet_routes.wallet_bp, None),
        'bulk_cards': (bulk_cards_routes.bulk_cards_bp, None),
        'cards_available': (cards_available_routes.cards_available_bp, None),
        'cart': (cart_routes.cart_bp, None),
        'biggest_gainers': (biggest_gainers_routes.biggest_gainers_bp, None),
        'approve_item': (lambda: approve_item_routes.create_approve_item_bp(mongo_client), None),
        'view_order': (lambda: view_order_routes.create_view_order_bp(mongo_client), None),
        'warehouse': (warehouse_bp, None),
        'manual_inventory': (manual_inventory_routes.manual_inventory_bp, None),
        'view_inventory': (view_inventory_routes.view_inventory_bp, None),
        'seo': (seo_routes.seo_bp, None),
        'mobile_card_scanning': (mobile_card_scanning, None),
        'board_games': (board_games_routes.board_games_bp, None),
        'video_games': (video_games_routes.video_games_bp, None),
        'ebay_auth': (ebay_auth_routes.ebay_auth_bp, None),  # Allow both /api/ebay/auth and /cardmarket/api/ebay/auth
        'mobile_uploads': (mobile_uploads_routes.mobile_uploads_bp, None),
        'ebay_orders': (ebay_orders_routes.ebay_orders_bp, None),
        'ebay_policies': (ebay_policies_routes.ebay_policies_bp, None),
        'ebay_inventory': (ebay_inventory_routes.ebay_inventory_bp, None),
        'ebay_settings': (ebay_settings_routes.ebay_settings, None),
        'kiosk': (lambda: kiosk_routes.create_kiosk_bp(), None)
    }
    
    # Import kiosk routes
    from routes import kiosk_routes
    
    for name, (blueprint_or_factory, url_prefix) in blueprints.items():
        try:
            blueprint = blueprint_or_factory() if callable(blueprint_or_factory) else blueprint_or_factory
            logger.info(f"Registering blueprint '{name}' with url_prefix '{url_prefix}'")
            app.register_blueprint(blueprint, url_prefix=url_prefix)
            logger.info(f"Successfully registered blueprint '{name}'")
            
            # Log registered routes for this blueprint
            for rule in app.url_map.iter_rules():
                if rule.endpoint.startswith(f"{name}."):
                    logger.info(f"Registered route: {rule.rule} -> {rule.endpoint}")
                    
        except Exception as e:
            logger.error(f"Error registering blueprint '{name}': {str(e)}")
            raise

def create_app(config_class: type = Config) -> Flask:
    """Create and configure the Flask application."""
    app = FlaskAppFactory.create_app(config_class)
    
    @app.before_request
    def make_session_permanent() -> None:
        session.permanent = True
    
    # Initialize application with MongoDB connection
    with app.app_context():
        mongo_connection = connect_to_mongodb()
        register_blueprints(app, mongo_connection)
        
        # Add root route to show and handle login
        @app.route('/', methods=['GET', 'POST'])
        def index():
            if current_user.is_authenticated:
                return redirect(url_for('dashboard.dashboard'))
            
            if request.method == 'POST':
                # Get username and password from JSON data
                if request.is_json:
                    data = request.get_json()
                    username = data.get('username')
                    password = data.get('password')
                else:
                    username = request.form.get('username')
                    password = request.form.get('password')

                # Get user from database
                user = User.objects(username=username).first()
                ip_address = request.remote_addr
                location = get_location_from_ip(ip_address)

                if not user:
                    return jsonify({"success": False, "message": "Invalid username or password"})

                if not check_password_hash(user.password, password):
                    user.add_login_record(ip_address, location, success=False, error_message="Invalid password")
                    return jsonify({"success": False, "message": "Invalid username or password"})

                # Check if email is verified for cardmarket trial users
                if user.cardmarket_trial and not user.email_verified:
                    error_message = "Please verify your email before logging in. Check your inbox for the verification link."
                    resend_link = url_for('auth.resend_verification', username=username)
                    user.add_login_record(ip_address, location, success=False, error_message=error_message)
                    return jsonify({
                        "success": False, 
                        "message": error_message,
                        "resend_link": resend_link,
                        "show_resend": True
                    })

                # Ensure user has a valid subscription
                user = ensure_user_subscription(user)

                # Check if user needs scan limit reset (for cardmarket trial users)
                if user.cardmarket_trial:
                    now = datetime.utcnow()
                    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    
                    if not user.last_scan_reset or user.last_scan_reset < today_start:
                        user.scans_available = 50
                        user.last_scan_reset = now
                        user.save()

                login_user(user)
                
                # Record successful login
                user.add_login_record(ip_address, location, success=True)
                
                # Send login notification to admin
                login_time = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
                admin_subject = "User Login Notification"
                admin_body = f'''
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <h2 style="color: #007bff;">User Login Alert</h2>
                    <p>A user has logged into TCGSync:</p>
                    <ul>
                        <li><strong>Username:</strong> {username}</li>
                        <li><strong>Login Time:</strong> {login_time}</li>
                        <li><strong>IP Address:</strong> {ip_address}</li>
                        <li><strong>Location:</strong> {location}</li>
                        <li><strong>Environment:</strong> {'Production' if is_production() else 'Local'}</li>
                    </ul>
                </body>
                </html>
                '''
                send_email("Admin", "<EMAIL>", admin_subject, admin_body, is_html=True)
                
                return jsonify({"success": True, "redirect": url_for('dashboard.dashboard')})

            return render_template('login.html')

        # Log all registered routes after initialization
        logger.info("All registered routes:")
        for rule in app.url_map.iter_rules():
            logger.info(f"{rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    return app

# Create the application instance
app = create_app()

# For gunicorn
application = app

if __name__ == '__main__':
    # Run the application
    app.run(debug=True, host='0.0.0.0', port=8000)
